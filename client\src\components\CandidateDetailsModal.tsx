import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import CollaborationAnnotations from '@/components/CollaborationAnnotations';
import VoiceAgent from '@/components/VoiceAgent';
import { useResumeInfo, useResumeDownload, formatFileSize } from '@/hooks/useResume';
import { 
  ChevronLeft,
  ChevronRight,
  Expand,
  X,
  Monitor,
  Calendar,
  MoreHorizontal,
  MapPin,
  UserCheck,
  DollarSign,
  FileText,
  Download,
  Link,
  ExternalLink,
  MessageCircle
} from 'lucide-react';

interface ContactInfo {
  name?: string;
  email?: string;
  phone?: string;
  location?: string;
  linkedin?: string;
}

interface ResumeAnalysis {
  overall_score: number;
  match_score: number;
  experience_years: number;
  key_skills: string[];
  matched_skills: string[];
  missing_skills: string[];
  strengths: string[];
  concerns: string[];
  recommendation: 'HIRE' | 'INTERVIEW' | 'REJECT';
  detailed_feedback: string;
  interview_questions: string[];
}

interface AnalysisResult {
  contactInfo: ContactInfo;
  analysis?: ResumeAnalysis;
  extractedText: string;
  method: string;
}

interface Candidate {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  location?: string;
  skills: string[];
  experience?: string;
  experienceYears?: number;
  education?: string;
  status: 'pending_review' | 'approved_for_interview' | 'rejected' | 'hired';
  resumeUrl?: string;
  overallScore?: number;
  matchScore?: number;
  availability?: string;
  notes?: string;
  appliedDate?: string;
  lastContacted?: string;
  interviewDate?: string;
  summary?: string;
  createdAt: string;
  updatedAt: string;
  analysisResult?: ResumeAnalysis;
  aiSummary?: string;
}

interface CandidateDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidate: Candidate;
  fileName?: string;
}

const CandidateDetailsModal: React.FC<CandidateDetailsModalProps> = ({
  isOpen,
  onClose,
  candidate,
  fileName = 'resume.pdf'
}) => {
  if (!isOpen || !candidate) return null;

  // Fetch resume info
  const { data: resumeInfo, isLoading: resumeLoading } = useResumeInfo(candidate.id);
  const { downloadResume } = useResumeDownload();

  const avatar = candidate.fullName ? candidate.fullName.charAt(0).toUpperCase() : 'C';
  const matchScore = candidate.matchScore || candidate.overallScore || 0;
  const experienceYears = candidate.experienceYears || 0;

  const handleDownloadResume = () => {
    if (resumeInfo?.resumeUrl) {
      const filename = resumeInfo.resumeUrl.split('/').pop() || 'resume.pdf';
      downloadResume(candidate.id, filename);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-200 bg-opacity-90 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 flex justify-between items-center border-b">
          <h1 className="text-xl font-bold">Candidate Details</h1>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" className="text-blue-500 hover:text-blue-600 p-0">
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>
            <Button variant="ghost" className="text-blue-500 hover:text-blue-600 p-0">
              Next
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
            <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-600 p-2">
              <Expand className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-600 p-2" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Profile Section */}
        <div className="px-6 pb-4">
          <div className="flex items-start pt-4">
            <div className="mr-4">
              <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                {avatar}
              </div>
            </div>
            <div className="flex-1">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-bold">{candidate.fullName || 'Unknown Candidate'}</h2>
                  <div className="flex items-center text-gray-500 text-sm mt-1">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span>{candidate.location || 'Location not specified'}</span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" className="flex items-center px-4 py-2 rounded-full">
                    <Monitor className="w-4 h-4 mr-2" />
                    Screen
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center px-4 py-2 rounded-full">
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule Call
                  </Button>
                  <Button variant="outline" size="sm" className="px-2 py-2 rounded-full">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="flex mt-4 space-x-2">
            <div className="flex items-center bg-gray-100 rounded-full px-4 py-2">
              <UserCheck className="w-4 h-4 text-green-500 mr-2" />
              <span className="font-semibold text-green-500">{matchScore}%</span>
              <span className="ml-1 text-gray-700">Matched</span>
            </div>
            <div className="flex items-center bg-gray-100 rounded-full px-4 py-2">
              <Calendar className="w-4 h-4 text-gray-500 mr-2" />
              <span className="text-gray-700">{experienceYears} years</span>
            </div>
            <div className="flex items-center bg-gray-100 rounded-full px-4 py-2">
              <DollarSign className="w-4 h-4 text-gray-500 mr-2" />
              <span className="text-gray-700">Market Rate</span>
            </div>
          </div>

          {/* Details Grid */}
          <div className="grid grid-cols-3 gap-4 mt-6 border-b pb-6">
            <div>
              <p className="text-gray-500 text-sm">Email</p>
              <p className="font-medium">{candidate.email || 'Not provided'}</p>
            </div>
            <div>
              <p className="text-gray-500 text-sm">Phone</p>
              <p className="font-medium">{candidate.phone || 'Not provided'}</p>
            </div>
            <div>
              <p className="text-gray-500 text-sm">Analysis Date</p>
              <p className="font-medium">{new Date().toLocaleDateString()}</p>
            </div>
          </div>

          {/* Tabs */}
          <Tabs defaultValue="application" className="mt-4">
            <TabsList className="grid w-full grid-cols-6 bg-transparent border-b border-gray-200 rounded-none h-auto p-0">
              <TabsTrigger value="application" className="px-4 py-2 text-indigo-600 border-b-2 border-indigo-600 bg-transparent rounded-none data-[state=active]:bg-transparent data-[state=active]:text-indigo-600">
                Application
              </TabsTrigger>
              <TabsTrigger value="experiences" className="px-4 py-2 text-gray-500 bg-transparent rounded-none data-[state=active]:bg-transparent data-[state=active]:text-indigo-600">
                Analysis
              </TabsTrigger>
              <TabsTrigger value="certifications" className="px-4 py-2 text-gray-500 bg-transparent rounded-none data-[state=active]:bg-transparent data-[state=active]:text-indigo-600">
                Feedback
              </TabsTrigger>
              <TabsTrigger value="calendar" className="px-4 py-2 text-gray-500 bg-transparent rounded-none data-[state=active]:bg-transparent data-[state=active]:text-indigo-600">
                Questions
              </TabsTrigger>
              <TabsTrigger value="voice-agent" className="px-4 py-2 text-gray-500 bg-transparent rounded-none data-[state=active]:bg-transparent data-[state=active]:text-indigo-600">
                Voice Agent
              </TabsTrigger>
              <TabsTrigger value="collaboration" className="px-4 py-2 text-gray-500 bg-transparent rounded-none data-[state=active]:bg-transparent data-[state=active]:text-indigo-600">
                <MessageCircle className="w-4 h-4 mr-1" />
                Comments
              </TabsTrigger>
            </TabsList>

            <TabsContent value="application" className="mt-6">
              {/* About Section */}
              <div className="mb-6">
                <h3 className="font-bold text-lg mb-3">About {candidate.fullName || 'Candidate'}</h3>
                <p className="text-gray-700">
                  {candidate.aiSummary || candidate.summary || 'No detailed feedback available.'}
                </p>
              </div>

              {/* Skills Section */}
              <div className="mb-6">
                <h3 className="font-bold text-lg mb-3">Top Skills</h3>
                <div className="flex flex-wrap gap-2">
                  {candidate.skills?.map((skill, index) => (
                    <Badge key={index} variant="secondary" className="px-4 py-2 bg-gray-100 rounded-full text-gray-700">
                      {skill}
                    </Badge>
                  )) || <p className="text-gray-500">No skills extracted</p>}
                </div>
              </div>

              {/* Resume Section */}
              <div className="mb-6">
                <h3 className="font-bold text-lg mb-3">Resume</h3>
                {resumeLoading ? (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-gray-500">Loading resume information...</p>
                  </div>
                ) : resumeInfo?.hasResume ? (
                  <div className="bg-gray-50 p-4 rounded-lg flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="bg-red-100 p-2 rounded mr-3">
                        <FileText className="w-4 h-4 text-red-500" />
                      </div>
                      <div>
                        <p className="font-medium">{resumeInfo.fileName || 'resume.pdf'}</p>
                        <p className="text-gray-500 text-sm">{formatFileSize(resumeInfo.fileSize || 0)}</p>
                      </div>
                    </div>
                    <Button 
                      variant="ghost" 
                      className="text-gray-700 flex items-center hover:bg-gray-100"
                      onClick={handleDownloadResume}
                    >
                      Download
                      <Download className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                ) : (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-gray-500">No resume file available for this candidate.</p>
                  </div>
                )}
              </div>

              {/* Experience Section */}
              <div className="mb-6">
                <h3 className="font-bold text-lg mb-3">Experience</h3>
                <p className="text-gray-700">{candidate.experience || `${candidate.experienceYears || 0} years of experience`}</p>
              </div>

              {/* Status Section */}
              <div className="mb-6">
                <h3 className="font-bold text-lg mb-3">Current Status</h3>
                <div className={`p-4 rounded-lg ${
                  candidate.status === 'approved_for_interview' ? 'bg-green-100' : 
                  candidate.status === 'rejected' ? 'bg-red-100' : 
                  'bg-yellow-100'
                }`}>
                  <p className="font-medium capitalize">{candidate.status?.replace('_', ' ') || 'pending review'}</p>
                </div>
              </div>

              {/* Recommendation Section */}
              <div className="mb-6">
                <h3 className="font-bold text-lg mb-3">Recommendation</h3>
                <div className={`p-4 rounded-lg ${
                  candidate.analysisResult?.recommendation === 'HIRE' ? 'bg-green-100' :
                  candidate.analysisResult?.recommendation === 'INTERVIEW' ? 'bg-blue-100' :
                  'bg-yellow-100'
                }`}>
                  <p className="font-medium">{candidate.analysisResult?.recommendation || 'INTERVIEW'}</p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="experiences" className="mt-6">
              <div className="space-y-4">
                <div>
                  <h3 className="font-bold text-lg mb-3">Analysis Scores</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-600">Overall Score</p>
                      <p className="text-2xl font-bold text-blue-600">{candidate.overallScore || 0}%</p>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-sm text-gray-600">Match Score</p>
                      <p className="text-2xl font-bold text-green-600">{candidate.matchScore || 0}%</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-bold text-lg mb-3">Strengths</h3>
                  <div className="space-y-2">
                    {candidate.analysisResult?.strengths?.map((strength, index) => (
                      <div key={index} className="flex items-center">
                        <UserCheck className="w-4 h-4 text-green-500 mr-2" />
                        <span className="text-gray-700">{strength}</span>
                      </div>
                    )) || <p className="text-gray-500">No strengths identified</p>}
                  </div>
                </div>

                {candidate.analysisResult?.concerns && candidate.analysisResult.concerns.length > 0 && (
                  <div>
                    <h3 className="font-bold text-lg mb-3">Areas for Improvement</h3>
                    <div className="space-y-2">
                      {candidate.analysisResult.concerns.map((concern, index) => (
                        <div key={index} className="flex items-center">
                          <X className="w-4 h-4 text-yellow-500 mr-2" />
                          <span className="text-gray-700">{concern}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="certifications" className="mt-6">
              <div>
                <h3 className="font-bold text-lg mb-3">Detailed Feedback</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-gray-700 whitespace-pre-wrap">
                    {candidate.analysisResult?.detailed_feedback || candidate.aiSummary || 'No detailed feedback available.'}
                  </p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="calendar" className="mt-6">
              <div>
                <h3 className="font-bold text-lg mb-3">Interview Questions</h3>
                <div className="space-y-3">
                  {candidate.analysisResult?.interview_questions?.map((question, index) => (
                    <div key={index} className="flex items-start">
                      <span className="flex-shrink-0 w-6 h-6 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">
                        {index + 1}
                      </span>
                      <p className="text-gray-700">{question}</p>
                    </div>
                  )) || <p className="text-gray-500">No interview questions generated</p>}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="voice-agent" className="mt-6">
              <VoiceAgent
                candidateId={candidate.id}
                candidateName={candidate.fullName || 'Candidate'}
                candidatePhone={candidate.phone}
                candidateEmail={candidate.email || 'No email provided'}
              />
            </TabsContent>

            <TabsContent value="collaboration" className="mt-6">
              <div>
                <h3 className="font-bold text-lg mb-3">Team Collaboration</h3>
                <CollaborationAnnotations 
                  entityType="candidate" 
                  entityId={candidate.id}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default CandidateDetailsModal;