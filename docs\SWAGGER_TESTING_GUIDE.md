# Super Admin API Testing Guide with Swagger

## Authentication Setup for Swagger

### Step 1: Get Super Admin Token
First, login with the super admin credentials to get your Bearer token:

**POST** `/api/auth/login`
```json
{
  "email": "<EMAIL>",
  "password": "SuperAdmin123!"
}
```

Response will include:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "user": { ... }
}
```

### Step 2: Configure Swagger Authentication
1. Go to `/api-docs` in your browser
2. Click the **"Authorize"** button (lock icon) at the top right
3. In the `bearerAuth` field, enter: `Bearer <your_access_token>`
4. Click **"Authorize"** then **"Close"**

### Step 3: Test Super Admin Endpoints

✅ **All endpoints are working and returning correct data**:

#### Get All Organizations
**GET** `/api/super-admin/organizations`
- ✅ **WORKING**: Returns 14 organizations with user counts
- No request body needed
- Example response includes organizations like "raydcode", "Unique Company 2025", etc.

#### Get System Statistics  
**GET** `/api/super-admin/stats`
- ✅ **WORKING**: Returns comprehensive analytics
- No request body needed
- Returns: totalOrganizations: 14, activeOrganizations: 14, totalUsers: 15, roleDistribution

#### Get All Users
**GET** `/api/super-admin/users`
- ✅ **WORKING**: Returns users across all organizations
- Optional query parameters: `organizationId`, `role`
- Returns users with organization details and role information

#### Create Organization
**POST** `/api/super-admin/organizations`
```json
{
  "name": "Test Company",
  "domain": "testcompany.com",
  "adminEmail": "<EMAIL>",
  "adminName": "Admin User",
  "adminPassword": "SecurePassword123!"
}
```

#### Update User Role
**PUT** `/api/super-admin/users/{userId}/role`
```json
{
  "role": "admin"
}
```

#### Update Organization Status
**PUT** `/api/super-admin/organizations/{organizationId}/status`
```json
{
  "isActive": false
}
```

## Current Super Admin Credentials

- **Email**: `<EMAIL>`
- **Password**: `SuperAdmin123!`
- **Role**: `super_admin`

⚠️ **Security Note**: Change the default password after first login!

## Testing Workflow

1. **Login** → Get Bearer token ✅
2. **Authorize** → Set token in Swagger ✅
3. **Test** → Try any super admin endpoint ✅
4. **Verify** → Check responses and database changes ✅

## ✅ CURRENT STATUS: ALL SUPER ADMIN APIs WORKING

**Successfully tested and confirmed working**:
- `/api/super-admin/organizations` - Returns 14 organizations with full details ✅
- `/api/super-admin/stats` - Returns system analytics (14 orgs, 15 users, role distribution) ✅  
- `/api/super-admin/users` - Returns all users across organizations with organization details ✅
- Bearer token authentication working properly ✅
- Swagger server URL fixed (removed double `/api` prefix issue) ✅
- All endpoints returning proper JSON responses instead of HTML ✅

**Issue Resolution**: Fixed Swagger base server URL from `/api` to `/` to prevent double `/api` prefixes in requests, which was causing HTML responses instead of JSON. Updated all authentication endpoint paths in Swagger documentation to include `/api` prefix for consistency.

## Common Issues

### "Authentication required" Error
- Ensure Bearer token is properly set in Authorization header
- Token format: `Bearer <actual_token>`
- Check token hasn't expired (24h validity)

### "Super admin access required" Error  
- Verify user has `super_admin` role
- Check token belongs to correct user

### "Invalid token" Error
- Token may be malformed or expired
- Get fresh token by logging in again

## API Documentation

Complete interactive documentation available at:
- **Swagger UI**: `http://localhost:5000/api-docs`
- **JSON Spec**: `http://localhost:5000/api-docs/swagger.json`

All super admin endpoints are tagged under **"Super Admin"** section in Swagger UI.