# Simple SSO Setup Steps

## Overview
This guide helps you set up Single Sign-On (SSO) for your organization. SSO allows your team to use their existing work accounts (like Google or Microsoft) to log into the ATS system.

## Before You Start
- You need admin access to your ATS system
- You need admin access to your organization's SSO provider (Google Workspace, Microsoft 365, etc.)
- Have your organization's domain name ready (e.g., yourcompany.com)

## Step 1: Access SSO Settings
1. Log into your ATS system as an admin
2. Click on "SSO Setup" in the left sidebar (shield icon)
3. You'll see the SSO configuration page

## Step 2: Enable SSO
1. Find the "SSO Status" section at the top
2. Toggle the "Single Sign-On" switch to ON
3. Click "Save Configuration"

## Step 3: Configure Basic Settings
1. Go to the "Settings" tab
2. Set these options:
   - **Auto-provision users**: Turn ON to automatically create accounts for new users
   - **Default Role**: Choose "Member" (safest option)
   - **Allowed Domains**: Add your company domain (e.g., yourcompany.com)
3. Click "Save Configuration"

## Step 4: Add Your SSO Provider

### For Google Workspace:
1. Go to the "Providers" tab
2. Click "Add Provider"
3. Fill in:
   - **Provider Name**: "Google Workspace"
   - **Provider Type**: "OpenID Connect"
   - **Client ID**: (get from Google Cloud Console)
   - **Client Secret**: (get from Google Cloud Console)
   - **Issuer URL**: `https://accounts.google.com`
   - **Scopes**: `openid email profile`

### For Microsoft 365:
1. Go to the "Providers" tab
2. Click "Add Provider"
3. Fill in:
   - **Provider Name**: "Microsoft 365"
   - **Provider Type**: "SAML 2.0"
   - **Client ID**: (get from Azure Portal)
   - **Client Secret**: (get from Azure Portal)
   - **Issuer URL**: `https://login.microsoftonline.com/[your-tenant-id]`

## Step 5: Configure Your SSO Provider

### Google Workspace Setup:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Go to "APIs & Services" → "Credentials"
4. Click "Create Credentials" → "OAuth 2.0 Client ID"
5. Choose "Web application"
6. Add this redirect URI: `https://your-domain.replit.app/api/sso/callback/google-workspace`
7. Copy the Client ID and Client Secret to your ATS

### Microsoft 365 Setup:
1. Go to [Azure Portal](https://portal.azure.com/)
2. Go to "Azure Active Directory" → "App registrations"
3. Click "New registration"
4. Set redirect URI: `https://your-domain.replit.app/api/sso/callback/microsoft-365`
5. Go to "Certificates & secrets" and create a new secret
6. Copy the Application ID and Client Secret to your ATS

## Step 6: Test Your Setup
1. In your ATS, find your provider in the "Providers" tab
2. Click the "Test" button
3. If successful, you'll see a green checkmark
4. If there are errors, check your configuration

## Step 7: Enable for Users
1. Toggle your provider to "Enabled"
2. Click "Save Configuration"
3. Your users can now log in using SSO

## How Users Will Log In
1. Users go to your ATS login page
2. They'll see a button like "Sign in with Google" or "Sign in with Microsoft"
3. They click it and log in with their work account
4. They're automatically logged into the ATS

## Common Issues and Solutions

### "Invalid Redirect URI"
- Check that the redirect URI in your SSO provider exactly matches the one shown in the ATS
- Common mistake: missing https:// or wrong domain

### "Client Secret Invalid"
- Generate a new client secret in your SSO provider
- Update the secret in your ATS configuration

### "User Not Found"
- Make sure the user's email domain is in your "Allowed Domains" list
- Check that "Auto-provision users" is enabled

### "Access Denied"
- User might not have access to your SSO provider
- Check user permissions in Google Workspace or Microsoft 365

## Security Tips
- Only add domains you control to "Allowed Domains"
- Set the default role to "Member" or "Viewer" for safety
- Keep your client secrets secure and rotate them regularly
- Monitor who has access to your SSO provider admin settings

## Need Help?
- Use the "Setup Guide" tab for detailed instructions
- Test your configuration using the built-in test functionality
- Contact your IT administrator if you need help with your SSO provider setup

## What's Next?
After SSO is working:
1. Inform your team about the new login method
2. Consider disabling local password authentication for security
3. Set up user groups and permissions as needed
4. Monitor SSO usage in the "SSO Users" tab