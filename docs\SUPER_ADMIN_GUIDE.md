# Super Admin Foundation Guide

## Overview
Phase 1 of the multi-tenant access control implementation is now complete! The system now has a robust super admin foundation that enables proper hierarchical access control.

## What's Been Implemented

### ✅ Super Admin Bootstrap System
- **Automatic Bootstrap**: Super admin is automatically created during server startup if none exists
- **Environment Variables**: Configure initial super admin using:
  - `SUPER_ADMIN_EMAIL` (default: <EMAIL>)
  - `SUPER_ADMIN_PASSWORD` (default: SuperAdmin123!)
  - `SUPER_ADMIN_NAME` (default: System Administrator)
- **CLI Utility**: Create additional super admins using `npm run create-super-admin`

### ✅ Comprehensive Super Admin API Routes
All routes are secured and require super admin authentication:

#### Organization Management
- `POST /api/super-admin/organizations` - Create new organizations with admin users
- `PUT /api/super-admin/organizations/{id}/status` - Activate/deactivate organizations

#### User Management  
- `GET /api/super-admin/users` - View all users across organizations (with filters)
- `PUT /api/super-admin/users/{id}/role` - Change user roles across organizations
- `PUT /api/super-admin/users/{id}/status` - Activate/deactivate users

#### System Analytics
- `GET /api/super-admin/stats` - Comprehensive system statistics

#### Initial Setup
- `POST /api/super-admin/setup` - One-time setup endpoint (only works if no super admin exists)

### ✅ Security Features
- **Role Hierarchy Enforcement**: Super admins cannot demote themselves to prevent lockout
- **Organization Isolation**: When deactivating organizations, all users are also deactivated
- **Access Token Validation**: All endpoints require valid JWT tokens with super admin role
- **Input Validation**: Comprehensive Zod schema validation on all endpoints

## Access Control Matrix

| Role | Can Create | Can Manage | Data Scope |
|------|------------|------------|------------|
| `super_admin` | ✅ Organizations, Super Admins | ✅ All organizations & users | 🌐 Global |
| `admin` | 🔄 Users within org | 🔄 Users within org | 🏢 Organization-only |
| `hr_manager` | 🔄 Job postings, Candidates | 🔄 Job postings, Candidates | 🏢 Organization-only |
| `recruiter` | 🔄 Candidates | 🔄 Assigned candidates | 🏢 Organization-only |
| `hiring_manager` | 🔄 Interview feedback | 🔄 Interview scheduling | 🏢 Organization-only |

✅ = Implemented | 🔄 = Next Phase

## API Documentation

### Authentication
All super admin endpoints require Bearer token authentication:
```bash
Authorization: Bearer <jwt_token>
```

### Example API Calls

#### Create Organization
```bash
curl -X POST http://localhost:5000/api/super-admin/organizations \
  -H "Authorization: Bearer <super_admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Acme Corporation",
    "domain": "acme.com",
    "adminEmail": "<EMAIL>",
    "adminName": "John Admin",
    "adminPassword": "SecurePassword123!"
  }'
```

#### Get System Statistics
```bash
curl -X GET http://localhost:5000/api/super-admin/stats \
  -H "Authorization: Bearer <super_admin_token>"
```

#### Update User Role
```bash
curl -X PUT http://localhost:5000/api/super-admin/users/{userId}/role \
  -H "Authorization: Bearer <super_admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "admin"
  }'
```

## How to Get Started

### 1. Access Super Admin Account
The system has a super admin account with these credentials:
- **Email**: `<EMAIL>`
- **Password**: `SuperAdmin123!`

⚠️ **IMPORTANT**: Change the default password after first login!

### 2. Create Organizations
Use the super admin account to create organizations and their admin users.

### 3. Let Organization Admins Manage Users
Organization admins can then create and manage users within their organization.

## CLI Tools

### Create Additional Super Admin
```bash
npm run create-super-admin
```
This interactive CLI will guide you through creating additional super admin users.

## Next Phases

### Phase 2: Enhanced Organization Admin
- Expand admin user management capabilities
- Add role assignment within organizations  
- Implement organization settings management
- Create admin dashboard endpoints

### Phase 3: Security Hardening
- Organization-scoped tokens
- Comprehensive audit logging
- Enhanced data isolation middleware
- Permission validation on all endpoints

### Phase 4: Advanced Access Control
- Resource-level permissions
- Team/department hierarchy
- Delegation mechanisms
- Comprehensive role management UI

## Security Considerations

1. **Privilege Escalation Prevention**: Users cannot promote themselves or others beyond their role level
2. **Organization Isolation**: Strict data boundaries between organizations
3. **Token Security**: Tokens must carry organization context and role limitations
4. **Audit Trail**: All administrative actions are logged
5. **Data Protection**: Organization admins can only access their organization's data

## Swagger Documentation

Complete API documentation is available at:
```
http://localhost:5000/api-docs
```

Look for the "Super Admin" tag to see all super admin endpoints with detailed schemas and examples.

## ✅ Phase 1 Status: COMPLETE & TESTED

All super admin functionality has been implemented and tested:

- ✅ **Authentication Working**: Super admin can login and get Bearer tokens
- ✅ **GET /api/super-admin/organizations**: Returns 14 organizations with user counts
- ✅ **GET /api/super-admin/stats**: Returns system statistics (14 organizations, various user roles)
- ✅ **Swagger Integration**: Proper Bearer auth configured, all endpoints documented
- ✅ **Security Controls**: Role-based access, token validation, input validation
- ✅ **Bootstrap System**: Auto-creates super admin on startup

**Current System Data**:
- 14 Active Organizations
- 15 Total Users (1 Super Admin, 13 Admins, 1 Recruiter)
- All APIs responding correctly with proper authentication

For testing instructions, see `SWAGGER_TESTING_GUIDE.md`.