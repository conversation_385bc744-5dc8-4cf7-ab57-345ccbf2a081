import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface CollaborationUser {
  userId: string;
  userName: string;
  sessionId: string;
}

interface UseCollaborationProps {
  entityType: string;
  entityId: string;
  enabled?: boolean;
}

interface WebSocketMessage {
  type: string;
  payload: any;
  entityType: string;
  entityId: string;
}

export function useCollaboration({ entityType, entityId, enabled = true }: UseCollaborationProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [activeUsers, setActiveUsers] = useState<CollaborationUser[]>([]);
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { user } = useAuth();

  const connect = useCallback(() => {
    if (!user || !enabled) return;

    const token = localStorage.getItem('auth_token');
    if (!token) return;

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/collaboration?token=${token}&entityType=${entityType}&entityId=${entityId}`;
    
    const ws = new WebSocket(wsUrl);
    wsRef.current = ws;

    ws.onopen = () => {
      console.log('Collaboration WebSocket connected');
      setIsConnected(true);
      setReconnectAttempts(0);
    };

    ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        handleMessage(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    ws.onclose = (event) => {
      console.log('Collaboration WebSocket closed:', event.code, event.reason);
      setIsConnected(false);
      
      // Attempt to reconnect with exponential backoff
      if (enabled && reconnectAttempts < 5) {
        const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
        reconnectTimeoutRef.current = setTimeout(() => {
          setReconnectAttempts(prev => prev + 1);
          connect();
        }, delay);
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }, [user, entityType, entityId, enabled, reconnectAttempts]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setActiveUsers([]);
    setTypingUsers(new Set());
  }, []);

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'connection_established':
        console.log('Collaboration session established:', message.payload.sessionId);
        break;
      
      case 'user_joined':
        setActiveUsers(prev => {
          const filtered = prev.filter(u => u.userId !== message.payload.userId);
          return [...filtered, message.payload];
        });
        break;
      
      case 'user_left':
        setActiveUsers(prev => prev.filter(u => u.userId !== message.payload.userId));
        break;
      
      case 'typing_started':
        if (message.payload.userId !== user?.id) {
          setTypingUsers(prev => new Set(prev).add(message.payload.userId));
        }
        break;
      
      case 'typing_stopped':
        setTypingUsers(prev => {
          const newSet = new Set(prev);
          newSet.delete(message.payload.userId);
          return newSet;
        });
        break;
      
      case 'cursor_moved':
        // Handle cursor position updates
        // This could trigger UI updates for showing other users' cursors
        break;
      
      default:
        console.warn('Unknown collaboration message type:', message.type);
    }
  }, [user?.id]);

  // Send message to WebSocket
  const sendMessage = useCallback((type: string, payload: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({ type, payload }));
    }
  }, []);

  // Specific collaboration actions
  const startTyping = useCallback((location?: string) => {
    sendMessage('typing_started', { location });
  }, [sendMessage]);

  const stopTyping = useCallback(() => {
    sendMessage('typing_stopped', {});
  }, [sendMessage]);

  const updateCursor = useCallback((position: { x: number; y: number; selector?: string }) => {
    sendMessage('cursor_moved', { position });
  }, [sendMessage]);

  // Initialize and cleanup
  useEffect(() => {
    if (enabled) {
      connect();
    }
    
    return () => {
      disconnect();
    };
  }, [enabled, connect, disconnect]);

  // Auto-reconnect when entity changes
  useEffect(() => {
    if (isConnected) {
      disconnect();
      setTimeout(connect, 100);
    }
  }, [entityType, entityId]);

  return {
    isConnected,
    activeUsers,
    typingUsers,
    reconnectAttempts,
    sendMessage,
    startTyping,
    stopTyping,
    updateCursor,
    connect,
    disconnect,
  };
}