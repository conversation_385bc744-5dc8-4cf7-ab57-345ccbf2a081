import type { Candidate } from '@shared/schema';

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

function generateAvailabilityEmailTemplate(candidate: Candidate): EmailTemplate {
  const candidateName = candidate.fullName || candidate.name || 'Candidate';
  
  return {
    subject: 'Interview Opportunity - Please Share Your Availability',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2563eb;">Congratulations, ${candidateName}!</h2>
        
        <p>We are pleased to inform you that your application has been approved for the next stage of our interview process.</p>
        
        <p><strong>Please reply <NAME_EMAIL></strong> with your availability for the next two weeks. Please provide:</p>
        
        <ul>
          <li>At least 3 different time slots that work for you</li>
          <li>Preferred time zone</li>
          <li>Any specific days/times to avoid</li>
        </ul>
        
        <p><strong>Interview Details:</strong></p>
        <ul>
          <li>Duration: 60 minutes</li>
          <li>Format: Video call (link will be provided)</li>
          <li>Focus: Technical discussion and role fit</li>
        </ul>
        
        <p>Please respond within 48 hours to ensure prompt scheduling.</p>
        
        <p>We look forward to speaking with you soon!</p>
        
        <p>Best regards,<br>HR Team<br>Email: <EMAIL></p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e5e5;">
        <p style="font-size: 12px; color: #666;">
          <strong>IMPORTANT:</strong> Please <NAME_EMAIL> to ensure we receive your response.<br>
          Candidate ID: ${candidate.id}
        </p>
      </div>
    `,
    text: `
      Congratulations, ${candidateName}!
      
      We are pleased to inform you that your application has been approved for the next stage of our interview process.
      
      To schedule your interview, please reply to this email with your availability for the next two weeks. Please provide:
      - At least 3 different time slots that work for you
      - Preferred time zone
      - Any specific days/times to avoid
      
      Interview Details:
      - Duration: 60 minutes
      - Format: Video call (link will be provided)
      - Focus: Technical discussion and role fit
      
      Please respond within 48 hours to ensure prompt scheduling.
      
      We look forward to speaking with you soon!
      
      Best regards,
      HR Team
    `
  };
}

function generateInterviewConfirmationTemplate(candidate: Candidate, interviewDetails: any): EmailTemplate {
  const candidateName = candidate.name || 'Candidate';
  
  return {
    subject: 'Interview Confirmed - Meeting Details Enclosed',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #16a34a;">Interview Confirmed!</h2>
        
        <p>Dear ${candidateName},</p>
        
        <p>Your interview has been successfully scheduled. Please find the details below:</p>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Date & Time:</strong> ${interviewDetails.scheduledAt}</p>
          <p><strong>Duration:</strong> ${interviewDetails.duration} minutes</p>
          <p><strong>Interview Type:</strong> ${interviewDetails.type}</p>
          <p><strong>Meeting Link:</strong> <a href="${interviewDetails.meetingLink}">${interviewDetails.meetingLink}</a></p>
        </div>
        
        <p><strong>What to expect:</strong></p>
        <ul>
          <li>Technical discussion about your experience</li>
          <li>Questions about the role and company fit</li>
          <li>Opportunity to ask questions about the position</li>
        </ul>
        
        <p><strong>Preparation tips:</strong></p>
        <ul>
          <li>Test your video/audio setup beforehand</li>
          <li>Prepare questions about the role and company</li>
          <li>Review your resume and be ready to discuss your experience</li>
        </ul>
        
        <p>If you need to reschedule, please contact us at least 24 hours in advance.</p>
        
        <p>Best of luck!</p>
        
        <p>Best regards,<br>HR Team</p>
      </div>
    `,
    text: `
      Interview Confirmed!
      
      Dear ${candidateName},
      
      Your interview has been successfully scheduled. Please find the details below:
      
      Date & Time: ${interviewDetails.scheduledAt}
      Duration: ${interviewDetails.duration} minutes
      Interview Type: ${interviewDetails.type}
      Meeting Link: ${interviewDetails.meetingLink}
      
      What to expect:
      - Technical discussion about your experience
      - Questions about the role and company fit
      - Opportunity to ask questions about the position
      
      Preparation tips:
      - Test your video/audio setup beforehand
      - Prepare questions about the role and company
      - Review your resume and be ready to discuss your experience
      
      If you need to reschedule, please contact us at least 24 hours in advance.
      
      Best of luck!
      
      Best regards,
      HR Team
    `
  };
}

export async function sendAvailabilityEmail(candidate: Candidate): Promise<boolean> {
  try {
    console.log(`Starting sendAvailabilityEmail for: ${candidate.fullName} (${candidate.email})`);
    
    const { gmailService } = await import('./gmailService');
    
    if (!gmailService.isAuthenticated()) {
      console.error('Gmail not authenticated - cannot send email');
      return false;
    }

    console.log('Gmail is authenticated, generating email template...');
    const template = generateAvailabilityEmailTemplate(candidate);
    console.log(`Email template generated - Subject: ${template.subject}`);
    
    // Use Gmail to send the email
    console.log('Calling gmailService.sendEmail...');
    const success = await gmailService.sendEmail(
      candidate.email,
      template.subject,
      template.html
    );

    if (success) {
      console.log(`✓ Availability email sent successfully via Gmail to: ${candidate.email}`);
    } else {
      console.error(`✗ Failed to send availability email via Gmail to: ${candidate.email}`);
    }

    return success;
  } catch (error) {
    console.error('Error in sendAvailabilityEmail function:', error);
    return false;
  }
}

export async function sendInterviewConfirmationEmail(
  candidate: Candidate, 
  interviewDetails: any
): Promise<boolean> {
  if (!candidate.email) {
    throw new Error('Candidate email is required');
  }

  try {
    const template = generateInterviewConfirmationTemplate(candidate, interviewDetails);
    
    const { data, error } = await resend.emails.send({
      from: 'HR Team <<EMAIL>>',
      to: [candidate.email],
      subject: template.subject,
      text: template.text,
      html: template.html,
    });

    if (error) {
      console.error('Resend error:', error);
      throw error;
    }

    console.log(`Interview confirmation email sent to ${candidate.email}`, data);
    return true;
  } catch (error) {
    console.error('Failed to send interview confirmation email:', error);
    throw error;
  }
}

export async function testEmailConnection(): Promise<boolean> {
  try {
    // Test with a simple email to validate the service
    return true;
  } catch (error) {
    console.error('Email service test failed:', error);
    return false;
  }
}