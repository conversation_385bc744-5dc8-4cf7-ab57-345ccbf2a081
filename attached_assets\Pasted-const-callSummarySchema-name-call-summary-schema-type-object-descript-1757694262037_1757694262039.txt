const callSummarySchema = {
  name: "call_summary",
  schema: {
    type: "object",
    description: "Structured summary of an agent-candidate call.",
    properties: {
      summary: {
        type: "string",
        description: "Brief summary of the call (≤ 4 sentences)."
      },
      next_actions: {
        type: "object",
        description: "Key–value map of system-automatable actions. Keys are stable action codes.",
        additionalProperties: {
          type: "object",
          description: "Action payload.",
          properties: {
            description: {
              type: "string",
              description: "Human-readable description of the action."
            },
            due_time: {
              type: ["string", "null"],
              description: "Optional ISO-8601 datetime in UTC-4 (e.g., 2025-09-12T15:30:00-04:00). Null if not applicable."
            },
            assignee: {
              type: ["string", "null"],
              description: "Optional owner for this action (e.g., 'recruiter', 'hiring_manager', or a name/email)."
            },
            metadata: {
              type: ["object", "null"],
              description: "Optional extra fields your system may need.",
              additionalProperties: true
            }
          },
          required: ["description"],
          additionalProperties: false
        }
      },
      scheduled: {
        type: "boolean",
        description: "True if the agent and candidate explicitly agreed on a meeting/interview time."
      },
      scheduled_time: {
        type: ["string", "null"],
        description: "Agreed time in ISO-8601 with UTC-4 offset (e.g., 2025-09-12T15:30:00-04:00). Null if not agreed."
      },
      scheduled_evidence: {
        type: ["string", "null"],
        description: "Short quote (≤ 15 words) from the transcript confirming the scheduled time, or null."
      },
      open_questions: {
        type: "array",
        description: "Outstanding questions or info gaps (0–5).",
        items: { type: "string" }
      },
      risk_flags: {
        type: "array",
        description: "Potential risks/concerns (0–5). Empty if none.",
        items: { type: "string" }
      }
    },
    required: [
      "summary",
      "next_actions",
      "scheduled",
      "scheduled_time",
      "scheduled_evidence",
      "open_questions",
      "risk_flags"
    ],
    additionalProperties: false
  }
};
