# ElevenLabs Conversational AI Setup Guide

## Overview
This guide will help you set up ElevenLabs Conversational AI for true interactive voice conversations (not just voice messages) with your Twilio integration.

## Required Setup Steps

### 1. Create a Conversational AI Agent

1. **Go to ElevenLabs Dashboard** → **Conversational AI** → **Agents**
2. **Create New Agent** with these settings:
   ```
   Name: Sarah - HR Recruiter
   Language: English
   Voice: Rachel (or your preferred voice)
   Knowledge Base: Enable
   ```

3. **Agent Prompt Template:**
   ```
   You are <PERSON>, a professional HR recruiter conducting phone interviews. Your role is to:
   
   - Call candidates about job opportunities
   - Conduct initial screening interviews
   - Answer questions about positions and company
   - Schedule follow-up interviews
   - Maintain a warm, professional tone
   
   CONVERSATION GUIDELINES:
   - Keep responses under 30 words for phone clarity
   - Ask one question at a time
   - Be genuinely interested and engaging
   - Always provide clear next steps
   - If candidate seems uninterested, politely end the call
   
   CURRENT CALL CONTEXT:
   - You're calling about: {{job_title}} position
   - Company: {{company_name}}
   - Candidate: {{candidate_name}}
   - Purpose: {{call_purpose}}
   ```

### 2. Configure Webhooks (CRITICAL)

Based on your screenshot, you need to set up these webhooks:

#### A. Conversation Initiation Webhook
- **URL:** `https://YOUR_REPLIT_URL.replit.dev/api/elevenlabs/conversation-initiation`
- **Method:** POST
- **Purpose:** Called when Twilio call connects to start conversation

#### B. Post-Call Webhook  
- **URL:** `https://YOUR_REPLIT_URL.replit.dev/api/elevenlabs/conversation-ended`
- **Method:** POST  
- **Purpose:** Called when conversation ends to save analytics

### 3. Environment Variables

Add these to your Replit Secrets:

```
ELEVENLABS_API_KEY=your_api_key_here
ELEVENLABS_AGENT_ID=your_agent_id_here
ELEVENLABS_WORKSPACE_ID=your_workspace_id_here
```

### 4. Twilio Integration Method

The key is using **Twilio Connect + Stream** (not Play tags):

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Connect>
        <Stream url="wss://api.elevenlabs.io/v1/convai/conversation/websocket">
            <Parameter name="agent_id" value="YOUR_AGENT_ID" />
            <Parameter name="authorization" value="Bearer YOUR_API_KEY" />
            <Parameter name="conversation_id" value="CONVERSATION_ID" />
        </Stream>
    </Connect>
</Response>
```

## Implementation Plan

### Phase 1: Create Webhook Endpoints
```bash
# We need to create these API endpoints:
/api/elevenlabs/conversation-initiation
/api/elevenlabs/conversation-ended
/api/elevenlabs/websocket-handler
```

### Phase 2: Update Voice Provider
Replace current TTS approach with WebSocket streaming to ElevenLabs Conversational AI.

### Phase 3: Test Flow
1. Make call → Twilio connects to ElevenLabs WebSocket
2. ElevenLabs agent starts conversation
3. Real-time bidirectional audio streaming
4. Natural conversation until completion
5. Webhook saves call analytics

## Key Differences from Current Implementation

**BEFORE (Voice Messages):**
- Generate audio file → Play file → Hangup
- No interaction, just one-way message

**AFTER (Conversational AI):**
- Connect WebSocket → Real-time conversation → Natural ending
- Full bidirectional conversation with context awareness

## Next Steps

1. **Create ElevenLabs Agent** in your dashboard
2. **Configure webhooks** with your Replit URL
3. **Get Agent ID** from dashboard
4. **Test WebSocket connection** 
5. **Update Twilio TwiML** to use Connect/Stream

Would you like me to implement the webhook endpoints and update the integration for true conversational AI?