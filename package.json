{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "jest tests/e2e", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@aws-sdk/client-connect": "^3.876.0", "@google-cloud/speech": "^7.2.0", "@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@sendgrid/mail": "^8.1.5", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@tanstack/react-query": "^5.60.5", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.10", "@types/memoizee": "^0.4.12", "@types/multer": "^1.4.13", "@types/pg": "^8.15.4", "@types/puppeteer": "^5.4.7", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@vonage/server-sdk": "^3.24.1", "@zoom/videosdk": "^2.2.12", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "dotenv": "^17.2.2", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.0", "elevenlabs-node": "^2.0.3", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.18.2", "googleapis": "^150.0.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "multer": "^2.0.2", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "openai": "^5.21.0", "openid-client": "^6.7.1", "passport": "^0.7.0", "passport-local": "^1.0.0", "pg": "^8.16.2", "postgres": "^3.4.7", "puppeteer": "^24.22.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.6.2", "recharts": "^2.15.2", "sonner": "^2.0.5", "stripe": "^18.3.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "twilio": "^5.7.3", "vaul": "^1.1.2", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.7", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.2", "@types/jest": "^30.0.0", "@types/node": "^20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/supertest": "^6.0.3", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "cross-env": "^10.0.0", "drizzle-kit": "^0.18.1", "esbuild": "^0.25.0", "jest": "^30.1.3", "postcss": "^8.5.6", "supertest": "^7.1.4", "tailwindcss": "^3.4.17", "ts-jest": "^29.4.3", "tsx": "^4.19.1", "typescript": "^5.6.3", "vite": "^7.1.5"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}