import { Router } from 'express';
import { z } from 'zod';
import { db } from '../db';
import { authUsers, organizations, jobPostings, candidates } from '@shared/schema';
import { eq, and, desc, count, sql, gte, lte } from 'drizzle-orm';
import { authenticateToken, AuthenticatedRequest } from '../auth';
import { requireAdmin } from '../middleware/organizationAccess';
import { validateOrganizationAccess, validateAdminOrganizationManagement } from '../middleware/organizationSecurity';
import { auditLoggerMiddleware, criticalActionLogger } from '../middleware/auditLogger';

const router = Router();

// Organization Settings Schema
const organizationSettingsSchema = z.object({
  allowSelfRegistration: z.boolean().optional(),
  requireApprovalForNewUsers: z.boolean().optional(),
  defaultUserRole: z.enum(['hr_manager', 'recruiter', 'hiring_manager']).optional(),
  emailDomainRestriction: z.string().optional(),
  maxUsers: z.number().min(1).max(10000).optional(),
  customBranding: z.object({
    logoUrl: z.string().url().optional(),
    primaryColor: z.string().optional(),
    companyName: z.string().optional(),
  }).optional(),
});

// Bulk User Operations Schema
const bulkUserOperationSchema = z.object({
  userIds: z.array(z.string().uuid()),
  operation: z.enum(['approve', 'reject', 'activate', 'deactivate', 'delete']),
  role: z.enum(['admin', 'hr_manager', 'recruiter', 'hiring_manager']).optional(),
});

/**
 * @swagger
 * /api/admin-dashboard/analytics:
 *   get:
 *     summary: Get organization analytics dashboard (Admin)
 *     description: Comprehensive analytics for organization admins
 *     tags: [Admin Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: period
 *         in: query
 *         description: Time period for analytics
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year]
 *           default: month
 *     responses:
 *       200:
 *         description: Organization analytics data
 *       403:
 *         description: Admin access required
 *       500:
 *         description: Internal server error
 */
router.get('/analytics', authenticateToken, requireAdmin, validateOrganizationAccess, auditLoggerMiddleware('admin_dashboard', 'view_analytics'), async (req: AuthenticatedRequest, res) => {
  try {
    const { period = 'month' } = req.query;
    const organizationId = req.user!.organizationId;

    // Calculate date range based on period
    const now = new Date();
    let startDate = new Date();
    switch (period) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'quarter':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default: // month
        startDate.setMonth(now.getMonth() - 1);
    }

    // Get user statistics
    const userStats = await db
      .select({
        total: count(),
        role: authUsers.role,
        isActive: authUsers.isActive,
        isApproved: authUsers.isApproved,
      })
      .from(authUsers)
      .where(eq(authUsers.organizationId, organizationId))
      .groupBy(authUsers.role, authUsers.isActive, authUsers.isApproved);

    // Get job posting statistics
    const jobStats = await db
      .select({
        total: count(),
        isActive: jobPostings.isActive,
      })
      .from(jobPostings)
      .where(eq(jobPostings.organizationId, organizationId))
      .groupBy(jobPostings.isActive);

    // Get candidate statistics
    const candidateStats = await db
      .select({
        total: count(),
        status: candidates.status,
      })
      .from(candidates)
      .where(eq(candidates.organizationId, organizationId))
      .groupBy(candidates.status);

    // Get recent activity (new users in period)
    const newUsersInPeriod = await db
      .select({ count: count() })
      .from(authUsers)
      .where(
        and(
          eq(authUsers.organizationId, organizationId),
          gte(authUsers.createdAt, startDate)
        )
      );

    // Get recent job postings
    const recentJobPostings = await db
      .select({
        id: jobPostings.id,
        title: jobPostings.title,
        isActive: jobPostings.isActive,
        createdAt: jobPostings.createdAt,
      })
      .from(jobPostings)
      .where(eq(jobPostings.organizationId, organizationId))
      .orderBy(desc(jobPostings.createdAt))
      .limit(5);

    // Process statistics
    const analytics = {
      users: {
        total: userStats.reduce((sum, stat) => sum + stat.total, 0),
        active: userStats.filter(s => s.isActive && s.isApproved).reduce((sum, stat) => sum + stat.total, 0),
        pending: userStats.filter(s => !s.isApproved).reduce((sum, stat) => sum + stat.total, 0),
        byRole: userStats.reduce((acc, stat) => {
          acc[stat.role] = (acc[stat.role] || 0) + stat.total;
          return acc;
        }, {} as Record<string, number>),
        newInPeriod: newUsersInPeriod[0]?.count || 0,
      },
      jobPostings: {
        total: jobStats.reduce((sum, stat) => sum + stat.total, 0),
        byStatus: jobStats.reduce((acc, stat) => {
          const statusKey = stat.isActive ? 'active' : 'inactive';
          acc[statusKey] = stat.total;
          return acc;
        }, {} as Record<string, number>),
        recent: recentJobPostings,
      },
      candidates: {
        total: candidateStats.reduce((sum, stat) => sum + stat.total, 0),
        byStatus: candidateStats.reduce((acc, stat) => {
          const statusKey = stat.status ?? 'unknown';
          acc[statusKey] = stat.total;
          return acc;
        }, {} as Record<string, number>),
      },
      period,
      generatedAt: new Date().toISOString(),
    };

    res.json(analytics);
  } catch (error) {
    console.error('Analytics fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch analytics' });
  }
});

/**
 * @swagger
 * /api/admin-dashboard/users/manage:
 *   get:
 *     summary: Get enhanced user management view (Admin)
 *     description: Detailed user list with management capabilities
 *     tags: [Admin Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: status
 *         in: query
 *         description: Filter by user status
 *         schema:
 *           type: string
 *           enum: [all, active, inactive, pending]
 *       - name: role
 *         in: query
 *         description: Filter by user role
 *         schema:
 *           type: string
 *           enum: [admin, hr_manager, recruiter, hiring_manager]
 *       - name: search
 *         in: query
 *         description: Search by name or email
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Enhanced user management data
 *       403:
 *         description: Admin access required
 *       500:
 *         description: Internal server error
 */
router.get('/users/manage', authenticateToken, requireAdmin, validateOrganizationAccess, auditLoggerMiddleware('admin_dashboard', 'manage_users'), async (req: AuthenticatedRequest, res) => {
  try {
    const { status, role, search } = req.query;
    const organizationId = req.user!.organizationId;

    let baseQuery = db
      .select({
        id: authUsers.id,
        email: authUsers.email,
        fullName: authUsers.fullName,
        role: authUsers.role,
        isActive: authUsers.isActive,
        isApproved: authUsers.isApproved,
        createdAt: authUsers.createdAt,
        updatedAt: authUsers.updatedAt,
        lastLogin: authUsers.lastLogin,
      })
      .from(authUsers)
      .where(eq(authUsers.organizationId, organizationId));

    // Apply filters
    const conditions = [eq(authUsers.organizationId, organizationId)];

    if (status && status !== 'all') {
      switch (status) {
        case 'active':
          conditions.push(eq(authUsers.isActive, true), eq(authUsers.isApproved, true));
          break;
        case 'inactive':
          conditions.push(eq(authUsers.isActive, false));
          break;
        case 'pending':
          conditions.push(eq(authUsers.isApproved, false));
          break;
      }
    }

    if (role) {
      conditions.push(eq(authUsers.role, role as any));
    }

    if (search) {
      conditions.push(
        sql`(${authUsers.fullName} ILIKE ${`%${search}%`} OR ${authUsers.email} ILIKE ${`%${search}%`})`
      );
    }

    const users = await db
      .select({
        id: authUsers.id,
        email: authUsers.email,
        fullName: authUsers.fullName,
        role: authUsers.role,
        isActive: authUsers.isActive,
        isApproved: authUsers.isApproved,
        createdAt: authUsers.createdAt,
        updatedAt: authUsers.updatedAt,
        lastLogin: authUsers.lastLogin,
      })
      .from(authUsers)
      .where(conditions.length === 1 ? conditions[0] : and(...conditions))
      .orderBy(desc(authUsers.createdAt));

    res.json({
      users,
      totalCount: users.length,
      filters: { status, role, search },
    });
  } catch (error) {
    console.error('User management fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch user management data' });
  }
});

/**
 * @swagger
 * /api/admin-dashboard/users/bulk-action:
 *   post:
 *     summary: Perform bulk operations on users (Admin)
 *     description: Execute bulk operations like approve, activate, or role changes
 *     tags: [Admin Dashboard]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *               operation:
 *                 type: string
 *                 enum: [approve, reject, activate, deactivate, delete]
 *               role:
 *                 type: string
 *                 enum: [admin, hr_manager, recruiter, hiring_manager]
 *     responses:
 *       200:
 *         description: Bulk operation completed
 *       400:
 *         description: Invalid request
 *       403:
 *         description: Admin access required
 *       500:
 *         description: Internal server error
 */
router.post('/users/bulk-action', authenticateToken, requireAdmin, validateAdminOrganizationManagement, criticalActionLogger('bulk_user_action', 'user'), async (req: AuthenticatedRequest, res) => {
  try {
    const { userIds, operation, role } = bulkUserOperationSchema.parse(req.body);
    const organizationId = req.user!.organizationId;
    const currentUserId = req.user!.id;

    // Prevent self-modification in bulk operations
    if (userIds.includes(currentUserId)) {
      return res.status(400).json({ error: 'Cannot perform bulk operations on your own account' });
    }

    let updateData: any = { updatedAt: new Date() };
    let message = '';

    switch (operation) {
      case 'approve':
        updateData.isApproved = true;
        message = 'Users approved successfully';
        break;
      case 'reject':
        updateData.isApproved = false;
        message = 'Users rejected successfully';
        break;
      case 'activate':
        updateData.isActive = true;
        message = 'Users activated successfully';
        break;
      case 'deactivate':
        updateData.isActive = false;
        message = 'Users deactivated successfully';
        break;
      case 'delete':
        // For delete, we'll deactivate instead of hard delete for data integrity
        updateData.isActive = false;
        updateData.isApproved = false;
        message = 'Users removed successfully';
        break;
    }

    if (role) {
      updateData.role = role;
      message = `User roles updated to ${role} successfully`;
    }

    // Perform bulk update
    const result = await db
      .update(authUsers)
      .set(updateData)
      .where(
        and(
          sql`${authUsers.id} = ANY(${userIds})`,
          eq(authUsers.organizationId, organizationId)
        )
      )
      .returning({ id: authUsers.id, email: authUsers.email });

    res.json({
      message,
      affectedUsers: result.length,
      updatedUsers: result,
    });
  } catch (error) {
    console.error('Bulk user operation error:', error);
    res.status(500).json({ error: 'Failed to perform bulk operation' });
  }
});

/**
 * @swagger
 * /api/admin-dashboard/settings:
 *   get:
 *     summary: Get organization settings (Admin)
 *     description: Retrieve current organization configuration settings
 *     tags: [Admin Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Organization settings
 *       403:
 *         description: Admin access required
 *       500:
 *         description: Internal server error
 */
router.get('/settings', authenticateToken, requireAdmin, validateOrganizationAccess, auditLoggerMiddleware('admin_dashboard', 'view_settings'), async (req: AuthenticatedRequest, res) => {
  try {
    const organizationId = req.user!.organizationId;

    const organization = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, organizationId))
      .limit(1);

    if (!organization.length) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const org = organization[0];
    
    // Default settings if not configured
    const settings = {
      allowSelfRegistration: org.allowSelfRegistration ?? true,
      requireApprovalForNewUsers: org.requireApprovalForNewUsers ?? true,
      defaultUserRole: org.defaultUserRole ?? 'recruiter',
      emailDomainRestriction: org.emailDomainRestriction ?? null,
      maxUsers: org.maxUsers ?? 100,
      customBranding: org.customBranding ?? {
        logoUrl: null,
        primaryColor: '#2563eb',
        companyName: org.name,
      },
    };

    res.json({
      organization: {
        id: org.id,
        name: org.name,
        domain: org.domain,
        isActive: org.isActive,
        createdAt: org.createdAt,
      },
      settings,
    });
  } catch (error) {
    console.error('Settings fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch organization settings' });
  }
});

/**
 * @swagger
 * /api/admin-dashboard/settings:
 *   put:
 *     summary: Update organization settings (Admin)
 *     description: Configure organization settings and preferences
 *     tags: [Admin Dashboard]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               allowSelfRegistration:
 *                 type: boolean
 *               requireApprovalForNewUsers:
 *                 type: boolean
 *               defaultUserRole:
 *                 type: string
 *                 enum: [hr_manager, recruiter, hiring_manager]
 *               emailDomainRestriction:
 *                 type: string
 *               maxUsers:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 10000
 *               customBranding:
 *                 type: object
 *                 properties:
 *                   logoUrl:
 *                     type: string
 *                     format: uri
 *                   primaryColor:
 *                     type: string
 *                   companyName:
 *                     type: string
 *     responses:
 *       200:
 *         description: Settings updated successfully
 *       400:
 *         description: Invalid settings data
 *       403:
 *         description: Admin access required
 *       500:
 *         description: Internal server error
 */
router.put('/settings', authenticateToken, requireAdmin, validateAdminOrganizationManagement, criticalActionLogger('update_organization_settings', 'organization'), async (req: AuthenticatedRequest, res) => {
  try {
    const settings = organizationSettingsSchema.parse(req.body);
    const organizationId = req.user!.organizationId;

    const updateData: any = {
      updatedAt: new Date(),
    };

    // Map settings to database fields
    if (settings.allowSelfRegistration !== undefined) {
      updateData.allowSelfRegistration = settings.allowSelfRegistration;
    }
    if (settings.requireApprovalForNewUsers !== undefined) {
      updateData.requireApprovalForNewUsers = settings.requireApprovalForNewUsers;
    }
    if (settings.defaultUserRole !== undefined) {
      updateData.defaultUserRole = settings.defaultUserRole;
    }
    if (settings.emailDomainRestriction !== undefined) {
      updateData.emailDomainRestriction = settings.emailDomainRestriction;
    }
    if (settings.maxUsers !== undefined) {
      updateData.maxUsers = settings.maxUsers;
    }
    if (settings.customBranding !== undefined) {
      updateData.customBranding = settings.customBranding;
    }

    const result = await db
      .update(organizations)
      .set(updateData)
      .where(eq(organizations.id, organizationId))
      .returning();

    if (!result.length) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    res.json({
      message: 'Organization settings updated successfully',
      settings: {
        allowSelfRegistration: result[0].allowSelfRegistration,
        requireApprovalForNewUsers: result[0].requireApprovalForNewUsers,
        defaultUserRole: result[0].defaultUserRole,
        emailDomainRestriction: result[0].emailDomainRestriction,
        maxUsers: result[0].maxUsers,
        customBranding: result[0].customBranding,
      },
    });
  } catch (error) {
    console.error('Settings update error:', error);
    res.status(500).json({ error: 'Failed to update organization settings' });
  }
});

export default router;