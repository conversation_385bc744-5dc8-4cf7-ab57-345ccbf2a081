import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { 
  Search, 
  User, 
  MapPin, 
  Briefcase, 
  Star, 
  ExternalLink, 
  Plus,
  RefreshCw,
  Filter,
  Download,
  Mail,
  Phone,
  Calendar,
  Building,
  GraduationCap,
  Award,
  Target,
  Eye,
  UserPlus,
  Zap
} from 'lucide-react';

interface CandidateProfile {
  id: string;
  name: string;
  title: string;
  company: string;
  location: string;
  experience: string;
  skills: string[];
  education: string;
  profileUrl: string;
  summary: string;
  matchScore: number;
  availability: 'active' | 'passive' | 'unknown';
  contactInfo?: {
    email?: string;
    phone?: string;
    linkedin?: string;
  };
  source: 'linkedin' | 'github' | 'indeed' | 'glassdoor' | 'internal';
}

interface JobPosting {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  location?: string;
  skillsRequired?: string[];
  experienceLevel?: string;
}

interface SearchFilters {
  location: string;
  experience: string;
  skills: string[];
  salary: string;
  availability: string;
  source: string;
}

export default function CandidateSearchAgent() {
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [selectedJobId, setSelectedJobId] = useState<string>('');
  const [customJobDescription, setCustomJobDescription] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<CandidateProfile[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    location: '',
    experience: 'any',
    skills: [],
    salary: '',
    availability: '',
    source: 'all'
  });
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    fetchJobPostings();
  }, []);

  const fetchJobPostings = async () => {
    try {
      const response = await fetch('/api/job-postings');
      if (response.ok) {
        const data = await response.json();
        setJobPostings(data);
      }
    } catch (error) {
      console.error('Failed to fetch job postings:', error);
    }
  };

  const performCandidateSearch = async () => {
    if (!selectedJobId && !customJobDescription.trim()) {
      toast({
        title: "Search criteria required",
        description: "Please select a job posting or provide a custom job description",
        variant: "destructive",
      });
      return;
    }

    setIsSearching(true);
    try {
      const selectedJob = jobPostings.find(job => job.id === selectedJobId);
      const jobDescription = selectedJob?.description || customJobDescription;

      const response = await fetch('/api/candidates/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jobDescription,
          jobTitle: selectedJob?.title || searchQuery,
          filters,
          maxResults: 50
        }),
      });

      if (response.ok) {
        const results = await response.json();
        setSearchResults(results.candidates || []);
        toast({
          title: "Search completed",
          description: `Found ${results.candidates?.length || 0} potential candidates`,
        });
      } else {
        throw new Error('Search failed');
      }
    } catch (error) {
      console.error('Search failed:', error);
      toast({
        title: "Search failed",
        description: "Unable to search for candidates. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  const addCandidateToDatabase = async (candidate: CandidateProfile) => {
    try {
      const selectedJob = jobPostings.find(job => job.id === selectedJobId);
      const response = await fetch('/api/candidates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: candidate.contactInfo?.email || `${candidate.name.replace(' ', '.').toLowerCase()}@example.com`,
          fullName: candidate.name,
          phone: candidate.contactInfo?.phone || null,
          location: candidate.location,
          linkedinUrl: candidate.contactInfo?.linkedin || candidate.profileUrl,
          currentCompany: candidate.company,
          currentPosition: candidate.title,
          skills: candidate.skills,
          experienceYears: parseInt(candidate.experience) || 0,
          status: 'pending_review',
          sourceChannel: `candidate_search_${candidate.source}`,
          sourceType: 'internal_search',
          matchScore: candidate.matchScore,
          notes: `Sourced from ${candidate.source} for ${selectedJob?.title || 'custom search'}: ${candidate.summary}`,
          appliedJobId: selectedJobId || null,
        }),
      });

      if (response.ok) {
        const savedCandidate = await response.json();
        toast({
          title: "Candidate added",
          description: `${candidate.name} has been added to ${selectedJob?.title || 'the candidate database'}`,
        });
        
        // Trigger a custom event to notify other components
        window.dispatchEvent(new CustomEvent('candidateAdded', { 
          detail: { candidateId: savedCandidate.id, jobId: selectedJobId } 
        }));
      } else {
        throw new Error('Failed to add candidate');
      }
    } catch (error) {
      console.error('Failed to add candidate:', error);
      toast({
        title: "Error",
        description: "Failed to add candidate to database",
        variant: "destructive",
      });
    }
  };

  const bulkAddCandidates = async () => {
    if (selectedCandidates.length === 0) {
      toast({
        title: "No candidates selected",
        description: "Please select candidates to add to the database",
        variant: "destructive",
      });
      return;
    }

    for (const candidateId of selectedCandidates) {
      const candidate = searchResults.find(c => c.id === candidateId);
      if (candidate) {
        await addCandidateToDatabase(candidate);
      }
    }

    setSelectedCandidates([]);
    toast({
      title: "Bulk import completed",
      description: `Added ${selectedCandidates.length} candidates to the database`,
    });
  };

  const toggleCandidateSelection = (candidateId: string) => {
    setSelectedCandidates(prev => 
      prev.includes(candidateId) 
        ? prev.filter(id => id !== candidateId)
        : [...prev, candidateId]
    );
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'linkedin': return <Building className="w-4 h-4 text-blue-600" />;
      case 'github': return <Star className="w-4 h-4 text-gray-800" />;
      case 'indeed': return <Briefcase className="w-4 h-4 text-blue-500" />;
      case 'glassdoor': return <Award className="w-4 h-4 text-green-600" />;
      default: return <User className="w-4 h-4 text-gray-500" />;
    }
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-800';
    if (score >= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-purple-900 dark:to-indigo-900">
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
            AI Candidate Search Agent
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2">
            Discover top talent from LinkedIn, GitHub, and other professional platforms
          </p>
        </div>

        {/* Search Configuration */}
        <Card className="enhanced-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Search Configuration
            </CardTitle>
            <CardDescription>
              Define your search criteria to find the perfect candidates
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Job Selection */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Job Posting</label>
              <Select value={selectedJobId} onValueChange={setSelectedJobId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a job posting or use custom description" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="custom">Custom Job Description</SelectItem>
                  {jobPostings.map((job) => (
                    <SelectItem key={job.id} value={job.id}>
                      {job.title} - {job.location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Custom Job Description */}
            {(selectedJobId === 'custom' || !selectedJobId) && (
              <div className="space-y-3">
                <label className="text-sm font-medium">Custom Job Description</label>
                <Textarea
                  placeholder="Enter job title, requirements, and description..."
                  value={customJobDescription}
                  onChange={(e) => setCustomJobDescription(e.target.value)}
                  className="min-h-[100px]"
                />
              </div>
            )}

            {/* Search Filters */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Location</label>
                <Input
                  placeholder="e.g., San Francisco, Remote"
                  value={filters.location}
                  onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Experience Level</label>
                <Select value={filters.experience} onValueChange={(value) => setFilters(prev => ({ ...prev, experience: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Any experience" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any">Any experience</SelectItem>
                    <SelectItem value="entry">Entry Level (0-2 years)</SelectItem>
                    <SelectItem value="mid">Mid Level (3-5 years)</SelectItem>
                    <SelectItem value="senior">Senior Level (6-10 years)</SelectItem>
                    <SelectItem value="lead">Lead/Principal (10+ years)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Source Platform</label>
                <Select value={filters.source} onValueChange={(value) => setFilters(prev => ({ ...prev, source: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="All platforms" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All platforms</SelectItem>
                    <SelectItem value="linkedin">LinkedIn</SelectItem>
                    <SelectItem value="github">GitHub</SelectItem>
                    <SelectItem value="indeed">Indeed</SelectItem>
                    <SelectItem value="glassdoor">Glassdoor</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Search Button */}
            <div className="flex gap-3">
              <Button 
                onClick={performCandidateSearch}
                disabled={isSearching}
                className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white"
              >
                {isSearching ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Search className="w-4 h-4 mr-2" />
                )}
                {isSearching ? 'Searching...' : 'Search Candidates'}
              </Button>

              {selectedCandidates.length > 0 && (
                <Button 
                  onClick={bulkAddCandidates}
                  variant="outline"
                  className="border-green-200 text-green-600 hover:bg-green-50"
                >
                  <UserPlus className="w-4 h-4 mr-2" />
                  Add Selected ({selectedCandidates.length})
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <Card className="enhanced-card">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Search Results ({searchResults.length} candidates)
                </div>
                <Badge variant="outline" className="text-purple-600 border-purple-200">
                  AI-Powered Matching
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {searchResults.map((candidate) => (
                  <Card key={candidate.id} className="p-4 hover:shadow-lg transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            checked={selectedCandidates.includes(candidate.id)}
                            onChange={() => toggleCandidateSelection(candidate.id)}
                            className="w-4 h-4 text-purple-600 border-gray-300 rounded"
                          />
                          {getSourceIcon(candidate.source)}
                        </div>
                        
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center justify-between">
                            <h3 className="font-semibold text-lg">{candidate.name}</h3>
                            <Badge className={`${getMatchScoreColor(candidate.matchScore)} font-medium`}>
                              {candidate.matchScore}% Match
                            </Badge>
                          </div>
                          
                          <div className="text-gray-600 dark:text-gray-300">
                            <div className="flex items-center gap-2 mb-1">
                              <Briefcase className="w-4 h-4" />
                              <span>{candidate.title} at {candidate.company}</span>
                            </div>
                            <div className="flex items-center gap-2 mb-1">
                              <MapPin className="w-4 h-4" />
                              <span>{candidate.location}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <GraduationCap className="w-4 h-4" />
                              <span>{candidate.experience} years experience</span>
                            </div>
                          </div>

                          <p className="text-sm text-gray-500 line-clamp-2">
                            {candidate.summary}
                          </p>

                          <div className="flex flex-wrap gap-1">
                            {candidate.skills.slice(0, 5).map((skill) => (
                              <Badge key={skill} variant="outline" className="text-xs">
                                {skill}
                              </Badge>
                            ))}
                            {candidate.skills.length > 5 && (
                              <Badge variant="outline" className="text-xs">
                                +{candidate.skills.length - 5} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col gap-2 ml-4">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(candidate.profileUrl, '_blank')}
                          className="text-xs"
                        >
                          <Eye className="w-3 h-3 mr-1" />
                          View Profile
                        </Button>
                        
                        <Button
                          size="sm"
                          onClick={() => addCandidateToDatabase(candidate)}
                          className="text-xs bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white"
                        >
                          <Plus className="w-3 h-3 mr-1" />
                          Add to DB
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* No Results */}
        {!isSearching && searchResults.length === 0 && (
          <Card className="p-8 text-center border-dashed border-2 border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
            <CardContent className="pt-6">
              <Search className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Ready to Find Top Talent
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Configure your search criteria above and start discovering qualified candidates from across professional platforms.
              </p>
              <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                <Zap className="w-4 h-4" />
                <span>AI-powered candidate matching and scoring</span>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}