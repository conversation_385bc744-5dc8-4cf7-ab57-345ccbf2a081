import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Clock, 
  CheckCircle, 
  Send, 
  X,
  RefreshCw,
  Trash2
} from 'lucide-react';

interface ApprovedCandidate {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  location?: string;
  status: string;
  analysisResult?: any;
  overallScore?: number;
  matchScore?: number;
  createdAt: string;
  updatedAt: string;
}

interface AvailabilityResponse {
  id: string;
  candidateId: string;
  respondedAt: string;
  status: string;
  availableSlots?: string | string[];
  selectedSlot?: string | any;
}

export default function InterviewScheduling() {
  const [candidates, setCandidates] = useState<ApprovedCandidate[]>([]);
  const [loading, setLoading] = useState(true);
  const [availabilityData, setAvailabilityData] = useState<{ [key: string]: AvailabilityResponse[] }>({});
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  const fetchCandidates = async () => {
    try {
      // Fetch both approved and interview_scheduled candidates using session authentication
      const response = await fetch('/api/candidates', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const allCandidates = await response.json();
        
        // Filter for approved_for_interview and interview_scheduled status
        const relevantCandidates = allCandidates.filter((candidate: ApprovedCandidate) => 
          candidate.status === 'approved_for_interview' || candidate.status === 'interview_scheduled'
        );
        setCandidates(relevantCandidates);
        
        // Fetch availability data for each candidate
        const availabilityPromises = relevantCandidates.map(async (candidate: ApprovedCandidate) => {
          try {
            const availResponse = await fetch(`/api/availability/${candidate.id}`, {
              credentials: 'include',
            });
            if (availResponse.ok) {
              const availData = await availResponse.json();
              return { candidateId: candidate.id, data: availData };
            }
          } catch (error) {
            console.error(`Failed to fetch availability for ${candidate.id}:`, error);
          }
          return { candidateId: candidate.id, data: [] };
        });

        const availabilityResults = await Promise.all(availabilityPromises);
        const availabilityMap: { [key: string]: AvailabilityResponse[] } = {};
        
        availabilityResults.forEach(result => {
          availabilityMap[result.candidateId] = result.data;
        });
        
        setAvailabilityData(availabilityMap);
      } else {
        console.error('Failed to fetch candidates:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching candidates:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCandidates();
  }, []);

  const sendAvailabilityRequest = async (candidate: ApprovedCandidate) => {
    try {
      const response = await fetch('/api/availability/request', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          candidateId: candidate.id,
          candidateName: candidate.fullName,
          candidateEmail: candidate.email,
        }),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: `Availability request sent to ${candidate.fullName}`,
        });
      } else {
        throw new Error('Failed to send availability request');
      }
    } catch (error) {
      console.error('Failed to send availability request:', error);
      toast({
        title: "Error",
        description: "Failed to send availability request",
        variant: "destructive",
      });
    }
  };

  const handleDeleteCandidate = async (candidateId: string, candidateName: string) => {
    if (!confirm(`Are you sure you want to remove ${candidateName} from the interview scheduling list?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/candidates/${candidateId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: `${candidateName} has been removed from the list`,
        });
        await fetchCandidates(); // Refresh the list
      } else {
        throw new Error('Failed to delete candidate');
      }
    } catch (error) {
      console.error('Failed to delete candidate:', error);
      toast({
        title: "Error",
        description: "Failed to remove candidate",
        variant: "destructive",
      });
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchCandidates();
    setIsRefreshing(false);
    toast({
      title: "Refreshed",
      description: "Candidate data has been updated",
    });
  };

  const clearAllCandidates = async () => {
    if (!confirm('Are you sure you want to clear all candidates from the interview scheduling list?')) {
      return;
    }

    try {
      const deletePromises = candidates.map(candidate => 
        (async () => {
          const { authenticatedFetch } = await import('../lib/api');
          return authenticatedFetch(`/api/candidates/${candidate.id}`, { method: 'DELETE' });
        })()
      );
      
      await Promise.all(deletePromises);
      
      toast({
        title: "Success",
        description: "All candidates have been cleared from the list",
      });
      
      await fetchCandidates();
    } catch (error) {
      console.error('Failed to clear candidates:', error);
      toast({
        title: "Error",
        description: "Failed to clear all candidates",
        variant: "destructive",
      });
    }
  };

  const convertSlotToDateTime = (slot: string): string => {
    // Convert natural language slot to ISO datetime
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    
    // Handle various date formats
    if (slot.includes('tomorrow')) {
      const tomorrow = new Date(now);
      tomorrow.setDate(now.getDate() + 1);
      return tomorrow.toISOString();
    }
    
    if (slot.includes('Monday') || slot.includes('Tuesday') || slot.includes('Wednesday') || 
        slot.includes('Thursday') || slot.includes('Friday') || slot.includes('Saturday') || slot.includes('Sunday')) {
      // For now, just return a future date
      const futureDate = new Date(now);
      futureDate.setDate(now.getDate() + 7);
      return futureDate.toISOString();
    }
    
    // Default to next week
    const nextWeek = new Date(now);
    nextWeek.setDate(now.getDate() + 7);
    return nextWeek.toISOString();
  };

  const scheduleInterview = async (candidateId: string, selectedSlot: string, notes?: string) => {
    try {
      console.log('Scheduling interview for:', { candidateId, selectedSlot, notes });
      
      const response = await fetch('/api/calendar/schedule-interview', {
        credentials: 'include',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          candidateId,
          selectedSlot,
          interviewerEmail: '<EMAIL>',
          notes: notes || 'Interview scheduled from candidate availability response',
        }),
      });

      if (response.ok) {
        const result = await response.json();
        const message = result.calendarEventCreated 
          ? "Interview scheduled successfully! Calendar event created and invitation sent."
          : "Interview scheduled successfully! (Calendar invitation pending - please check Google Calendar permissions)";
        
        toast({
          title: "Success",
          description: message,
        });
        
        // Refresh candidates data to reflect updated status
        await fetchCandidates();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to schedule interview');
      }
    } catch (error) {
      console.error('Failed to schedule interview:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to schedule interview';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Interview Scheduling</h2>
            <p className="text-gray-600 dark:text-gray-400">Loading approved candidates...</p>
          </div>
        </div>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900">
      <div className="p-4 space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Interview Scheduling
            </h2>
            <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">
              Manage interview scheduling for approved candidates
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={handleRefresh}
              variant="outline"
              disabled={isRefreshing}
              size="sm"
              className="border-blue-200 text-blue-600 hover:bg-blue-50 dark:border-blue-700 dark:text-blue-400 dark:hover:bg-blue-900/20"
            >
              <RefreshCw className={`w-3 h-3 mr-1.5 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            {candidates.length > 0 && (
              <Button
                onClick={clearAllCandidates}
                variant="outline"
                size="sm"
                className="border-red-200 text-red-600 hover:bg-red-50 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-900/20"
              >
                <Trash2 className="w-3 h-3 mr-1.5" />
                Clear All
              </Button>
            )}
          </div>
        </div>

        {candidates.length === 0 ? (
          <Card className="p-6 text-center border-dashed border-2 border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg">
            <CardContent className="pt-4">
              <Calendar className="w-12 h-12 mx-auto text-gray-400 mb-3" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No Approved Candidates
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                Start by analyzing resumes in the Resume Screening section.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {candidates.map((candidate) => (
              <Card key={candidate.id} className="overflow-hidden bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-0 shadow-lg rounded-xl" style={{
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.05)'
              }}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2 text-lg font-semibold">
                        <User className="w-4 h-4" />
                        {candidate.fullName}
                      </CardTitle>
                      <CardDescription className="mt-1 text-sm">
                        Approved on {formatDate(candidate.updatedAt)}
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 text-xs px-2 py-1">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Approved
                      </Badge>
                      {candidate.status === 'interview_scheduled' ? (
                        <Badge className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100 text-xs px-2 py-1">
                          <Calendar className="w-3 h-3 mr-1" />
                          Interview Scheduled
                        </Badge>
                      ) : availabilityData[candidate.id]?.some(a => a.status === 'received') ? (
                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100 text-xs px-2 py-1">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Response Received
                        </Badge>
                      ) : (
                        <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100 text-xs px-2 py-1">
                          <Clock className="w-3 h-3 mr-1" />
                          Awaiting Response
                        </Badge>
                      )}
                      <Button
                        onClick={() => handleDeleteCandidate(candidate.id, candidate.fullName)}
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 h-8 w-8 p-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  <div className="grid md:grid-cols-2 gap-4">
                    {/* Contact Information */}
                    <div className="space-y-2">
                      <h4 className="font-semibold text-gray-900 dark:text-white text-sm">
                        Contact Information
                      </h4>
                      <div className="space-y-1.5">
                        <div className="flex items-center gap-2 text-xs">
                          <Mail className="w-3 h-3 text-gray-500" />
                          <span>{candidate.email}</span>
                        </div>
                        {candidate.phone && (
                          <div className="flex items-center gap-2 text-xs">
                            <Phone className="w-3 h-3 text-gray-500" />
                            <span>{candidate.phone}</span>
                          </div>
                        )}
                        {candidate.location && (
                          <div className="flex items-center gap-2 text-xs">
                            <MapPin className="w-3 h-3 text-gray-500" />
                            <span>{candidate.location}</span>
                          </div>
                        )}
                        {candidate.overallScore && (
                          <div className="flex items-center gap-2 text-xs">
                            <span className="font-medium">Overall Score:</span>
                            <Badge variant="outline" className="text-xs px-1.5 py-0.5">{candidate.overallScore}/10</Badge>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Interview Status & Actions */}
                    <div className="space-y-2">
                      <h4 className="font-semibold text-gray-900 dark:text-white text-sm">
                        Interview Status
                      </h4>
                      
                      {/* Show scheduled interview confirmation */}
                      {candidate.status === 'interview_scheduled' && (
                        <div className="p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                          <div className="flex items-start gap-2">
                            <Calendar className="w-3 h-3 text-purple-600 dark:text-purple-400 mt-0.5" />
                            <div>
                              <span className="text-xs font-medium text-purple-800 dark:text-purple-200">
                                Interview Successfully Scheduled!
                              </span>
                              <p className="text-xs text-purple-700 dark:text-purple-300 mt-0.5">
                                Candidate status updated. Check Calendar Integration page for event details.
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {/* Show availability response if received and not yet scheduled */}
                      {candidate.status !== 'interview_scheduled' && availabilityData[candidate.id]?.some(a => a.status === 'received') && (
                        <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                          <div className="flex items-start gap-2 mb-1">
                            <CheckCircle className="w-3 h-3 text-green-600 dark:text-green-400 mt-0.5" />
                            <span className="text-xs font-medium text-green-800 dark:text-green-200">
                              Candidate has responded!
                            </span>
                          </div>
                          {(() => {
                            const response = availabilityData[candidate.id].find(a => a.status === 'received');
                            const selectedSlot = response?.selectedSlot ? 
                              (typeof response.selectedSlot === 'string' ? JSON.parse(response.selectedSlot) : response.selectedSlot) : null;
                            return (
                              <>
                                <p className="text-xs text-green-700 dark:text-green-300 mb-1">
                                  Response received on {new Date(response?.respondedAt || '').toLocaleDateString()}
                                </p>
                                {selectedSlot && (
                                  <div className="text-xs bg-white dark:bg-gray-800 p-2 rounded border">
                                    <span className="font-medium">Available times: </span>
                                    {selectedSlot.slots ? (
                                      <div className="mt-1 space-y-1">
                                        {selectedSlot.slots.map((slot: string, index: number) => (
                                          <div key={index} className="flex items-center justify-between text-blue-600 dark:text-blue-400 p-1 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded">
                                            <span className="text-xs">• {slot}</span>
                                            <Button
                                              onClick={() => scheduleInterview(candidate.id, slot, selectedSlot.notes)}
                                              size="sm"
                                              className="h-5 px-2 text-xs bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                                            >
                                              <Calendar className="w-3 h-3 mr-1" />
                                              Schedule
                                            </Button>
                                          </div>
                                        ))}
                                      </div>
                                    ) : (
                                      <div className="flex items-center justify-between">
                                        <span className="text-blue-600 dark:text-blue-400 text-xs">
                                          {selectedSlot.time}
                                        </span>
                                        <Button
                                          onClick={() => scheduleInterview(candidate.id, selectedSlot.time, selectedSlot.notes)}
                                          size="sm"
                                          className="h-5 px-2 text-xs bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                                        >
                                          <Calendar className="w-3 h-3 mr-1" />
                                          Schedule
                                        </Button>
                                      </div>
                                    )}
                                    {selectedSlot.notes && (
                                      <div className="mt-1 text-xs text-gray-600 dark:text-gray-400 border-t pt-1">
                                        Note: {selectedSlot.notes}
                                      </div>
                                    )}
                                  </div>
                                )}
                              </>
                            );
                          })()}
                        </div>
                      )}

                      {/* Email Status - only show if not scheduled and no response received */}
                      {candidate.status !== 'interview_scheduled' && !availabilityData[candidate.id]?.some(a => a.status === 'received') && (
                        <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                          <div className="flex items-center justify-between">
                            <div>
                              <h5 className="font-medium text-blue-900 dark:text-blue-100 text-xs">
                                Request Availability
                              </h5>
                              <p className="text-xs text-blue-700 dark:text-blue-300 mt-0.5">
                                Send an email to request interview availability
                              </p>
                            </div>
                            <Button
                              onClick={() => sendAvailabilityRequest(candidate)}
                              size="sm"
                              className="ml-4"
                            >
                              <Send className="w-4 h-4 mr-2" />
                              Send Request
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}