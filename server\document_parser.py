#!/usr/bin/env python3
import sys
import json
import base64
import io
import re
from docx import Document
import PyPDF2
import requests
import os

def extract_text_from_docx(file_content):
    """Extract text from Word document"""
    try:
        buffer = io.BytesIO(base64.b64decode(file_content))
        doc = Document(buffer)
        
        text_parts = []
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_parts.append(paragraph.text.strip())
        
        # Also extract from tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.text.strip():
                        text_parts.append(cell.text.strip())
        
        return '\n'.join(text_parts)
    except Exception as e:
        print(f"Error extracting from DOCX: {e}", file=sys.stderr)
        return ""

def extract_text_from_pdf(file_content):
    """Extract text from PDF document"""
    try:
        buffer = io.BytesIO(base64.b64decode(file_content))
        reader = PyPDF2.PdfReader(buffer)
        
        text_parts = []
        for page in reader.pages:
            text = page.extract_text()
            if text.strip():
                text_parts.append(text.strip())
        
        return '\n'.join(text_parts)
    except Exception as e:
        print(f"Error extracting from PDF: {e}", file=sys.stderr)
        return ""

def extract_contact_info(text):
    """Extract contact information using regex patterns"""
    contact_info = {
        "name": None,
        "email": None,
        "phone": None,
        "location": None,
        "linkedin": None
    }
    
    if not text:
        return contact_info
    
    # Extract email
    email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
    email_matches = re.findall(email_pattern, text)
    if email_matches:
        contact_info["email"] = email_matches[0]
    
    # Extract phone number
    phone_patterns = [
        r'\+\d{1,3}[\s\-]?\d{3,4}[\s\-]?\d{3,4}[\s\-]?\d{3,4}',
        r'\(\d{3}\)[\s\-]?\d{3}[\s\-]?\d{4}',
        r'\d{3}[\s\-]?\d{3}[\s\-]?\d{4}',
        r'\+\d{1,3}[\s\-]?\d{7,14}'
    ]
    
    for pattern in phone_patterns:
        phone_matches = re.findall(pattern, text)
        if phone_matches:
            contact_info["phone"] = phone_matches[0]
            break
    
    # Extract LinkedIn
    linkedin_pattern = r'linkedin\.com/in/[\w\-]+'
    linkedin_matches = re.findall(linkedin_pattern, text, re.IGNORECASE)
    if linkedin_matches:
        contact_info["linkedin"] = linkedin_matches[0]
    
    # Extract name (usually first line or near top)
    lines = text.split('\n')
    for line in lines[:5]:  # Check first 5 lines
        line = line.strip()
        if line and len(line.split()) <= 4 and len(line) > 2:
            # Skip lines that look like headers or contain special chars
            if not re.search(r'[:\|\@\(\)]', line) and not line.lower().startswith(('email', 'phone', 'address')):
                contact_info["name"] = line
                break
    
    # Extract location (look for city, state patterns)
    location_pattern = r'([A-Z][a-z]+,\s*[A-Z]{2})|([A-Z][a-z]+,\s*[A-Z][a-z]+)'
    location_matches = re.findall(location_pattern, text)
    if location_matches:
        location = location_matches[0]
        contact_info["location"] = location[0] if location[0] else location[1]
    
    return contact_info

def analyze_with_openai(text, openai_api_key, job_description=None):
    """Use OpenAI to extract contact information and analyze resume against job description"""
    if not openai_api_key or openai_api_key == 'none':
        return None
    
    if job_description:
        # Full analysis against job description
        prompt = f"""Analyze this resume against the job description and provide a comprehensive assessment.

Job Description:
{job_description[:1500]}

Resume Text:
{text[:2500]}

Provide analysis in this JSON format:
{{
  "contactInfo": {{
    "name": "Full Name",
    "email": "<EMAIL>", 
    "phone": "phone number",
    "location": "City, State",
    "linkedin": "linkedin URL"
  }},
  "analysis": {{
    "overall_score": 85,
    "match_score": 78,
    "experience_years": 5,
    "key_skills": ["skill1", "skill2", "skill3"],
    "matched_skills": ["skill1", "skill2"],
    "missing_skills": ["skill3", "skill4"],
    "strengths": [
      "Strong technical background with X years of experience",
      "Proven track record in relevant technologies",
      "Leadership experience managing teams"
    ],
    "concerns": [
      "Limited experience with specific requirement X",
      "No direct experience in industry Y"
    ],
    "recommendation": "HIRE/INTERVIEW/REJECT",
    "detailed_feedback": "This candidate demonstrates strong qualifications for the role. Their experience with [specific technologies/skills] aligns well with the job requirements. Key strengths include [list 2-3 specific strengths from resume]. Areas for development include [list 2-3 areas]. The candidate's background in [relevant experience] makes them a strong fit. Overall assessment: [detailed reasoning for recommendation].",
    "interview_questions": [
      "Can you walk me through your experience with [specific technology from job description]?",
      "How did you handle [specific challenge relevant to the role]?",
      "Tell me about a time you [behavioral question relevant to job requirements]?"
    ]
  }}
}}

Important: 
- For strengths, provide 3-5 specific, detailed strengths based on the resume content
- For detailed_feedback, write a comprehensive 3-4 sentence assessment explaining the match
- For interview_questions, provide 3 specific questions tailored to both the resume and job requirements
- Base all analysis on actual resume content, not generic responses"""
        max_tokens = 1000
    else:
        # Contact info only
        prompt = f"""Extract contact information from this resume text. Look for:
- Full name (usually at the top)
- Email address
- Phone number
- Location/city
- LinkedIn profile

Resume text:
{text[:2000]}

Return only JSON: {{"name":"Full Name","email":"<EMAIL>","phone":"phone number","location":"City, State","linkedin":"linkedin URL"}}"""
        max_tokens = 300

    try:
        response = requests.post(
            'https://api.openai.com/v1/chat/completions',
            headers={
                'Authorization': f'Bearer {openai_api_key}',
                'Content-Type': 'application/json',
            },
            json={
                'model': 'gpt-3.5-turbo',
                'messages': [
                    {'role': 'system', 'content': 'You are an expert HR analyst. Extract information and analyze resumes professionally. Return only valid JSON.'},
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': max_tokens,
                'temperature': 0.3
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            content = data['choices'][0]['message']['content'].strip()
            
            # Clean and extract JSON
            if content.startswith('```json'):
                content = content.replace('```json', '').replace('```', '')
            elif content.startswith('```'):
                content = content.replace('```', '')
            
            json_match = re.search(r'\{[\s\S]*\}', content)
            if json_match:
                return json.loads(json_match.group(0))
        
    except Exception as e:
        print(f"OpenAI extraction failed: {e}", file=sys.stderr)
    
    return None

def main():
    if len(sys.argv) < 5 or len(sys.argv) > 6:
        print("Usage: python document_parser.py <file_path_or_content> <file_name> <file_type> <openai_api_key> [job_description]", file=sys.stderr)
        sys.exit(1)
    
    file_input = sys.argv[1]
    file_name = sys.argv[2]
    file_type = sys.argv[3]
    openai_api_key = sys.argv[4] if sys.argv[4] != 'none' else None
    job_description = sys.argv[5] if len(sys.argv) == 6 and sys.argv[5] != 'none' else None
    
    # Check if input is a file path or direct content
    if os.path.exists(file_input):
        # Read from file
        with open(file_input, 'r') as f:
            file_content = f.read()
    else:
        # Direct content
        file_content = file_input
    
    extracted_text = ""
    
    # Extract text based on file type
    if 'wordprocessingml' in file_type or 'msword' in file_type:
        extracted_text = extract_text_from_docx(file_content)
    elif 'pdf' in file_type:
        extracted_text = extract_text_from_pdf(file_content)
    else:
        # For other file types, decode as text
        try:
            extracted_text = base64.b64decode(file_content).decode('utf-8')
        except:
            extracted_text = ""
    
    if not extracted_text:
        print(json.dumps({
            "contactInfo": {
                "name": None,
                "email": None,
                "phone": None,
                "location": None,
                "linkedin": None
            },
            "extractedText": f"Failed to extract text from {file_name}",
            "method": "Failed extraction"
        }))
        return
    
    # Try OpenAI extraction first
    ai_result = analyze_with_openai(extracted_text, openai_api_key, job_description)
    if ai_result:
        if job_description and "analysis" in ai_result:
            # Full analysis with job matching
            result = {
                "contactInfo": ai_result.get("contactInfo", {}),
                "analysis": ai_result.get("analysis", {}),
                "extractedText": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text,
                "method": "OpenAI + Python full analysis",
                "hasJobAnalysis": True
            }
        else:
            # Contact info only
            contact_info = ai_result if "name" in ai_result else ai_result.get("contactInfo", ai_result)
            result = {
                "contactInfo": {
                    "name": contact_info.get("name"),
                    "email": contact_info.get("email"),
                    "phone": contact_info.get("phone"),
                    "location": contact_info.get("location"),
                    "linkedin": contact_info.get("linkedin")
                },
                "extractedText": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text,
                "method": "OpenAI + Python parsing",
                "hasJobAnalysis": False
            }
    else:
        # Fallback to regex extraction
        contact_info = extract_contact_info(extracted_text)
        result = {
            "contactInfo": contact_info,
            "extractedText": extracted_text[:500] + "..." if len(extracted_text) > 500 else extracted_text,
            "method": "Python regex parsing",
            "hasJobAnalysis": False
        }
    
    print(json.dumps(result))

if __name__ == "__main__":
    main()