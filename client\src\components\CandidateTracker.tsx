import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Filter, 
  MapPin, 
  Calendar,
  Mail,
  Phone,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  Eye,
  UserCheck,
  UserX
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import CandidateDetailsModal from './CandidateDetailsModal';

interface Candidate {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  location?: string;
  status: string;
  overallScore?: number;
  matchScore?: number;
  experienceYears?: number;
  analysisResult?: any;
  recommendation?: string;
  createdAt: string;
  updatedAt: string;
  appliedJobId?: string;
  skills?: string[];
  currentCompany?: string;
  currentPosition?: string;
  sourceType?: 'direct_application' | 'internal_search' | 'referral' | 'headhunter' | 'job_board';
  sourceChannel?: string;
}

interface JobPosting {
  id: string;
  title: string;
  department: string;
  location: string;
  salaryRange: string;
  employmentType: string;
  skillsRequired: string[];
  experienceLevel: string;
  isActive: boolean;
}

interface FilterState {
  categories: string[];
  locations: string[];
  jobTypes: string[];
}

export default function CandidateTracker() {
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [filteredCandidates, setFilteredCandidates] = useState<Candidate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    categories: [],
    locations: [],
    jobTypes: []
  });
  const { toast } = useToast();

  const categories = ['Engineering', 'IT', 'Marketing', 'Product', 'Operations', 'Sales', 'Administrative'];
  const locations = ['Toronto, Canada', 'New York, USA', 'Lead, UK', 'India', 'Remote'];
  const jobTypes = ['Full-Time', 'Part-Time', 'Contractor'];

  useEffect(() => {
    fetchAllCandidates();
    fetchJobPostings();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [candidates, activeTab, filters]);

  const fetchAllCandidates = async () => {
    try {
      const response = await fetch('/api/candidates');
      if (response.ok) {
        const data = await response.json();
        setCandidates(data);
      } else {
        console.error('Error fetching candidates:', await response.text());
      }
    } catch (error) {
      console.error('Error fetching candidates:', error);
      toast({
        title: "Error",
        description: "Failed to load candidates",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchJobPostings = async () => {
    try {
      const response = await fetch('/api/job-postings/all');
      if (response.ok) {
        const data = await response.json();
        setJobPostings(data);
      }
    } catch (error) {
      console.error('Error fetching job postings:', error);
    }
  };

  const applyFilters = () => {
    let filtered = candidates;

    // Tab filter
    if (activeTab !== 'all') {
      const statusMap = {
        'open': ['pending_review', 'approved_for_interview'],
        'hold': ['on_hold'],
        'closed': ['rejected', 'hired'],
        'drafts': ['draft']
      };
      if (statusMap[activeTab as keyof typeof statusMap]) {
        filtered = filtered.filter(candidate => 
          statusMap[activeTab as keyof typeof statusMap].includes(candidate.status)
        );
      }
    }

    // Category filter
    if (filters.categories.length > 0) {
      filtered = filtered.filter(candidate => {
        const candidateCategory = getCandidateCategory(candidate);
        return filters.categories.includes(candidateCategory);
      });
    }

    // Location filter
    if (filters.locations.length > 0) {
      filtered = filtered.filter(candidate => 
        candidate.location && filters.locations.some(loc => 
          candidate.location?.toLowerCase().includes(loc.toLowerCase())
        )
      );
    }

    setFilteredCandidates(filtered);
  };

  const getJobTitle = (candidate: Candidate) => {
    if (!candidate.appliedJobId) return 'General Application';
    const jobPosting = jobPostings.find(job => job.id === candidate.appliedJobId);
    return jobPosting?.title || 'Unknown Position';
  };

  const getCandidateCategory = (candidate: Candidate) => {
    // First try to get category from the job posting they applied to
    if (candidate.appliedJobId) {
      const jobPosting = jobPostings.find(job => job.id === candidate.appliedJobId);
      if (jobPosting?.department) {
        return jobPosting.department;
      }
    }

    // Fallback to skills-based categorization
    if (!candidate.skills) return 'Administrative';
    const skills = candidate.skills.join(' ').toLowerCase();
    
    // Enhanced ERP/Management categorization
    if (skills.includes('erp') || skills.includes('sap') || skills.includes('oracle') || skills.includes('director') || skills.includes('management') || skills.includes('enterprise') || skills.includes('crm') || skills.includes('salesforce')) {
      return 'IT';
    } else if (skills.includes('react') || skills.includes('javascript') || skills.includes('python') || skills.includes('engineering') || skills.includes('software') || skills.includes('developer') || skills.includes('programming') || skills.includes('ai') || skills.includes('machine learning') || skills.includes('ml') || skills.includes('artificial intelligence')) {
      return 'Engineering';
    } else if (skills.includes('it') || skills.includes('information technology') || skills.includes('network') || skills.includes('system') || skills.includes('database') || skills.includes('server') || skills.includes('infrastructure') || skills.includes('technical support')) {
      return 'IT';
    } else if (skills.includes('marketing') || skills.includes('seo') || skills.includes('content') || skills.includes('social media') || skills.includes('advertising')) {
      return 'Marketing';
    } else if (skills.includes('product') || skills.includes('design') || skills.includes('ux') || skills.includes('ui') || skills.includes('user experience')) {
      return 'Product';
    } else if (skills.includes('sales') || skills.includes('business') || skills.includes('account') || skills.includes('customer relationship')) {
      return 'Sales';
    } else if (skills.includes('operations') || skills.includes('project') || skills.includes('logistics') || skills.includes('supply chain')) {
      return 'Operations';
    }
    return 'Administrative';
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending_review':
      case 'approved_for_interview':
        return {
          label: 'Open',
          className: 'inline-flex items-center px-3 py-1 rounded-full border border-[#a5b4fc] bg-[#f5f3ff] text-[#5f3dc4] text-sm font-semibold',
          dotColor: 'bg-[#5f3dc4]'
        };
      case 'on_hold':
        return {
          label: 'Hold',
          className: 'inline-flex items-center px-3 py-1 rounded-full border border-[#fde68a] bg-[#fef9c3] text-[#eab308] text-sm font-semibold',
          dotColor: 'bg-[#eab308]'
        };
      case 'rejected':
      case 'hired':
        return {
          label: 'Closed',
          className: 'inline-flex items-center px-3 py-1 rounded-full border border-[#fca5a5] bg-[#fef2f2] text-[#ef4444] text-sm font-semibold',
          dotColor: 'bg-[#ef4444]'
        };
      default:
        return {
          label: 'Draft',
          className: 'inline-flex items-center px-3 py-1 rounded-full border border-[#d1d5db] bg-[#f9fafb] text-[#6b7280] text-sm font-semibold',
          dotColor: 'bg-[#6b7280]'
        };
    }
  };

  const handleFilterChange = (filterType: keyof FilterState, value: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: checked 
        ? [...prev[filterType], value]
        : prev[filterType].filter(item => item !== value)
    }));
  };

  const getTabCount = (tabName: string) => {
    if (tabName === 'drafts') {
      return candidates.filter(c => c.status === 'draft').length;
    }
    return null;
  };

  const getSalaryRange = (candidate: Candidate) => {
    // Generate a salary range based on experience and role
    const baseMin = 60000;
    const baseMax = 80000;
    const experienceMultiplier = (candidate.experienceYears || 0) * 5000;
    
    const min = Math.round((baseMin + experienceMultiplier) / 1000);
    const max = Math.round((baseMax + experienceMultiplier + 20000) / 1000);
    
    return `$${min}K - $${max}K`;
  };

  const getSourceTypeInfo = (sourceType?: string) => {
    switch (sourceType) {
      case 'internal_search':
        return {
          label: 'Internal Search',
          className: 'inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800 text-xs font-medium',
          icon: '🔍'
        };
      case 'direct_application':
        return {
          label: 'Direct Application',
          className: 'inline-flex items-center px-2 py-1 rounded-full bg-green-100 text-green-800 text-xs font-medium',
          icon: '📝'
        };
      case 'referral':
        return {
          label: 'Referral',
          className: 'inline-flex items-center px-2 py-1 rounded-full bg-purple-100 text-purple-800 text-xs font-medium',
          icon: '👥'
        };
      case 'headhunter':
        return {
          label: 'Headhunter',
          className: 'inline-flex items-center px-2 py-1 rounded-full bg-orange-100 text-orange-800 text-xs font-medium',
          icon: '🎯'
        };
      case 'job_board':
        return {
          label: 'Job Board',
          className: 'inline-flex items-center px-2 py-1 rounded-full bg-gray-100 text-gray-800 text-xs font-medium',
          icon: '📋'
        };
      default:
        return {
          label: 'Unknown',
          className: 'inline-flex items-center px-2 py-1 rounded-full bg-gray-100 text-gray-600 text-xs font-medium',
          icon: '❓'
        };
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading candidates...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#f7f7fa] min-h-screen">
      <div className="flex min-h-screen">
        {/* Sidebar */}
        <aside className="w-[280px] bg-white border-r border-[#e5e7eb] py-6 px-6">
          <div className="flex items-center mb-6">
            <button className="flex items-center px-3 py-1.5 rounded-full border border-[#e5e7eb] text-[#6b7280] text-sm font-medium focus:outline-none">
              <Filter className="w-3 h-3 mr-1.5" />
              Filter
            </button>
          </div>
          
          <div>
            {/* Category Filter */}
            <div className="mb-4">
              <h3 className="text-[#22223b] text-sm font-semibold mb-2">Category</h3>
              <div className="flex flex-col gap-2">
                {categories.map((category) => (
                  <label key={category} className="flex items-center text-[#6b7280] text-sm font-normal cursor-pointer">
                    <Checkbox
                      checked={filters.categories.includes(category)}
                      onCheckedChange={(checked) => handleFilterChange('categories', category, checked as boolean)}
                      className="h-4 w-4 rounded border-[#e5e7eb] mr-2"
                    />
                    {category}
                  </label>
                ))}
              </div>
            </div>

            {/* Location Filter */}
            <div className="mb-4">
              <h3 className="text-[#22223b] text-sm font-semibold mb-2">Location</h3>
              <div className="flex flex-col gap-2">
                {locations.map((location) => (
                  <label key={location} className="flex items-center text-[#6b7280] text-sm font-normal cursor-pointer">
                    <Checkbox
                      checked={filters.locations.includes(location)}
                      onCheckedChange={(checked) => handleFilterChange('locations', location, checked as boolean)}
                      className="h-4 w-4 rounded border-[#e5e7eb] mr-2"
                    />
                    {location}
                  </label>
                ))}
              </div>
            </div>

            {/* Job Type Filter */}
            <div>
              <h3 className="text-[#22223b] text-sm font-semibold mb-2">Job Type</h3>
              <div className="flex flex-col gap-2">
                {jobTypes.map((jobType) => (
                  <label key={jobType} className="flex items-center text-[#6b7280] text-sm font-normal cursor-pointer">
                    <Checkbox
                      checked={filters.jobTypes.includes(jobType)}
                      onCheckedChange={(checked) => handleFilterChange('jobTypes', jobType, checked as boolean)}
                      className="h-4 w-4 rounded border-[#e5e7eb] mr-2"
                    />
                    {jobType}
                  </label>
                ))}
              </div>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 px-6 py-6 overflow-x-auto">
          {/* Tabs */}
          <div className="flex items-center border-b border-[#e5e7eb] mb-4">
            {[
              { id: 'all', label: 'All' },
              { id: 'open', label: 'Open' },
              { id: 'hold', label: 'Hold' },
              { id: 'closed', label: 'Closed' },
              { id: 'drafts', label: 'Drafts' }
            ].map((tab) => {
              const count = getTabCount(tab.id);
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-3 pb-2 text-sm font-medium focus:outline-none ${
                    activeTab === tab.id
                      ? 'text-[#5f3dc4] font-semibold border-b-2 border-[#5f3dc4]'
                      : 'text-[#6b7280]'
                  }`}
                >
                  {tab.label}
                  {count !== null && (
                    <span className="ml-1 text-xs text-[#6b7280]">({count})</span>
                  )}
                </button>
              );
            })}
          </div>

          {/* Table Header */}
          <div className="grid grid-cols-8 gap-3 px-2 py-1.5 text-[#6b7280] text-xs font-semibold">
            <div>Candidate Name</div>
            <div>Job Title</div>
            <div>Category</div>
            <div>Source</div>
            <div>Status</div>
            <div>Experience</div>
            <div>Salary Range</div>
            <div>Location</div>
          </div>

          {/* Candidate List */}
          <div className="flex flex-col gap-2 mt-2">
            {filteredCandidates.map((candidate) => {
              const statusInfo = getStatusInfo(candidate.status);
              const category = getCandidateCategory(candidate);
              const jobTitle = getJobTitle(candidate);
              const sourceInfo = getSourceTypeInfo(candidate.sourceType);
              
              return (
                <div
                  key={candidate.id}
                  className="grid grid-cols-8 gap-3 bg-white rounded-lg px-4 py-3 items-center text-[#22223b] text-sm font-medium shadow-sm cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => {
                    setSelectedCandidate(candidate);
                    setShowModal(true);
                  }}
                >
                  <div className="font-medium">{candidate.fullName}</div>
                  <div className="text-[#6b7280] font-normal text-sm">{jobTitle}</div>
                  <div className="text-[#6b7280] font-normal text-sm">{category}</div>
                  <div>
                    <span className={sourceInfo.className}>
                      <span className="mr-1">{sourceInfo.icon}</span>
                      {sourceInfo.label}
                    </span>
                  </div>
                  <div>
                    <span className={statusInfo.className}>
                      <span className={`w-1.5 h-1.5 rounded-full ${statusInfo.dotColor} mr-1.5`}></span>
                      {statusInfo.label}
                    </span>
                  </div>
                  <div className="text-[#6b7280] font-normal text-sm">
                    {candidate.experienceYears ? `${candidate.experienceYears} years` : 'Not specified'}
                  </div>
                  <div className="text-[#6b7280] font-normal text-sm">{getSalaryRange(candidate)}</div>
                  <div className="flex items-center text-[#6b7280] font-normal text-sm">
                    <MapPin className="w-3 h-3 mr-1" />
                    {candidate.location || 'Not specified'}
                  </div>
                </div>
              );
            })}
          </div>

          {filteredCandidates.length === 0 && (
            <div className="text-center py-8">
              <div className="text-[#6b7280] text-base">No candidates found</div>
              <div className="text-[#9ca3af] text-sm mt-1">Try adjusting your filters or tab selection</div>
            </div>
          )}
        </main>
      </div>

      {/* Candidate Details Modal */}
      {showModal && selectedCandidate && (
        <CandidateDetailsModal
          candidate={selectedCandidate}
          isOpen={showModal}
          onClose={() => {
            setShowModal(false);
            setSelectedCandidate(null);
          }}
          onCandidateUpdate={(updatedCandidate) => {
            setCandidates(prev => 
              prev.map(c => c.id === updatedCandidate.id ? updatedCandidate : c)
            );
          }}
        />
      )}
    </div>
  );
}