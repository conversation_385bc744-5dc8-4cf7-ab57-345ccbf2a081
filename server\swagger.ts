import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Multi-Tenant ATS API',
      version: '1.0.0',
      description: 'A comprehensive Applicant Tracking System with AI-powered features including resume screening, voice agents, and multi-tenant organization management.',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: '/',
        description: 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            email: { type: 'string', format: 'email' },
            fullName: { type: 'string' },
            role: { type: 'string', enum: ['admin', 'member', 'viewer'] },
            organizationId: { type: 'string', format: 'uuid' },
            organizationName: { type: 'string' },
            isActive: { type: 'boolean' },
            isApproved: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        Organization: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            name: { type: 'string' },
            domain: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        JobPosting: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            title: { type: 'string' },
            department: { type: 'string' },
            location: { type: 'string' },
            description: { type: 'string' },
            detailedDescription: { type: 'string' },
            responsibilities: { type: 'string' },
            preferredQualifications: { type: 'string' },
            benefits: { type: 'string' },
            companyOverview: { type: 'string' },
            workEnvironment: { type: 'string' },
            growthOpportunities: { type: 'string' },
            requirements: { type: 'string' },
            skillsRequired: { type: 'array', items: { type: 'string' } },
            experienceLevel: { type: 'string', enum: ['entry', 'mid', 'senior', 'executive'] },
            salaryRange: { type: 'string' },
            employmentType: { type: 'string', enum: ['full-time', 'part-time', 'contract', 'freelance'] },
            isRemote: { type: 'boolean' },
            status: { type: 'string', enum: ['draft', 'active', 'closed'] },
            organizationId: { type: 'string', format: 'uuid' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        Candidate: {
          type: 'object',
          properties: {
            id: { type: 'string', format: 'uuid' },
            email: { type: 'string', format: 'email' },
            fullName: { type: 'string' },
            phone: { type: 'string' },
            location: { type: 'string' },
            linkedinUrl: { type: 'string' },
            resumeUrl: { type: 'string' },
            resumeText: { type: 'string' },
            resumeFileName: { type: 'string' },
            resumeFileSize: { type: 'number' },
            skills: { type: 'array', items: { type: 'string' } },
            experienceYears: { type: 'number' },
            education: { type: 'string' },
            currentCompany: { type: 'string' },
            currentPosition: { type: 'string' },
            status: { type: 'string', enum: ['pending_review', 'approved_for_interview', 'rejected', 'dismissed'] },
            analysisResult: { type: 'object' },
            overallScore: { type: 'number' },
            matchScore: { type: 'number' },
            recommendation: { type: 'string' },
            sourceChannel: { type: 'string' },
            sourceType: { type: 'string' },
            appliedJobId: { type: 'string', format: 'uuid' },
            organizationId: { type: 'string', format: 'uuid' },
            tags: { type: 'array', items: { type: 'string' } },
            notes: { type: 'string' },
            aiSummary: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        Error: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  tags: [
    { name: 'Authentication', description: 'User authentication and authorization' },
    { name: 'Organizations', description: 'Organization management' },
    { name: 'Job Postings', description: 'Job posting management' },
    { name: 'Candidates', description: 'Candidate management and tracking' },
    { name: 'Resume', description: 'Resume parsing and analysis' },
    { name: 'Voice Agent', description: 'AI-powered voice calls and interviews' },
    { name: 'Availability', description: 'Interview scheduling and availability' },
    { name: 'Files', description: 'File upload and management' },
    { name: 'Annotations', description: 'Collaborative annotations and comments' },
    { name: 'Users', description: 'User management within organizations' },
    { name: 'User Management', description: 'Advanced user administration' },
    { name: 'Subscription', description: 'Subscription and billing management' },
    { name: 'Gmail', description: 'Gmail integration and email automation' },
    { name: 'Calendar', description: 'Calendar integration for interview scheduling' },
    { name: 'Webhooks', description: 'Webhook endpoints for external integrations' },
    { name: 'Twilio Setup', description: 'Twilio configuration and management' },
    { name: 'LinkedIn Test', description: 'LinkedIn API testing and validation' },
    { name: 'Call Summaries', description: 'AI-generated call summaries and analytics' },
    { name: 'Super Admin', description: 'Super administrator functions' },
    { name: 'SSO', description: 'Single Sign-On integration' },
    { name: 'Voice Providers', description: 'Voice service provider management' }
  ],
  apis: [
    './server/api/*.ts',
    './server/*.ts',
    './server/api/call-summaries.ts'
  ]
};

const specs = swaggerJsdoc(options);

export function setupSwagger(app: Express) {
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Multi-Tenant ATS API Documentation'
  }));
  
  // Also serve the raw spec at /api-docs/swagger.json
  app.get('/api-docs/swagger.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });
  
  console.log('📚 Swagger documentation available at /api-docs');
}

export { specs };