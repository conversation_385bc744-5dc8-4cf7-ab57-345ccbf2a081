#!/usr/bin/env tsx

// <PERSON><PERSON><PERSON> to manually clean up company.com emails from Gmail
import { gmailService } from '../services/gmailService';

async function cleanupEmails() {
  console.log('🧹 Starting manual Gmail cleanup for company.com emails...');
  
  try {
    // Wait a moment for service to initialize
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const result = await gmailService.cleanupCompanyEmails();
    
    console.log(`✅ Cleanup completed successfully!`);
    console.log(`   Messages processed: ${result.cleaned}`);
    console.log(`   Details:`);
    result.details.forEach(detail => console.log(`   - ${detail}`));
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  }
}

// Run cleanup
cleanupEmails();