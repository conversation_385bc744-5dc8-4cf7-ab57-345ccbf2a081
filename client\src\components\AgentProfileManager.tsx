import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Bot, Plus, Edit, Trash2, Settings, Mic, Brain } from 'lucide-react';
import { toast } from 'sonner';

interface AgentProfile {
  id: string;
  name: string;
  scriptVersion: string;
  rubricJson?: any;
  safetyJson?: any;
  promptTemplate?: string;
  voiceSettings?: any;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface AgentProfileManagerProps {
  profiles: AgentProfile[];
  onProfileCreated: (profile: AgentProfile) => void;
  onProfileUpdated: (profile: AgentProfile) => void;
}

const AgentProfileManager: React.FC<AgentProfileManagerProps> = ({
  profiles,
  onProfileCreated,
  onProfileUpdated
}) => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingProfile, setEditingProfile] = useState<AgentProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    scriptVersion: 'v1.0',
    promptTemplate: '',
    voiceSettings: {
      voiceId: 'sarah',
      stability: 0.5,
      similarityBoost: 0.8,
      style: 0.0,
      useSpeakerBoost: true
    }
  });

  const handleCreateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Profile name is required');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/agent-profiles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const data = await response.json();
        onProfileCreated(data.profile);
        setIsCreateDialogOpen(false);
        resetForm();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to create agent profile');
      }
    } catch (error) {
      console.error('Error creating agent profile:', error);
      toast.error('Failed to create agent profile');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editingProfile) return;

    setLoading(true);

    try {
      const response = await fetch(`/api/agent-profiles/${editingProfile.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const data = await response.json();
        onProfileUpdated(data.profile);
        setIsEditDialogOpen(false);
        setEditingProfile(null);
        resetForm();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to update agent profile');
      }
    } catch (error) {
      console.error('Error updating agent profile:', error);
      toast.error('Failed to update agent profile');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteProfile = async (profileId: string) => {
    if (!confirm('Are you sure you want to delete this agent profile?')) {
      return;
    }

    try {
      const response = await fetch(`/api/agent-profiles/${profileId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        // Remove from local state by marking as inactive
        const updatedProfile = profiles.find(p => p.id === profileId);
        if (updatedProfile) {
          onProfileUpdated({ ...updatedProfile, isActive: false });
        }
        toast.success('Agent profile deleted successfully');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to delete agent profile');
      }
    } catch (error) {
      console.error('Error deleting agent profile:', error);
      toast.error('Failed to delete agent profile');
    }
  };

  const openEditDialog = (profile: AgentProfile) => {
    setEditingProfile(profile);
    setFormData({
      name: profile.name,
      scriptVersion: profile.scriptVersion,
      promptTemplate: profile.promptTemplate || '',
      voiceSettings: profile.voiceSettings || {
        voiceId: 'sarah',
        stability: 0.5,
        similarityBoost: 0.8,
        style: 0.0,
        useSpeakerBoost: true
      }
    });
    setIsEditDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      scriptVersion: 'v1.0',
      promptTemplate: '',
      voiceSettings: {
        voiceId: 'sarah',
        stability: 0.5,
        similarityBoost: 0.8,
        style: 0.0,
        useSpeakerBoost: true
      }
    });
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('voiceSettings.')) {
      const voiceField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        voiceSettings: {
          ...prev.voiceSettings,
          [voiceField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const ProfileForm = ({ isEdit = false }: { isEdit?: boolean }) => (
    <form onSubmit={isEdit ? handleUpdateProfile : handleCreateProfile} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Profile Name *</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => handleInputChange('name', e.target.value)}
          placeholder="e.g., Technical Interview Agent"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="scriptVersion">Script Version</Label>
        <Select 
          value={formData.scriptVersion} 
          onValueChange={(value) => handleInputChange('scriptVersion', value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="v1.0">v1.0 - Basic Interview</SelectItem>
            <SelectItem value="v1.1">v1.1 - Enhanced Technical</SelectItem>
            <SelectItem value="v2.0">v2.0 - Advanced Behavioral</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="promptTemplate">Custom Prompt Template</Label>
        <Textarea
          id="promptTemplate"
          value={formData.promptTemplate}
          onChange={(e) => handleInputChange('promptTemplate', e.target.value)}
          placeholder="You are an AI interviewer conducting a professional interview..."
          rows={4}
        />
      </div>

      <div className="space-y-4">
        <Label className="flex items-center space-x-2">
          <Mic className="w-4 h-4" />
          <span>Voice Settings</span>
        </Label>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="voiceId">Voice</Label>
            <Select 
              value={formData.voiceSettings.voiceId} 
              onValueChange={(value) => handleInputChange('voiceSettings.voiceId', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sarah">Sarah (Professional)</SelectItem>
                <SelectItem value="rachel">Rachel (Friendly)</SelectItem>
                <SelectItem value="domi">Domi (Authoritative)</SelectItem>
                <SelectItem value="bella">Bella (Warm)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="stability">Stability: {formData.voiceSettings.stability}</Label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={formData.voiceSettings.stability}
              onChange={(e) => handleInputChange('voiceSettings.stability', parseFloat(e.target.value))}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="similarityBoost">Similarity: {formData.voiceSettings.similarityBoost}</Label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={formData.voiceSettings.similarityBoost}
              onChange={(e) => handleInputChange('voiceSettings.similarityBoost', parseFloat(e.target.value))}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="style">Style: {formData.voiceSettings.style}</Label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={formData.voiceSettings.style}
              onChange={(e) => handleInputChange('voiceSettings.style', parseFloat(e.target.value))}
              className="w-full"
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button 
          type="button" 
          variant="outline" 
          onClick={() => {
            if (isEdit) {
              setIsEditDialogOpen(false);
              setEditingProfile(null);
            } else {
              setIsCreateDialogOpen(false);
            }
            resetForm();
          }}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>{isEdit ? 'Updating...' : 'Creating...'}</span>
            </div>
          ) : (
            <span>{isEdit ? 'Update Profile' : 'Create Profile'}</span>
          )}
        </Button>
      </div>
    </form>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Agent Profiles</h2>
          <p className="text-gray-600">Configure AI interviewer personalities and behaviors</p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Profile
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Agent Profile</DialogTitle>
              <DialogDescription>
                Configure a new AI interviewer profile with custom settings
              </DialogDescription>
            </DialogHeader>
            <ProfileForm />
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {profiles.filter(p => p.isActive).map(profile => (
          <Card key={profile.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <Bot className="w-5 h-5 text-purple-600" />
                  <span className="text-lg">{profile.name}</span>
                </CardTitle>
                <Badge variant="secondary">{profile.scriptVersion}</Badge>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-3">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Mic className="w-4 h-4" />
                <span>Voice: {profile.voiceSettings?.voiceId || 'sarah'}</span>
              </div>
              
              {profile.promptTemplate && (
                <div className="text-sm text-gray-600">
                  <p className="line-clamp-2">{profile.promptTemplate}</p>
                </div>
              )}
              
              <div className="flex items-center justify-between pt-2">
                <span className="text-xs text-gray-500">
                  Created {new Date(profile.createdAt).toLocaleDateString()}
                </span>
                
                <div className="flex space-x-1">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => openEditDialog(profile)}
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDeleteProfile(profile.id)}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {profiles.filter(p => p.isActive).length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <Bot className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Agent Profiles</h3>
            <p className="text-gray-600 mb-4">Create your first AI interviewer profile to get started</p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Profile
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Agent Profile</DialogTitle>
            <DialogDescription>
              Update the AI interviewer profile settings
            </DialogDescription>
          </DialogHeader>
          <ProfileForm isEdit={true} />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AgentProfileManager;
