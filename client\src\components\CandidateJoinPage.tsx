import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Camera, Mic, MicOff, Video, VideoOff, Clock, User, Bot, AlertCircle, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import ZoomVideo from '@zoom/videosdk';

interface Interview {
  id: string;
  role: string;
  scheduledAt: string;
  durationMin: number;
  status: string;
  roomOrMeetingId: string;
  candidate: {
    fullName: string;
    email: string;
  };
  agentProfile?: {
    name: string;
  };
}

interface CandidateJoinPageProps {
  interviewId: string;
}

const CandidateJoinPage: React.FC<CandidateJoinPageProps> = ({ interviewId }) => {
  const [interview, setInterview] = useState<Interview | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deviceStatus, setDeviceStatus] = useState({
    camera: false,
    microphone: false,
    cameraPermission: false,
    microphonePermission: false
  });
  const [isJoining, setIsJoining] = useState(false);
  const [zoomClient, setZoomClient] = useState<any>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [stream, setStream] = useState<any>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [zoomToken, setZoomToken] = useState<string | null>(null);
  const [zoomSDKLoaded, setZoomSDKLoaded] = useState(false);

  useEffect(() => {
    console.log('🔄 CandidateJoinPage useEffect triggered for interview:', interviewId);
    loadInterview();
    checkDevicePermissions();
    loadZoomSDK();
  }, [interviewId]);

  const loadInterview = async () => {
    try {
      console.log('🔍 Loading interview for candidate:', interviewId);
      const response = await fetch(`/api/interviews-v2/candidate/${interviewId}`);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Interview data loaded:', data);
        setInterview(data.interview);
      } else {
        console.error('❌ Failed to load interview:', response.status, response.statusText);
        setError('Interview not found or access denied');
      }
    } catch (error) {
      console.error('❌ Error loading interview:', error);
      setError('Failed to load interview details');
    } finally {
      setLoading(false);
    }
  };

  const loadZoomSDK = async () => {
    try {
      console.log('📥 Initializing Zoom Video SDK via npm package...');
      console.log('🔍 Current window location:', window.location.href);
      console.log('🔍 User agent:', navigator.userAgent);

      // Create Zoom Video client using the imported package
      const client = ZoomVideo.createClient();

      // Initialize the SDK
      console.log('🔄 Initializing Zoom Video SDK client...');
      await client.init('en-US', 'CDN'); // Use CDN for WebAssembly files

      console.log('✅ Zoom Video SDK initialized successfully via npm package');
      console.log('✅ ZoomVideo object type:', typeof ZoomVideo);

      // Set global reference for compatibility with existing code
      (window as any).ZoomVideo = ZoomVideo;

      setZoomSDKLoaded(true);

    } catch (error) {
      console.error('❌ Error initializing Zoom SDK:', error);

      // Fallback: try WebAssembly mode
      try {
        console.log('🔄 Trying WebAssembly mode as fallback...');
        const client = ZoomVideo.createClient();
        await client.init('en-US', 'WebAssembly');

        console.log('✅ Zoom Video SDK initialized successfully via WebAssembly fallback');
        (window as any).ZoomVideo = ZoomVideo;
        setZoomSDKLoaded(true);

      } catch (fallbackError) {
        console.error('❌ WebAssembly fallback also failed:', fallbackError);
        toast.error('Failed to initialize video SDK. Please refresh the page and try again.');

        // Show detailed error message for debugging
        console.error('🔍 Detailed error:', {
          originalError: error.message,
          fallbackError: fallbackError.message,
          stack: error.stack,
          userAgent: navigator.userAgent,
          url: window.location.href
        });
      }
    }
  };

  const checkDevicePermissions = async () => {
    try {
      // Check camera permission
      try {
        const videoStream = await navigator.mediaDevices.getUserMedia({ video: true });
        setDeviceStatus(prev => ({ ...prev, camera: true, cameraPermission: true }));
        videoStream.getTracks().forEach(track => track.stop());
      } catch (error) {
        console.warn('Camera permission denied or not available');
        setDeviceStatus(prev => ({ ...prev, camera: false, cameraPermission: false }));
      }

      // Check microphone permission
      try {
        const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
        setDeviceStatus(prev => ({ ...prev, microphone: true, microphonePermission: true }));
        audioStream.getTracks().forEach(track => track.stop());
      } catch (error) {
        console.warn('Microphone permission denied or not available');
        setDeviceStatus(prev => ({ ...prev, microphone: false, microphonePermission: false }));
      }
    } catch (error) {
      console.error('Error checking device permissions:', error);
    }
  };

  const requestPermissions = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: true, 
        audio: true 
      });
      
      setDeviceStatus({
        camera: true,
        microphone: true,
        cameraPermission: true,
        microphonePermission: true
      });

      // Show preview
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }

      toast.success('Camera and microphone access granted');
    } catch (error) {
      console.error('Error requesting permissions:', error);
      toast.error('Please allow camera and microphone access to join the interview');
    }
  };

  const getZoomToken = async () => {
    try {
      const endpoint = `/api/zoom/candidate-token/${interviewId}`;
      console.log('🎥 Getting Zoom token for interview:', interviewId);
      console.log('🎥 Using endpoint:', endpoint);
      console.log('🎥 Full URL will be:', window.location.origin + endpoint);

      const response = await fetch(endpoint);
      console.log('🎥 Response status:', response.status);
      console.log('🎥 Response headers:', Object.fromEntries(response.headers.entries()));

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Zoom token received:', data);
        setZoomToken(data.token);
        return data.token;
      } else {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('❌ Failed to get Zoom token:', response.status, errorData);
        console.error('❌ Response URL:', response.url);
        throw new Error(errorData.error || 'Failed to get Zoom token');
      }
    } catch (error) {
      console.error('❌ Error getting Zoom token:', error);
      console.error('❌ Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      toast.error('Failed to get meeting access token: ' + error.message);
      return null;
    }
  };

  const joinInterview = async () => {
    if (!interview || !deviceStatus.cameraPermission || !deviceStatus.microphonePermission) {
      toast.error('Please allow camera and microphone access first');
      return;
    }

    if (!zoomSDKLoaded) {
      toast.error('Video SDK is still loading. Please wait a moment and try again.');
      return;
    }

    if (!ZoomVideo) {
      toast.error('Video SDK not available. Please refresh the page.');
      return;
    }

    setIsJoining(true);

    try {
      console.log('🚀 Starting interview join process...');

      // Get Zoom token
      const token = await getZoomToken();
      if (!token) {
        throw new Error('Failed to get access token');
      }

      console.log('🎥 Creating Zoom Video SDK client...');
      // Create Zoom Video SDK client using the imported package
      const client = ZoomVideo.createClient();
      setZoomClient(client);

      console.log('🔗 Setting up event listeners...');
      // Set up event listeners
      client.on('connection-change', (payload: any) => {
        console.log('🔄 Connection change:', payload);
        if (payload.state === 'Connected') {
          setIsConnected(true);
          toast.success('Connected to interview session');
          console.log('✅ Successfully connected to Zoom session');
        } else if (payload.state === 'Closed') {
          setIsConnected(false);
          toast.info('Interview session ended');
          console.log('📴 Zoom session ended');
        } else if (payload.state === 'Reconnecting') {
          toast.info('Reconnecting to session...');
          console.log('🔄 Reconnecting to Zoom session');
        }
      });

      client.on('user-added', (payload: any) => {
        console.log('👤 User joined:', payload);
        if (payload.displayName === 'InterviewBot') {
          toast.info('AI Interviewer has joined the session');
        }
      });

      client.on('user-removed', (payload: any) => {
        console.log('👤 User left:', payload);
      });

      // Join session
      console.log('🚪 Joining Zoom session:', {
        sessionName: interview.roomOrMeetingId,
        userName: interview.candidate.fullName,
        hasToken: !!token,
        tokenLength: token?.length
      });

      // Join session with detailed error handling
      try {
        await client.join(interview.roomOrMeetingId, token, interview.candidate.fullName);
        console.log('✅ Successfully joined Zoom session');
      } catch (joinError) {
        console.error('❌ Failed to join Zoom session:', joinError);
        throw new Error(`Failed to join video session: ${joinError.message || joinError}`);
      }
      
      console.log('🎥 Starting video and audio...');
      // Start video and audio
      const mediaStream = client.getMediaStream();
      setStream(mediaStream);

      await mediaStream.startVideo();
      console.log('✅ Video started');

      await mediaStream.startAudio();
      console.log('✅ Audio started');

      // Render video
      if (videoRef.current) {
        console.log('🖼️ Rendering video to canvas...');
        await mediaStream.renderVideo(videoRef.current, client.getCurrentUserInfo().userId, 1920, 1080, 0, 0, 3);
        console.log('✅ Video rendered successfully');
      }

      toast.success('Successfully joined the interview!');
      console.log('🎉 Interview join process completed successfully');

    } catch (error) {
      console.error('❌ Error joining interview:', error);

      // Provide more specific error messages
      let errorMessage = 'Failed to join interview. Please try again.';
      if (error.message.includes('token')) {
        errorMessage = 'Authentication failed. Please refresh the page and try again.';
      } else if (error.message.includes('network') || error.message.includes('connection')) {
        errorMessage = 'Network connection failed. Please check your internet and try again.';
      } else if (error.message.includes('permission')) {
        errorMessage = 'Camera/microphone permission required. Please allow access and try again.';
      }

      toast.error(errorMessage);
    } finally {
      setIsJoining(false);
    }
  };

  const leaveInterview = async () => {
    try {
      if (zoomClient && isConnected) {
        await zoomClient.leave();
      }
      setIsConnected(false);
      setZoomClient(null);
      setStream(null);
      toast.info('Left the interview session');
    } catch (error) {
      console.error('Error leaving interview:', error);
    }
  };

  const toggleVideo = async () => {
    if (stream && isConnected) {
      try {
        if (deviceStatus.camera) {
          await stream.stopVideo();
          setDeviceStatus(prev => ({ ...prev, camera: false }));
        } else {
          await stream.startVideo();
          setDeviceStatus(prev => ({ ...prev, camera: true }));
        }
      } catch (error) {
        console.error('Error toggling video:', error);
      }
    }
  };

  const toggleAudio = async () => {
    if (stream && isConnected) {
      try {
        if (deviceStatus.microphone) {
          await stream.stopAudio();
          setDeviceStatus(prev => ({ ...prev, microphone: false }));
        } else {
          await stream.startAudio();
          setDeviceStatus(prev => ({ ...prev, microphone: true }));
        }
      } catch (error) {
        console.error('Error toggling audio:', error);
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading interview details...</p>
        </div>
      </div>
    );
  }

  if (error || !interview) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="max-w-md mx-auto">
          <CardContent className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Interview Not Found</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isInterviewTime = () => {
    const now = new Date();
    const scheduledTime = new Date(interview.scheduledAt);
    const timeDiff = scheduledTime.getTime() - now.getTime();
    const minutesDiff = Math.floor(timeDiff / (1000 * 60));

    console.log('🕐 Interview time check:', {
      now: now.toISOString(),
      scheduledTime: scheduledTime.toISOString(),
      timeDiff,
      minutesDiff,
      scheduledLocal: scheduledTime.toLocaleString(),
      nowLocal: now.toLocaleString()
    });

    // Allow joining 15 minutes before and up to 60 minutes after scheduled time (more flexible for testing)
    return minutesDiff <= 15 && minutesDiff >= -60;
  };

  const getTimeUntilInterview = () => {
    const now = new Date();
    const scheduledTime = new Date(interview.scheduledAt);
    const timeDiff = scheduledTime.getTime() - now.getTime();
    const minutesDiff = Math.floor(timeDiff / (1000 * 60));

    if (minutesDiff > 15) {
      return `Interview starts in ${minutesDiff} minutes`;
    } else if (minutesDiff > 0) {
      return `Interview starts in ${minutesDiff} minutes - You can join now!`;
    } else if (minutesDiff >= -60) {
      return 'Interview is now available - Join now!';
    } else {
      return 'Interview time has passed';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-2xl">{interview.role} Interview</CardTitle>
                  <CardDescription className="flex items-center space-x-2 mt-2">
                    <User className="w-4 h-4" />
                    <span>Welcome, {interview.candidate.fullName}</span>
                  </CardDescription>
                </div>
                <Badge variant={isInterviewTime() ? 'default' : 'secondary'}>
                  {interview.status}
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <div className="flex flex-col">
                    <span>{new Date(interview.scheduledAt).toLocaleString()}</span>
                    <span className="text-xs text-gray-400">
                      {Intl.DateTimeFormat().resolvedOptions().timeZone}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Bot className="w-4 h-4 text-gray-500" />
                  <span>AI-Powered Interview</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span>{interview.durationMin} minutes</span>
                </div>
              </div>
              
              <div className="mt-4 space-y-2">
                <div className={`p-3 rounded-lg ${isInterviewTime() ? 'bg-green-50' : 'bg-blue-50'}`}>
                  <p className={`font-medium ${isInterviewTime() ? 'text-green-800' : 'text-blue-800'}`}>
                    {getTimeUntilInterview()}
                  </p>
                </div>

                {/* Debug info for troubleshooting */}
                <div className="p-2 bg-gray-50 rounded text-xs text-gray-600">
                  <div>Current time: {new Date().toLocaleString()}</div>
                  <div>Interview time: {new Date(interview.scheduledAt).toLocaleString()}</div>
                  <div>Can join: {isInterviewTime() ? 'Yes' : 'No'}</div>
                  <div>Minutes diff: {Math.floor((new Date(interview.scheduledAt).getTime() - new Date().getTime()) / (1000 * 60))}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Device Check */}
          <Card>
            <CardHeader>
              <CardTitle>Device Setup</CardTitle>
              <CardDescription>
                Please ensure your camera and microphone are working properly
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Camera className="w-5 h-5" />
                    <span>Camera</span>
                  </div>
                  {deviceStatus.cameraPermission ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-500" />
                  )}
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Mic className="w-5 h-5" />
                    <span>Microphone</span>
                  </div>
                  {deviceStatus.microphonePermission ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-500" />
                  )}
                </div>
              </div>

              {(!deviceStatus.cameraPermission || !deviceStatus.microphonePermission) && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Please allow camera and microphone access to join the interview.
                    <Button 
                      variant="link" 
                      className="p-0 h-auto ml-2"
                      onClick={requestPermissions}
                    >
                      Grant Permissions
                    </Button>
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Video Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Video Preview</CardTitle>
            </CardHeader>
            
            <CardContent>
              <div className="relative bg-gray-900 rounded-lg overflow-hidden" style={{ aspectRatio: '16/9' }}>
                <video
                  ref={videoRef}
                  autoPlay
                  muted
                  className="w-full h-full object-cover"
                />
                
                {isConnected && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    <Button
                      size="sm"
                      variant={deviceStatus.camera ? "default" : "secondary"}
                      onClick={toggleVideo}
                    >
                      {deviceStatus.camera ? <Video className="w-4 h-4" /> : <VideoOff className="w-4 h-4" />}
                    </Button>
                    <Button
                      size="sm"
                      variant={deviceStatus.microphone ? "default" : "secondary"}
                      onClick={toggleAudio}
                    >
                      {deviceStatus.microphone ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Join Controls */}
          <Card>
            <CardContent className="text-center py-6">
              {!isConnected ? (
                <div className="space-y-4">
                  {!zoomSDKLoaded && (
                    <div className="space-y-3 mb-4">
                      <div className="flex items-center justify-center space-x-2 text-blue-600">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        <span className="text-sm">Initializing video SDK...</span>
                      </div>

                      {/* Debug button to manually retry SDK loading */}
                      <div className="text-center">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={loadZoomSDK}
                          className="text-xs text-gray-600 border-gray-300"
                        >
                          🔄 Retry SDK Loading
                        </Button>
                      </div>

                      {/* Debug info */}
                      <div className="text-xs text-gray-500 text-center space-y-2">
                        <p>If loading takes too long, try refreshing the page</p>
                        <p>SDK Status: {ZoomVideo ? '✅ Available' : '❌ Not Available'}</p>

                        {/* Debug buttons */}
                        <div className="flex justify-center space-x-2 mt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              console.log('🔍 Debug info:', {
                                zoomVideoAvailable: !!ZoomVideo,
                                zoomVideoType: typeof ZoomVideo,
                                zoomSDKLoaded: zoomSDKLoaded,
                                userAgent: navigator.userAgent,
                                location: window.location.href
                              });
                              toast.info('Debug info logged to console');
                            }}
                            className="text-xs"
                          >
                            🔍 Debug Info
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Force reload the page
                              window.location.reload();
                            }}
                            className="text-xs"
                          >
                            🔄 Reload Page
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              console.log('🔄 Manual SDK retry requested');
                              loadZoomSDK();
                            }}
                            className="text-xs"
                          >
                            🔄 Retry SDK
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  <Button
                    size="lg"
                    onClick={joinInterview}
                    disabled={isJoining || !isInterviewTime() || !deviceStatus.cameraPermission || !deviceStatus.microphonePermission || !zoomSDKLoaded}
                    className="px-8"
                  >
                    {isJoining ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Joining...</span>
                      </div>
                    ) : !zoomSDKLoaded ? (
                      'Loading SDK...'
                    ) : (
                      'Join Interview'
                    )}
                  </Button>

                  {/* Test/Override button for development */}
                  {(!isInterviewTime() && deviceStatus.cameraPermission && deviceStatus.microphonePermission && zoomSDKLoaded) && (
                    <div className="space-y-2">
                      <p className="text-sm text-gray-600">
                        Interview not available yet?
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={joinInterview}
                        disabled={isJoining || !zoomSDKLoaded}
                        className="text-orange-600 border-orange-300 hover:bg-orange-50"
                      >
                        🧪 Join Anyway (Test Mode)
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-center space-x-2 text-green-600">
                    <CheckCircle className="w-5 h-5" />
                    <span className="font-medium">Connected to interview session</span>
                  </div>
                  <Button
                    variant="outline"
                    onClick={leaveInterview}
                  >
                    Leave Interview
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>Interview Instructions</CardTitle>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">1</div>
                  <p>Ensure you're in a quiet, well-lit environment</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">2</div>
                  <p>The AI interviewer will join automatically and guide you through the process</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">3</div>
                  <p>Speak clearly and naturally - treat it like a conversation with a human interviewer</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">4</div>
                  <p>The interview will be recorded for evaluation purposes</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CandidateJoinPage;
