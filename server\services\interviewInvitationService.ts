import { google } from 'googleapis';
import fs from 'fs';
import path from 'path';
import { db } from '../db';
import { interviewsV2, candidates, authUsers } from '@shared/schema';
import { eq, and } from 'drizzle-orm';

interface InterviewInvitationData {
  interviewId: string;
  candidateEmail: string;
  candidateName: string;
  interviewerName: string;
  role: string;
  scheduledAt: Date;
  duration: number; // in minutes
  zoomJoinUrl: string;
  candidateJoinUrl: string;
  organizationName?: string;
}

interface ICSEvent {
  summary: string;
  description: string;
  start: string;
  end: string;
  location: string;
  organizer: string;
  attendees: string[];
}

class InterviewInvitationService {
  private auth: any;
  private gmail: any;

  constructor() {
    this.initializeAuth();
  }

  private async initializeAuth() {
    try {
      const client_id = process.env.GOOGLE_CLIENT_ID;
      const client_secret = process.env.GOOGLE_CLIENT_SECRET;
      
      if (!client_id || !client_secret) {
        console.log('Gmail OAuth not configured for interview invitations');
        return;
      }

      const redirectUri = process.env.REPLIT_DEV_DOMAIN 
        ? `https://${process.env.REPLIT_DEV_DOMAIN}/api/gmail/callback`
        : 'http://localhost:5000/api/gmail/callback';

      this.auth = new google.auth.OAuth2(client_id, client_secret, redirectUri);
      
      // Load existing token
      const tokenPath = 'token.json';
      if (fs.existsSync(tokenPath)) {
        const token = JSON.parse(fs.readFileSync(tokenPath, 'utf8'));
        this.auth.setCredentials(token);
        this.gmail = google.gmail({ version: 'v1', auth: this.auth });
      }
    } catch (error) {
      console.error('Error initializing Gmail auth for invitations:', error);
    }
  }

  /**
   * Send interview invitation with ICS calendar attachment
   */
  async sendInterviewInvitation(invitationData: InterviewInvitationData): Promise<boolean> {
    try {
      if (!this.gmail) {
        console.error('Gmail not initialized for interview invitations');
        return false;
      }

      console.log(`📧 Sending interview invitation to ${invitationData.candidateEmail}`);

      // Generate ICS calendar event
      const icsContent = this.generateICSEvent({
        summary: `Interview: ${invitationData.role} Position`,
        description: this.generateEventDescription(invitationData),
        start: this.formatDateForICS(invitationData.scheduledAt),
        end: this.formatDateForICS(new Date(invitationData.scheduledAt.getTime() + invitationData.duration * 60000)),
        location: invitationData.zoomJoinUrl,
        organizer: process.env.GMAIL_FROM_EMAIL || '<EMAIL>',
        attendees: [invitationData.candidateEmail]
      });

      // Create email with ICS attachment
      const emailContent = this.createEmailWithICSAttachment(invitationData, icsContent);

      // Send email
      const result = await this.gmail.users.messages.send({
        userId: 'me',
        requestBody: {
          raw: Buffer.from(emailContent).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '')
        }
      });

      console.log(`✅ Interview invitation sent successfully. Message ID: ${result.data.id}`);
      return true;

    } catch (error) {
      console.error('❌ Error sending interview invitation:', error);
      return false;
    }
  }

  /**
   * Send interview cancellation notification
   */
  async sendCancellationNotification(invitationData: InterviewInvitationData, reason?: string): Promise<boolean> {
    try {
      if (!this.gmail) {
        console.error('Gmail not initialized for cancellation notifications');
        return false;
      }

      console.log(`📧 Sending cancellation notification to ${invitationData.candidateEmail}`);

      const emailContent = this.createCancellationEmail(invitationData, reason);

      const result = await this.gmail.users.messages.send({
        userId: 'me',
        requestBody: {
          raw: Buffer.from(emailContent).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '')
        }
      });

      console.log(`✅ Cancellation notification sent successfully. Message ID: ${result.data.id}`);
      return true;

    } catch (error) {
      console.error('❌ Error sending cancellation notification:', error);
      return false;
    }
  }

  /**
   * Send interview rescheduling notification
   */
  async sendRescheduleNotification(
    oldInvitationData: InterviewInvitationData,
    newInvitationData: InterviewInvitationData
  ): Promise<boolean> {
    try {
      if (!this.gmail) {
        console.error('Gmail not initialized for reschedule notifications');
        return false;
      }

      console.log(`📧 Sending reschedule notification to ${newInvitationData.candidateEmail}`);

      // Generate new ICS event
      const icsContent = this.generateICSEvent({
        summary: `Interview: ${newInvitationData.role} Position (Rescheduled)`,
        description: this.generateEventDescription(newInvitationData),
        start: this.formatDateForICS(newInvitationData.scheduledAt),
        end: this.formatDateForICS(new Date(newInvitationData.scheduledAt.getTime() + newInvitationData.duration * 60000)),
        location: newInvitationData.zoomJoinUrl,
        organizer: process.env.GMAIL_FROM_EMAIL || '<EMAIL>',
        attendees: [newInvitationData.candidateEmail]
      });

      const emailContent = this.createRescheduleEmail(oldInvitationData, newInvitationData, icsContent);

      const result = await this.gmail.users.messages.send({
        userId: 'me',
        requestBody: {
          raw: Buffer.from(emailContent).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '')
        }
      });

      console.log(`✅ Reschedule notification sent successfully. Message ID: ${result.data.id}`);
      return true;

    } catch (error) {
      console.error('❌ Error sending reschedule notification:', error);
      return false;
    }
  }

  /**
   * Generate ICS calendar event content
   */
  private generateICSEvent(event: ICSEvent): string {
    const now = new Date().toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    const uid = `interview-${Date.now()}@company.com`;

    return [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//Company//Interview Scheduler//EN',
      'CALSCALE:GREGORIAN',
      'METHOD:REQUEST',
      'BEGIN:VEVENT',
      `UID:${uid}`,
      `DTSTAMP:${now}`,
      `DTSTART:${event.start}`,
      `DTEND:${event.end}`,
      `SUMMARY:${event.summary}`,
      `DESCRIPTION:${event.description.replace(/\n/g, '\\n')}`,
      `LOCATION:${event.location}`,
      `ORGANIZER:MAILTO:${event.organizer}`,
      ...event.attendees.map(email => `ATTENDEE:MAILTO:${email}`),
      'STATUS:CONFIRMED',
      'SEQUENCE:0',
      'BEGIN:VALARM',
      'TRIGGER:-PT15M',
      'ACTION:DISPLAY',
      'DESCRIPTION:Interview reminder',
      'END:VALARM',
      'END:VEVENT',
      'END:VCALENDAR'
    ].join('\r\n');
  }

  /**
   * Generate event description for calendar
   */
  private generateEventDescription(data: InterviewInvitationData): string {
    return [
      `Interview for ${data.role} position`,
      '',
      `Candidate: ${data.candidateName}`,
      `Interviewer: ${data.interviewerName}`,
      `Duration: ${data.duration} minutes`,
      '',
      'Join Links:',
      `Zoom Meeting: ${data.zoomJoinUrl}`,
      `Candidate Portal: ${data.candidateJoinUrl}`,
      '',
      'Please join the meeting 5 minutes early to test your audio and video.',
      '',
      'If you have any technical issues, please contact support immediately.'
    ].join('\n');
  }

  /**
   * Format date for ICS format (YYYYMMDDTHHMMSSZ)
   */
  private formatDateForICS(date: Date): string {
    return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
  }

  /**
   * Create email with ICS attachment
   */
  private createEmailWithICSAttachment(data: InterviewInvitationData, icsContent: string): string {
    const boundary = `boundary_${Date.now()}`;
    const fromEmail = process.env.GMAIL_FROM_EMAIL || '<EMAIL>';
    const fromName = data.organizationName || 'Interview Team';

    const emailHeaders = [
      `From: ${fromName} <${fromEmail}>`,
      `To: ${data.candidateName} <${data.candidateEmail}>`,
      `Subject: Interview Invitation: ${data.role} Position - ${this.formatDateTime(data.scheduledAt)}`,
      'MIME-Version: 1.0',
      `Content-Type: multipart/mixed; boundary="${boundary}"`,
      ''
    ].join('\r\n');

    const htmlBody = this.generateInvitationHTML(data);
    const textBody = this.generateInvitationText(data);

    const emailBody = [
      `--${boundary}`,
      'Content-Type: multipart/alternative; boundary="alt_boundary"',
      '',
      '--alt_boundary',
      'Content-Type: text/plain; charset=UTF-8',
      '',
      textBody,
      '',
      '--alt_boundary',
      'Content-Type: text/html; charset=UTF-8',
      '',
      htmlBody,
      '',
      '--alt_boundary--',
      '',
      `--${boundary}`,
      'Content-Type: text/calendar; charset=UTF-8; method=REQUEST',
      'Content-Disposition: attachment; filename="interview.ics"',
      '',
      icsContent,
      '',
      `--${boundary}--`
    ].join('\r\n');

    return emailHeaders + emailBody;
  }

  /**
   * Generate HTML invitation content
   */
  private generateInvitationHTML(data: InterviewInvitationData): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h2 style="color: #2563eb; margin-bottom: 20px;">Interview Invitation</h2>
          
          <p>Dear ${data.candidateName},</p>
          
          <p>We are pleased to invite you for an interview for the <strong>${data.role}</strong> position.</p>
          
          <div style="background-color: #f0f9ff; padding: 20px; border-radius: 6px; margin: 20px 0;">
            <h3 style="color: #1e40af; margin-top: 0;">Interview Details</h3>
            <p><strong>Date & Time:</strong> ${this.formatDateTime(data.scheduledAt)}</p>
            <p><strong>Duration:</strong> ${data.duration} minutes</p>
            <p><strong>Interviewer:</strong> ${data.interviewerName}</p>
          </div>
          
          <div style="background-color: #ecfdf5; padding: 20px; border-radius: 6px; margin: 20px 0;">
            <h3 style="color: #059669; margin-top: 0;">Join the Interview</h3>
            <p><strong>Zoom Meeting:</strong><br>
            <a href="${data.zoomJoinUrl}" style="color: #2563eb; text-decoration: none;">${data.zoomJoinUrl}</a></p>
            
            <p><strong>Candidate Portal:</strong><br>
            <a href="${data.candidateJoinUrl}" style="color: #2563eb; text-decoration: none;">${data.candidateJoinUrl}</a></p>
          </div>
          
          <div style="background-color: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="margin: 0;"><strong>📅 Calendar Event:</strong> A calendar invitation is attached to this email. Please add it to your calendar.</p>
          </div>
          
          <h3 style="color: #374151;">Preparation Tips</h3>
          <ul style="color: #6b7280;">
            <li>Join the meeting 5 minutes early to test your audio and video</li>
            <li>Ensure you have a stable internet connection</li>
            <li>Find a quiet, well-lit space for the interview</li>
            <li>Have your resume and any relevant documents ready</li>
          </ul>
          
          <p>If you need to reschedule or have any questions, please reply to this email as soon as possible.</p>
          
          <p>We look forward to speaking with you!</p>
          
          <p style="margin-top: 30px;">
            Best regards,<br>
            <strong>${data.interviewerName}</strong><br>
            ${data.organizationName || 'Interview Team'}
          </p>
        </div>
      </div>
    `;
  }

  /**
   * Generate plain text invitation content
   */
  private generateInvitationText(data: InterviewInvitationData): string {
    return [
      `Dear ${data.candidateName},`,
      '',
      `We are pleased to invite you for an interview for the ${data.role} position.`,
      '',
      'INTERVIEW DETAILS:',
      `Date & Time: ${this.formatDateTime(data.scheduledAt)}`,
      `Duration: ${data.duration} minutes`,
      `Interviewer: ${data.interviewerName}`,
      '',
      'JOIN THE INTERVIEW:',
      `Zoom Meeting: ${data.zoomJoinUrl}`,
      `Candidate Portal: ${data.candidateJoinUrl}`,
      '',
      'PREPARATION TIPS:',
      '- Join the meeting 5 minutes early to test your audio and video',
      '- Ensure you have a stable internet connection',
      '- Find a quiet, well-lit space for the interview',
      '- Have your resume and any relevant documents ready',
      '',
      'If you need to reschedule or have any questions, please reply to this email as soon as possible.',
      '',
      'We look forward to speaking with you!',
      '',
      'Best regards,',
      data.interviewerName,
      data.organizationName || 'Interview Team'
    ].join('\n');
  }

  /**
   * Create cancellation email content
   */
  private createCancellationEmail(data: InterviewInvitationData, reason?: string): string {
    const fromEmail = process.env.GMAIL_FROM_EMAIL || '<EMAIL>';
    const fromName = data.organizationName || 'Interview Team';

    const subject = `Interview Cancelled: ${data.role} Position`;
    const body = [
      `Dear ${data.candidateName},`,
      '',
      `We regret to inform you that your scheduled interview for the ${data.role} position on ${this.formatDateTime(data.scheduledAt)} has been cancelled.`,
      '',
      reason ? `Reason: ${reason}` : '',
      '',
      'We apologize for any inconvenience this may cause. Our team will be in touch soon to reschedule at your earliest convenience.',
      '',
      'Thank you for your understanding.',
      '',
      'Best regards,',
      data.interviewerName,
      data.organizationName || 'Interview Team'
    ].filter(line => line !== '').join('\n');

    return [
      `From: ${fromName} <${fromEmail}>`,
      `To: ${data.candidateName} <${data.candidateEmail}>`,
      `Subject: ${subject}`,
      'Content-Type: text/plain; charset=UTF-8',
      '',
      body
    ].join('\r\n');
  }

  /**
   * Create reschedule email content
   */
  private createRescheduleEmail(
    oldData: InterviewInvitationData,
    newData: InterviewInvitationData,
    icsContent: string
  ): string {
    const boundary = `boundary_${Date.now()}`;
    const fromEmail = process.env.GMAIL_FROM_EMAIL || '<EMAIL>';
    const fromName = newData.organizationName || 'Interview Team';

    const emailHeaders = [
      `From: ${fromName} <${fromEmail}>`,
      `To: ${newData.candidateName} <${newData.candidateEmail}>`,
      `Subject: Interview Rescheduled: ${newData.role} Position - New Time: ${this.formatDateTime(newData.scheduledAt)}`,
      'MIME-Version: 1.0',
      `Content-Type: multipart/mixed; boundary="${boundary}"`,
      ''
    ].join('\r\n');

    const body = [
      `Dear ${newData.candidateName},`,
      '',
      `Your interview for the ${newData.role} position has been rescheduled.`,
      '',
      `Previous Time: ${this.formatDateTime(oldData.scheduledAt)}`,
      `New Time: ${this.formatDateTime(newData.scheduledAt)}`,
      '',
      `Duration: ${newData.duration} minutes`,
      `Interviewer: ${newData.interviewerName}`,
      '',
      'JOIN THE INTERVIEW:',
      `Zoom Meeting: ${newData.zoomJoinUrl}`,
      `Candidate Portal: ${newData.candidateJoinUrl}`,
      '',
      'A new calendar invitation is attached to this email.',
      '',
      'We apologize for any inconvenience and look forward to speaking with you at the new time.',
      '',
      'Best regards,',
      newData.interviewerName,
      newData.organizationName || 'Interview Team'
    ].join('\n');

    const emailBody = [
      `--${boundary}`,
      'Content-Type: text/plain; charset=UTF-8',
      '',
      body,
      '',
      `--${boundary}`,
      'Content-Type: text/calendar; charset=UTF-8; method=REQUEST',
      'Content-Disposition: attachment; filename="interview_rescheduled.ics"',
      '',
      icsContent,
      '',
      `--${boundary}--`
    ].join('\r\n');

    return emailHeaders + emailBody;
  }

  /**
   * Format date and time for display
   */
  private formatDateTime(date: Date): string {
    return date.toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      timeZoneName: 'short'
    });
  }
}

export const interviewInvitationService = new InterviewInvitationService();
export default interviewInvitationService;
