import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { 
  Upload, 
  FileText, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Linkedin, 
  Star,
  CheckCircle,
  XCircle,
  AlertCircle,
  MessageSquare,
  UserCheck,
  UserX,
  Calendar,
  Building2,
  Target,
  Zap,
  TrendingUp,
  Award,
  Briefcase,
  Monitor,
  DollarSign
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import CandidateDetailsModal from './CandidateDetailsModal';

interface ContactInfo {
  name?: string;
  email?: string;
  phone?: string;
  location?: string;
  linkedin?: string;
}

interface ResumeAnalysis {
  overall_score: number;
  match_score: number;
  experience_years: number;
  key_skills: string[];
  matched_skills: string[];
  missing_skills: string[];
  strengths: string[];
  concerns: string[];
  recommendation: 'HIRE' | 'INTERVIEW' | 'REJECT';
  detailed_feedback: string;
  interview_questions: string[];
}

interface JobPosting {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  department?: string;
  location?: string;
  skillsRequired?: string[];
  experienceLevel?: string;
  aiGeneratedSummary?: string;
}

interface AnalysisResult {
  contactInfo: ContactInfo;
  analysis?: ResumeAnalysis;
  extractedText: string;
  method: string;
  hasJobAnalysis: boolean;
  jobPosting?: JobPosting;
  autoMatchScore?: number;
  matchedJobs?: Array<{
    jobId: string;
    jobTitle: string;
    matchScore: number;
    matchedSkills: string[];
  }>;
}

export default function EnhancedResumeScreening() {
  const [file, setFile] = useState<File | null>(null);
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [selectedJobId, setSelectedJobId] = useState<string>('auto');
  const [customJobDescription, setCustomJobDescription] = useState('');
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    fetchJobPostings();
  }, []);

  const fetchJobPostings = async () => {
    try {
      const response = await fetch('/api/job-postings');
      if (response.ok) {
        const data = await response.json();
        setJobPostings(data);
      }
    } catch (error) {
      console.error('Failed to fetch job postings:', error);
    }
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];
      if (droppedFile.type === 'application/pdf' || 
          droppedFile.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        setFile(droppedFile);
      } else {
        toast({
          title: "Invalid file type",
          description: "Please upload a PDF or Word document",
          variant: "destructive",
        });
      }
    }
  }, [toast]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      console.log('File selected:', selectedFile.name, selectedFile.type, selectedFile.size);
      setFile(selectedFile);
      toast({
        title: "File selected",
        description: `${selectedFile.name} (${(selectedFile.size / 1024 / 1024).toFixed(2)} MB)`,
      });
    }
  };

  const analyzeResume = async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please select a resume file to analyze",
        variant: "destructive",
      });
      return;
    }

    console.log('Starting resume analysis with file:', file.name, file.type, file.size);
    setIsAnalyzing(true);
    setAnalysisResult(null);

    try {
      const formData = new FormData();
      formData.append('resume', file);
      
      // Determine job description source
      let jobDescription = '';
      let selectedJob: JobPosting | undefined;
      
      if (selectedJobId === 'custom') {
        jobDescription = customJobDescription;
      } else if (selectedJobId === 'auto') {
        // Auto-match against all job postings
        jobDescription = 'AUTO_MATCH_ALL_JOBS';
      } else {
        selectedJob = jobPostings.find(job => job.id === selectedJobId);
        if (selectedJob) {
          jobDescription = [
            `Job Title: ${selectedJob.title}`,
            `Department: ${selectedJob.department || 'Not specified'}`,
            `Location: ${selectedJob.location || 'Not specified'}`,
            `Experience Level: ${selectedJob.experienceLevel || 'Not specified'}`,
            `Required Skills: ${selectedJob.skillsRequired?.join(', ') || 'Not specified'}`,
            `Description: ${selectedJob.description}`,
            selectedJob.requirements ? `Requirements: ${selectedJob.requirements}` : ''
          ].filter(Boolean).join('\n\n');
        }
      }

      formData.append('jobDescription', jobDescription);
      formData.append('selectedJobId', selectedJobId);

      console.log('Sending request to /api/resume/analyze with job description length:', jobDescription.length);

      const response = await fetch('/api/resume/analyze', {
        method: 'POST',
        body: formData,
      });

      console.log('Response status:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Resume analysis error response:', errorText);
        throw new Error(`Analysis failed: ${response.statusText} - ${errorText}`);
      }

      const result: AnalysisResult = await response.json();
      console.log('Resume analysis result received:', result);
      
      // Enhance result with job posting data
      if (selectedJob && selectedJobId !== 'auto') {
        result.jobPosting = selectedJob;
      }
      
      setAnalysisResult(result);
      setShowModal(true);
      
      // Auto-save candidate to database after analysis
      await autoSaveCandidate(result);
      
      toast({
        title: "Analysis Complete",
        description: result.analysis 
          ? `Resume analyzed with ${result.analysis.overall_score}% overall score`
          : "Resume processed successfully",
      });

    } catch (error) {
      console.error('Resume analysis error:', error);
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : "Failed to analyze resume",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const autoSaveCandidate = async (result: AnalysisResult) => {
    if (!result.contactInfo.email) {
      console.log('No email found, skipping auto-save');
      return;
    }

    try {
      const candidateData = {
        email: result.contactInfo.email,
        fullName: result.contactInfo.name || 'Unknown',
        phone: result.contactInfo.phone,
        location: result.contactInfo.location,
        linkedinUrl: result.contactInfo.linkedin,
        resumeText: result.extractedText,
        skills: result.analysis?.key_skills || [],
        experienceYears: result.analysis?.experience_years || 0,
        status: 'pending_review', // Default status for auto-saved candidates
        analysisResult: result.analysis,
        overallScore: result.analysis?.overall_score || 0,
        matchScore: result.analysis?.match_score || 0,
        recommendation: result.analysis?.recommendation || 'INTERVIEW',
        appliedJobId: selectedJobId !== 'auto' && selectedJobId !== 'custom' ? selectedJobId : null,
        sourceChannel: 'resume_screening',
        aiSummary: result.analysis?.detailed_feedback,
      };

      const response = await fetch('/api/candidates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(candidateData),
      });

      if (response.ok) {
        const savedCandidate = await response.json();
        console.log('Candidate auto-saved:', savedCandidate.id);
        
        toast({
          title: "Candidate Added",
          description: `${result.contactInfo.name} has been added to job posting candidates`,
        });
      } else {
        const errorData = await response.json();
        console.error('Failed to auto-save candidate:', errorData);
        // Don't show error toast for auto-save failures, just log
      }
    } catch (error) {
      console.error('Auto-save error:', error);
      // Don't show error toast for auto-save failures, just log
    }
  };

  const saveCandidate = async (decision: 'approve' | 'reject') => {
    if (!analysisResult || !analysisResult.contactInfo.email) {
      toast({
        title: "Cannot save candidate",
        description: "Missing candidate contact information",
        variant: "destructive",
      });
      return;
    }

    try {
      const candidateData = {
        email: analysisResult.contactInfo.email,
        fullName: analysisResult.contactInfo.name || 'Unknown',
        phone: analysisResult.contactInfo.phone,
        location: analysisResult.contactInfo.location,
        linkedinUrl: analysisResult.contactInfo.linkedin,
        resumeText: analysisResult.extractedText,
        skills: analysisResult.analysis?.key_skills || [],
        experienceYears: analysisResult.analysis?.experience_years || 0,
        status: decision === 'approve' ? 'approved_for_interview' : 'rejected',
        analysisResult: analysisResult.analysis,
        overallScore: analysisResult.analysis?.overall_score || 0,
        matchScore: analysisResult.analysis?.match_score || 0,
        recommendation: analysisResult.analysis?.recommendation || (decision === 'approve' ? 'INTERVIEW' : 'REJECT'),
        appliedJobId: selectedJobId !== 'auto' && selectedJobId !== 'custom' ? selectedJobId : null,
        sourceChannel: 'resume_screening',
        aiSummary: analysisResult.analysis?.detailed_feedback,
      };

      const response = await fetch('/api/candidates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(candidateData),
      });

      if (response.ok) {
        const savedCandidate = await response.json();
        console.log('Candidate saved:', savedCandidate.id);
        
        toast({
          title: decision === 'approve' ? "Candidate Approved" : "Candidate Rejected",
          description: `${analysisResult.contactInfo.name} has been ${decision === 'approve' ? 'approved for interview' : 'rejected'}`,
        });

        // Reset form
        setFile(null);
        setAnalysisResult(null);
      } else {
        const errorData = await response.json();
        console.error('Failed to save candidate:', errorData);
        throw new Error(errorData.error || 'Failed to save candidate');
      }
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Failed to save candidate decision",
        variant: "destructive",
      });
    }
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'HIRE': return 'bg-green-100 text-green-800 border-green-200';
      case 'INTERVIEW': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'REJECT': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <>
      <div className="h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 p-6 overflow-y-auto">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-blue-700 mb-2">
              AI-Powered Resume Screening
            </h1>
            <p className="text-gray-600 text-sm">
              Advanced ATS with intelligent job matching and candidate evaluation
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Upload and Job Selection - Parallel Columns */}
          <div className="lg:col-span-2 space-y-4">
            {/* Parallel Columns Layout */}
            <div className="grid grid-cols-2 gap-8">
              {/* Resume Upload Column */}
              <div className="bg-white/80 backdrop-blur-sm border-0 shadow-lg rounded-xl p-6" style={{
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.05)'
              }}>
                <div className="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                  <Upload className="w-4 h-4" />
                  Resume Upload
                </div>
                <div
                  className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer ${
                    dragActive 
                      ? 'border-indigo-500 bg-indigo-50' 
                      : file
                        ? 'border-green-400 bg-green-50'
                        : 'border-gray-300 hover:border-indigo-400'
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                  onClick={(e) => {
                    if (!file && e.target === e.currentTarget) {
                      e.preventDefault();
                      e.stopPropagation();
                      if (fileInputRef.current) {
                        fileInputRef.current.click();
                      }
                    }
                  }}
                >
                  {file ? (
                    <div className="space-y-2">
                      <CheckCircle className="w-8 h-8 text-green-600 mx-auto" />
                      <p className="font-medium text-green-800 text-sm">{file.name}</p>
                      <p className="text-xs text-green-600">
                        {(file.size / 1024 / 1024).toFixed(2)} MB - Ready
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          setFile(null);
                        }}
                      >
                        Remove
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                      <div>
                        <p className="text-sm font-medium text-gray-900 mb-1">
                          Drop resume here
                        </p>
                        <p className="text-xs text-gray-500 mb-2">
                          or click to browse
                        </p>
                        <input
                          type="file"
                          accept=".pdf,.docx,.doc"
                          onChange={handleFileChange}
                          ref={fileInputRef}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs"
                          onClick={() => fileInputRef.current?.click()}
                        >
                          Choose File
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Job Matching Column */}
              <div className="bg-white/80 backdrop-blur-sm border-0 shadow-lg rounded-xl p-6" style={{
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.05)'
              }}>
                <div className="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                  <Building2 className="w-4 h-4" />
                  Job Matching
                </div>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="job-select" className="text-xs font-medium text-gray-700 mb-1 block">Job Posting</Label>
                    <Select value={selectedJobId} onValueChange={setSelectedJobId}>
                      <SelectTrigger id="job-select" className="h-8 text-xs">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="auto">
                          <div className="flex items-center gap-2">
                            <Zap className="w-3 h-3" />
                            Auto-match all jobs
                          </div>
                        </SelectItem>
                        <SelectItem value="custom">
                          <div className="flex items-center gap-2">
                            <MessageSquare className="w-3 h-3" />
                            Custom job description
                          </div>
                        </SelectItem>
                        {jobPostings.map((job) => (
                          <SelectItem key={job.id} value={job.id}>
                            <div className="flex items-center gap-2">
                              <Briefcase className="w-3 h-3" />
                              <div className="truncate">
                                <div className="font-medium text-xs">{job.title}</div>
                                <div className="text-xs text-gray-500">{job.department}</div>
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center justify-center h-12 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border">
                    <div className="text-center">
                      <Target className="w-5 h-5 text-blue-500 mx-auto mb-1" />
                      <div className="text-xs text-gray-600">
                        {selectedJobId === 'auto' ? 'Auto-matching' : 'Targeted match'}
                      </div>
                    </div>
                  </div>
                  
                  {/* Action Button inside Job Matching tile */}
                  <div className="pt-2">
                    <Button
                      onClick={analyzeResume}
                      disabled={!file || isAnalyzing}
                      className="w-full text-sm py-2"
                    >
                      {isAnalyzing ? 'Analyzing...' : 'Analyze Resume'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Custom Job Description */}
          <div className="lg:col-span-1">
            {selectedJobId === 'custom' && (
              <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="w-5 h-5" />
                    Custom Job Description
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Enter detailed job description, requirements, and qualifications..."
                    value={customJobDescription}
                    onChange={(e) => setCustomJobDescription(e.target.value)}
                    rows={6}
                  />
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Analysis Results - New Container Below */}
        {analysisResult && (
          <div className="space-y-6">
            {/* Candidate Details Header */}
            <div className="bg-white/80 backdrop-blur-sm border-0 shadow-lg rounded-xl p-6" style={{
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.05)'
            }}>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-gray-900">Candidate Details</h2>
                <div className="flex items-center gap-3">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Monitor className="w-4 h-4" />
                    Screen
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Calendar className="w-4 h-4" />
                    Schedule Call
                  </Button>
                </div>
              </div>

              {/* Candidate Profile */}
              <div className="flex items-start gap-4 mb-6">
                <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl font-bold">
                  {analysisResult.contactInfo.name ? analysisResult.contactInfo.name.charAt(0).toUpperCase() : 'C'}
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {analysisResult.contactInfo.name || 'Candidate'}
                  </h3>
                  <div className="flex items-center gap-2 text-gray-600 mb-3">
                    <MapPin className="w-4 h-4" />
                    <span>{analysisResult.contactInfo.location || 'Location not specified'}</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                      <CheckCircle className="w-4 h-4" />
                      {analysisResult.analysis?.overall_score || 0}% Matched
                    </div>
                    <div className="flex items-center gap-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                      <Briefcase className="w-4 h-4" />
                      {analysisResult.analysis?.experience_years || 0} years
                    </div>
                    <div className="flex items-center gap-2 px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">
                      <DollarSign className="w-4 h-4" />
                      Market Rate
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-3 gap-6 mb-6">
                <div>
                  <label className="text-sm font-medium text-gray-500 block mb-1">Email</label>
                  <span className="text-sm text-gray-900">{analysisResult.contactInfo.email || 'Not provided'}</span>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 block mb-1">Phone</label>
                  <span className="text-sm text-gray-900">{analysisResult.contactInfo.phone || 'Not provided'}</span>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 block mb-1">Analysis Date</label>
                  <span className="text-sm text-gray-900">{new Date().toLocaleDateString()}</span>
                </div>
              </div>

              {/* Tabs */}
              <div className="border-b border-gray-200 mb-6">
                <div className="flex space-x-8">
                  <button className="py-2 px-1 border-b-2 border-blue-500 text-blue-600 font-medium text-sm">
                    Application
                  </button>
                  <button className="py-2 px-1 text-gray-500 font-medium text-sm hover:text-gray-700">
                    Analysis
                  </button>
                  <button className="py-2 px-1 text-gray-500 font-medium text-sm hover:text-gray-700">
                    Feedback
                  </button>
                  <button className="py-2 px-1 text-gray-500 font-medium text-sm hover:text-gray-700">
                    Questions
                  </button>
                </div>
              </div>

              {/* About Section */}
              <div className="mb-6">
                <h4 className="text-base font-semibold text-gray-900 mb-3">
                  About {analysisResult.contactInfo.name || 'Candidate'}
                </h4>
                <p className="text-sm text-gray-600">
                  {analysisResult.analysis?.detailed_feedback || 'No detailed feedback available.'}
                </p>
              </div>

              {/* Top Skills */}
              <div className="mb-6">
                <h4 className="text-base font-semibold text-gray-900 mb-3">Top Skills</h4>
                <div className="flex flex-wrap gap-2">
                  {analysisResult.analysis?.key_skills?.slice(0, 6).map((skill, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Analysis Results - Detailed Cards */}
            <div className="space-y-4">
                {/* Contact Information */}
                <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="w-5 h-5" />
                      Candidate Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {analysisResult.contactInfo.name && (
                        <div className="flex items-center gap-2">
                          <User className="w-4 h-4 text-gray-500" />
                          <span className="font-medium">{analysisResult.contactInfo.name}</span>
                        </div>
                      )}
                      {analysisResult.contactInfo.email && (
                        <div className="flex items-center gap-2">
                          <Mail className="w-4 h-4 text-gray-500" />
                          <span>{analysisResult.contactInfo.email}</span>
                        </div>
                      )}
                      {analysisResult.contactInfo.phone && (
                        <div className="flex items-center gap-2">
                          <Phone className="w-4 h-4 text-gray-500" />
                          <span>{analysisResult.contactInfo.phone}</span>
                        </div>
                      )}
                      {analysisResult.contactInfo.location && (
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-gray-500" />
                          <span>{analysisResult.contactInfo.location}</span>
                        </div>
                      )}
                      {analysisResult.contactInfo.linkedin && (
                        <div className="flex items-center gap-2">
                          <Linkedin className="w-4 h-4 text-gray-500" />
                          <a 
                            href={analysisResult.contactInfo.linkedin}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            LinkedIn Profile
                          </a>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {analysisResult.analysis && (
                  <>
                    {/* Analysis Scores */}
                    <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <TrendingUp className="w-5 h-5" />
                          Analysis Scores
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          <div className="text-center">
                            <div className={`text-3xl font-bold ${getScoreColor(analysisResult.analysis.overall_score)}`}>
                              {analysisResult.analysis.overall_score}%
                            </div>
                            <div className="text-sm text-gray-600">Overall Score</div>
                            <Progress 
                              value={analysisResult.analysis.overall_score} 
                              className="mt-2 h-2"
                            />
                          </div>
                          <div className="text-center">
                            <div className={`text-3xl font-bold ${getScoreColor(analysisResult.analysis.match_score)}`}>
                              {analysisResult.analysis.match_score}%
                            </div>
                            <div className="text-sm text-gray-600">Job Match</div>
                            <Progress 
                              value={analysisResult.analysis.match_score} 
                              className="mt-2 h-2"
                            />
                          </div>
                          <div className="text-center">
                            <div className="text-3xl font-bold text-blue-600">
                              {analysisResult.analysis.experience_years}
                            </div>
                            <div className="text-sm text-gray-600">Years Experience</div>
                          </div>
                        </div>

                        <div className="mt-6 flex justify-center">
                          <Badge 
                            className={`text-base px-4 py-2 ${getRecommendationColor(analysisResult.analysis.recommendation)}`}
                          >
                            <Award className="w-4 h-4 mr-2" />
                            {analysisResult.analysis.recommendation}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Skills Analysis */}
                    <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle>Skills Analysis</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Tabs defaultValue="matched" className="w-full">
                          <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="matched" className="flex items-center gap-2">
                              <CheckCircle className="w-4 h-4" />
                              Matched ({analysisResult.analysis.matched_skills.length})
                            </TabsTrigger>
                            <TabsTrigger value="missing" className="flex items-center gap-2">
                              <XCircle className="w-4 h-4" />
                              Missing ({analysisResult.analysis.missing_skills.length})
                            </TabsTrigger>
                            <TabsTrigger value="all" className="flex items-center gap-2">
                              <Star className="w-4 h-4" />
                              All Skills ({analysisResult.analysis.key_skills.length})
                            </TabsTrigger>
                          </TabsList>
                          
                          <TabsContent value="matched" className="mt-4">
                            <div className="flex flex-wrap gap-2">
                              {analysisResult.analysis.matched_skills.map((skill, index) => (
                                <Badge key={index} className="bg-green-100 text-green-800">
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          </TabsContent>
                          
                          <TabsContent value="missing" className="mt-4">
                            <div className="flex flex-wrap gap-2">
                              {analysisResult.analysis.missing_skills.map((skill, index) => (
                                <Badge key={index} variant="destructive">
                                  <XCircle className="w-3 h-3 mr-1" />
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          </TabsContent>
                          
                          <TabsContent value="all" className="mt-4">
                            <div className="flex flex-wrap gap-2">
                              {analysisResult.analysis.key_skills.map((skill, index) => (
                                <Badge key={index} variant="secondary">
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          </TabsContent>
                        </Tabs>
                      </CardContent>
                    </Card>

                    {/* Detailed Analysis */}
                    <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
                      <CardHeader>
                        <CardTitle>Detailed Analysis</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Tabs defaultValue="feedback" className="w-full">
                          <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="feedback">AI Feedback</TabsTrigger>
                            <TabsTrigger value="strengths">Strengths</TabsTrigger>
                            <TabsTrigger value="concerns">Concerns</TabsTrigger>
                          </TabsList>
                          
                          <TabsContent value="feedback" className="mt-4">
                            <div className="bg-blue-50 p-4 rounded-lg">
                              <p className="text-gray-700 leading-relaxed">
                                {analysisResult.analysis.detailed_feedback}
                              </p>
                            </div>
                          </TabsContent>
                          
                          <TabsContent value="strengths" className="mt-4">
                            <ul className="space-y-2">
                              {analysisResult.analysis.strengths.map((strength, index) => (
                                <li key={index} className="flex items-start gap-2">
                                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-700">{strength}</span>
                                </li>
                              ))}
                            </ul>
                          </TabsContent>
                          
                          <TabsContent value="concerns" className="mt-4">
                            <ul className="space-y-2">
                              {analysisResult.analysis.concerns.map((concern, index) => (
                                <li key={index} className="flex items-start gap-2">
                                  <AlertCircle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
                                  <span className="text-gray-700">{concern}</span>
                                </li>
                              ))}
                            </ul>
                          </TabsContent>
                        </Tabs>
                      </CardContent>
                    </Card>

                    {/* Interview Questions */}
                    {analysisResult.analysis.interview_questions.length > 0 && (
                      <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <MessageSquare className="w-5 h-5" />
                            Suggested Interview Questions
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ol className="space-y-3">
                            {analysisResult.analysis.interview_questions.map((question, index) => (
                              <li key={index} className="flex gap-3">
                                <span className="bg-indigo-100 text-indigo-800 rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium flex-shrink-0">
                                  {index + 1}
                                </span>
                                <span className="text-gray-700">{question}</span>
                              </li>
                            ))}
                          </ol>
                        </CardContent>
                      </Card>
                    )}

                    {/* Decision Buttons */}
                    <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
                      <CardContent className="p-6">
                        <div className="flex gap-4 justify-center">
                          <Button
                            onClick={() => saveCandidate('approve')}
                            className="bg-green-600 hover:bg-green-700 text-white px-8 py-3"
                          >
                            <UserCheck className="w-5 h-5 mr-2" />
                            Approve for Interview
                          </Button>
                          <Button
                            onClick={() => saveCandidate('reject')}
                            variant="destructive"
                            className="px-8 py-3"
                          >
                            <UserX className="w-5 h-5 mr-2" />
                            Reject Candidate
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </>
                )}
              </div>
            ) : null}
          </div>
        </div>
        
        {/* Ready for AI-Powered Analysis - Moved to Bottom */}
        {!analysisResult && (
          <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
            <CardContent className="p-12 text-center">
              <Briefcase className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-xl font-semibold text-gray-700 mb-2">
                Ready for AI-Powered Analysis
              </h3>
              <p className="text-gray-600 mb-6">
                Upload a resume and select a job posting to get started with intelligent candidate screening
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-500">
                <div className="flex items-center justify-center gap-2">
                  <FileText className="w-4 h-4" />
                  PDF & Word Support
                </div>
                <div className="flex items-center justify-center gap-2">
                  <Target className="w-4 h-4" />
                  Smart Job Matching
                </div>
                <div className="flex items-center justify-center gap-2">
                  <Award className="w-4 h-4" />
                  AI-Powered Insights
                </div>
              </div>
            </CardContent>
          </Card>
        )}
        </div>
      </div>
      
      {/* Candidate Details Modal */}
      {analysisResult && (
        <CandidateDetailsModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          analysisResult={analysisResult}
          fileName={file?.name}
        />
      )}
    </>
  );
}