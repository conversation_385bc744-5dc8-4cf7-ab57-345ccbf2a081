import "dotenv/config" ;
import express, { type Request, Response, NextFunction } from "express";
import session from "express-session";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import { gmailService } from "./services/gmailService";
import { setupSwagger } from "./swagger";
import { bootstrapSuperAdmin } from './bootstrap-super-admin';
import { getSessionMaxAge } from './auth';

declare module 'express-session' {
  interface SessionData {
    userId?: string;
    user?: any;
  }
}

const app = express();
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: false, limit: '50mb' }));

// Session configuration - configurable expiry
app.use(session({
  secret: process.env.SESSION_SECRET || 'hr-workflow-ai-secret-key',
  resave: false,
  saveUninitialized: false,
  rolling: true, // Reset expiry on activity
  cookie: {
    secure: false,
    httpOnly: true,
    maxAge: getSessionMaxAge()
  }
}));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  // Setup Swagger documentation
  setupSwagger(app);
  
  // Bootstrap super admin if needed
  try {
    await bootstrapSuperAdmin();
  } catch (error) {
    console.log('⚠️  Super admin bootstrap:', error instanceof Error ? error.message : String(error));
  }
  
  // Setup ElevenLabs WebSocket BEFORE creating HTTP server to avoid conflicts
  const server = await registerRoutes(app);
  
  // IMPORTANT: Initialize ElevenLabs WebSocket first, before other WebSocket servers
  console.log('🔧 Initializing WebSocket servers in correct order...');
  
  // WEBSOCKET SERVER - Only handle specific application paths, let Vite handle its own
  const { WebSocketServer } = await import('ws');
  const mainWss = new WebSocketServer({ 
    server,
    perMessageDeflate: false,
    clientTracking: true,
    verifyClient: (info: { req: { url?: string; headers: Record<string, any> } }) => {
      const url = info.req.url || '';
      const protocol = info.req.headers['sec-websocket-protocol'];
      
      // Let Vite handle its own WebSocket connections
      if (protocol === 'vite-hmr') {
        return false; // Reject, let Vite handle
      }
      
      // Only accept specific application WebSocket paths
      return (
        url.includes('/elevenlabs') || 
        url.includes('/test-websocket') || 
        url.includes('/test-simple') ||
        url.includes('/voice') ||
        url.includes('/twilio')
      );
    }
  });
  
  mainWss.on('connection', async (ws, req) => {
    const path = req.url || '';
    
    console.log('🎯 APPLICATION WebSocket connection received');
    console.log('📍 Path:', path);
    
    // Route based on path for application WebSockets only
    if (path.includes('/elevenlabs-stream-final')) {
      console.log('🤖 ROUTING TO: ElevenLabs Final Handler');
      const { handleElevenLabsStreamFinal } = await import('./api/elevenlabs-stream-final-handler.js');
      handleElevenLabsStreamFinal(ws, req);
    } else if (path.includes('/test-websocket') || path.includes('/test-simple')) {
      console.log('🧪 ROUTING TO: Simple Test Handler');
      ws.send('Simple WebSocket Connected Successfully!');
    } else if (path.includes('/elevenlabs') || path.includes('/voice') || path.includes('/twilio')) {
      console.log('🔀 ROUTING TO: ElevenLabs Handler');
      const { handleElevenLabsStreamFinal } = await import('./api/elevenlabs-stream-final-handler.js');
      handleElevenLabsStreamFinal(ws, req);
    } else {
      console.log('❌ UNKNOWN WebSocket path, closing connection');
      ws.close(1000, 'Unknown WebSocket path');
    }
  });
  
  console.log('✅ UNIFIED WebSocket server configured (handles all paths)');
  
  console.log('✅ All WebSocket servers initialized successfully');

  // Initialize Gmail monitoring with delay to ensure full startup
  setTimeout(() => {
    if (gmailService.isAuthenticated()) {
      console.log('Starting Gmail monitoring service...');
      gmailService.startMonitoring();
    } else {
      console.log('Gmail not authenticated. Email monitoring disabled.');
    }
  }, 3000);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = 5000;
  const listenConfig: { port: number; host: string; reusePort?: boolean } = {
    port,
    host: "0.0.0.0",
  };

  if (process.platform !== "win32") {
    listenConfig.reusePort = true;
  }

  server.listen(listenConfig, () => {
    log(`serving on port ${port}`);
  });
})();

