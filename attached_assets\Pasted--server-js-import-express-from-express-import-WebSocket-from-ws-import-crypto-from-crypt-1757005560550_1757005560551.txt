// server.js
import express from "express";
import WebSocket from "ws";
import crypto from "crypto";

const ELEVEN_API_KEY = process.env.ELEVENLABS_API_KEY;
const AGENT_ID       = process.env.ELEVENLABS_AGENT_ID;

const app = express();
app.use(express.json({ limit: "2mb" }));

// Simple in-memory session store: sessionId -> { ws, lastFormats }
const sessions = new Map();

/**
 * Open a ConvAI socket and immediately send conversation config + dynamic vars.
 * Returns a sessionId you can use to stream audio and close later.
 */
app.post("/convai/start", async (req, res) => {
  try {
    const {
      agentId = AGENT_ID,

      // --- dynamic variables you showed in your screenshots ---
      job_title,
      company_name,
      company_industry,
      company_overview,
      experience_level,
      salary_range,
      work_environment,
      Key_Responsibilities,  // keep the exact casing you used in the portal
      job_requirements,

      // optional extras you might add
      candidate_name,
      call_purpose
    } = req.body || {};

    // open WS
    const url = `wss://api.elevenlabs.io/v1/convai/conversation?agent_id=${agentId}`;
    const ws  = new WebSocket(url, {
      headers: { Authorization: `Bearer ${ELEVEN_API_KEY}` }
    });

    const sessionId = crypto.randomUUID();

    ws.on("open", () => {
      // 1) SEND conversation config + dynamic variables FIRST
      // For phone bridges (Twilio), ulaw_8000 in/out avoids transcoding.
      // For browser mic, prefer pcm_16000 for input.
      const init = {
        type: "conversation_initiation_client_data",
        conversation_initiation_client_data: {
          conversation_config: {
            agent_input_audio_format: "ulaw_8000",   // or "pcm_16000" if browser mic
            agent_output_audio_format: "ulaw_8000"
          },
          dynamic_variables: {
            // send ONLY keys you actually use in the prompt; names are case-sensitive
            job_title,
            company_name,
            company_industry,
            company_overview,
            experience_level,
            salary_range,
            work_environment,
            Key_Responsibilities,
            job_requirements,
            candidate_name,
            call_purpose
          }
        }
      };
      ws.send(JSON.stringify(init));
      console.log(`[EL] (${sessionId}) sent conversation_initiation_client_data`);
    });

    ws.on("message", (buf) => {
      const evt = JSON.parse(buf.toString());

      // Helpful logs (trim long text)
      if (evt.type === "conversation_initiation_metadata") {
        const m = evt.conversation_initiation_metadata_event;
        console.log(`[EL] (${sessionId}) meta in=${m.user_input_audio_format} out=${m.agent_output_audio_format}`);
      }
      if (evt.type === "agent_response") {
        const t = evt.agent_response_event?.agent_response ?? "";
        console.log(`[EL] (${sessionId}) agent_response: ${t.slice(0,120)}…`);
      }
      if (evt.type === "audio") {
        // evt.audio_event.audio_base_64 -> ulaw_8000 (here) or PCM depending on your config
        // If you’re bridging to Twilio, you’d forward this base64 as a Twilio "media" payload.
        // For a web client, you’d decode and play it.
      }
      if (evt.type === "vad_score" || evt.type === "user_transcript") {
        console.log(`[EL] (${sessionId}) event: ${evt.type}`);
      }
    });

    ws.on("close", () => {
      console.log(`[EL] (${sessionId}) socket closed`);
      sessions.delete(sessionId);
    });

    ws.on("error", (e) => console.error(`[EL] (${sessionId}) error:`, e.message));

    sessions.set(sessionId, { ws, lastFormats: { in: "ulaw_8000", out: "ulaw_8000" } });
    res.json({ ok: true, sessionId });
  } catch (e) {
    console.error(e);
    res.status(500).json({ ok: false, error: e.message });
  }
});