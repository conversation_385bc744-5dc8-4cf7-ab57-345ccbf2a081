import { Router } from 'express';
import { gmailService } from '../services/gmailService';

const router = Router();

/**
 * @swagger
 * /email-cleanup/stop-company-emails:
 *   post:
 *     summary: Stop all email processing for company.com domain
 *     description: Immediately stop any email processing and mark problematic emails as read
 *     tags: [Email Cleanup]
 *     responses:
 *       200:
 *         description: Cleanup completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 cleaned:
 *                   type: number
 */
router.post('/stop-company-emails', async (req, res) => {
  try {
    console.log('🧹 Starting email cleanup for company.com domain...');
    
    const result = await gmailService.cleanupCompanyEmails();
    
    res.json({
      success: true,
      message: 'Email cleanup completed successfully',
      cleaned: result.cleaned,
      details: result.details
    });
  } catch (error) {
    console.error('Error during email cleanup:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clean up emails'
    });
  }
});

/**
 * @swagger
 * /email-cleanup/status:
 *   get:
 *     summary: Get email system status
 *     description: Check current email processing status and blocklist info
 *     tags: [Email Cleanup]
 *     responses:
 *       200:
 *         description: Email system status
 */
router.get('/status', async (req, res) => {
  try {
    const status = {
      gmailAuthenticated: gmailService.isAuthenticated(),
      blockedDomains: ['company.com', 'testcompany.com', 'example.com'],
      blockedAddresses: ['<EMAIL>', '<EMAIL>'],
      monitoringActive: true,
      timestamp: new Date().toISOString()
    };
    
    res.json(status);
  } catch (error) {
    console.error('Error getting email status:', error);
    res.status(500).json({ error: 'Failed to get email status' });
  }
});

export default router;