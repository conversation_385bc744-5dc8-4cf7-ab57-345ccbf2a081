import { Router } from 'express';
import { z } from 'zod';
import { db } from '../db';
import { authUsers, organizations, insertAuthUserSchema, insertOrganizationSchema } from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import { hashPassword, verifyPassword, generateToken, generateRefreshToken, verifyRefreshToken, authenticateToken, AuthenticatedRequest } from '../auth';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';

const router = Router();

// Validation schemas
const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

const registerSchema = z.object({
  email: z.string().email(),
  fullName: z.string().min(2),
  password: z.string().min(8),
  organizationId: z.string().uuid(),
});

const createOrganizationSchema = z.object({
  name: z.string().min(2),
  domain: z.string().optional(),
  adminEmail: z.string().email(),
  adminName: z.string().min(2),
  adminPassword: z.string().min(8),
});

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: User login
 *     description: Authenticate user and return JWT token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 8
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 token:
 *                   type: string
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       401:
 *         description: Invalid credentials or account issues
 *       400:
 *         description: Validation error
 *       500:
 *         description: Internal server error
 */
router.post('/login', async (req, res) => {
  try {
    const { email, password } = loginSchema.parse(req.body);

    // Find user with organization info
    const userResult = await db
      .select({
        id: authUsers.id,
        email: authUsers.email,
        fullName: authUsers.fullName,
        hashedPassword: authUsers.hashedPassword,
        role: authUsers.role,
        organizationId: authUsers.organizationId,
        isActive: authUsers.isActive,
        isApproved: authUsers.isApproved,
        organizationName: organizations.name,
      })
      .from(authUsers)
      .leftJoin(organizations, eq(authUsers.organizationId, organizations.id))
      .where(eq(authUsers.email, email))
      .limit(1);

    if (!userResult.length) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    const user = userResult[0];

    if (!user.isActive) {
      return res.status(401).json({ error: 'Account is inactive' });
    }

    if (!user.isApproved) {
      return res.status(401).json({ error: 'Account pending approval' });
    }

    // Verify password using bcrypt directly
    const passwordMatch = await bcrypt.compare(password, user.hashedPassword);
    if (!passwordMatch) {
      console.log('Password verification failed for user:', email);
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Store user session
    (req as any).session.userId = user.id;
    (req as any).session.organizationId = user.organizationId;
    (req as any).session.role = user.role;

    // Generate JWT tokens
    const token = generateToken({
      userId: user.id,
      email: user.email,
      organizationId: user.organizationId,
    });

    const refreshToken = generateRefreshToken({
      userId: user.id,
      email: user.email,
      organizationId: user.organizationId,
    });

    // Store refresh token in session for security
    (req as any).session.refreshToken = refreshToken;

    res.json({
      access_token: token,
      refresh_token: refreshToken,
      token_type: 'Bearer',
      expires_in: process.env.ACCESS_TOKEN_EXPIRES_IN || '15m',
      user: {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        role: user.role,
        organizationId: user.organizationId,
        organizationName: user.organizationName,
        isActive: user.isActive,
        isApproved: user.isApproved,
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(400).json({ error: 'Invalid request data' });
  }
});

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register new user
 *     description: Register a new user within an existing organization (requires admin approval)
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - fullName
 *               - password
 *               - organizationId
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: User's email address
 *               fullName:
 *                 type: string
 *                 minLength: 2
 *                 description: User's full name
 *               password:
 *                 type: string
 *                 minLength: 8
 *                 description: User's password (minimum 8 characters)
 *               organizationId:
 *                 type: string
 *                 format: uuid
 *                 description: ID of the organization to join
 *             example:
 *               email: "<EMAIL>"
 *               fullName: "John Doe"
 *               password: "securepassword123"
 *               organizationId: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       201:
 *         description: User registered successfully (pending approval)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "User registered successfully. Awaiting admin approval."
 *                 user:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     email:
 *                       type: string
 *                       format: email
 *                     full_name:
 *                       type: string
 *                     role:
 *                       type: string
 *                       example: "recruiter"
 *                     organization_id:
 *                       type: string
 *                       format: uuid
 *                     is_approved:
 *                       type: boolean
 *                       example: false
 *       400:
 *         description: Validation error or user already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   enum: ["Organization not found", "User already exists", "Invalid request data"]
 *       500:
 *         description: Internal server error
 */
router.post('/register', async (req, res) => {
  try {
    const { email, fullName, password, organizationId } = registerSchema.parse(req.body);

    // Check if organization exists
    const org = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, organizationId))
      .limit(1);

    if (!org.length) {
      return res.status(400).json({ error: 'Organization not found' });
    }

    // Check if user already exists
    const existingUser = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.email, email))
      .limit(1);

    if (existingUser.length) {
      return res.status(400).json({ error: 'User already exists' });
    }

    // Create new user
    const hashedPassword = hashPassword(password);
    const newUser = await db
      .insert(authUsers)
      .values({
        email,
        fullName,
        hashedPassword,
        organizationId,
        role: 'recruiter', // Default role
        isApproved: false, // Requires admin approval
      })
      .returning();

    res.status(201).json({
      message: 'User registered successfully. Awaiting admin approval.',
      user: {
        id: newUser[0].id,
        email: newUser[0].email,
        fullName: newUser[0].fullName,
        role: newUser[0].role,
        organizationId: newUser[0].organizationId,
        isApproved: newUser[0].isApproved,
      },
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(400).json({ error: 'Invalid request data' });
  }
});

/**
 * @swagger
 * /api/auth/organizations:
 *   post:
 *     summary: Create new organization
 *     description: Create a new organization with an admin user. This endpoint sets up a complete multi-tenant environment with the first admin user automatically approved and logged in.
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - adminEmail
 *               - adminName
 *               - adminPassword
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 description: Organization name
 *                 example: "Acme Corporation"
 *               domain:
 *                 type: string
 *                 description: Organization domain (optional, must be unique)
 *                 example: "acme.com"
 *               adminEmail:
 *                 type: string
 *                 format: email
 *                 description: Admin user's email address
 *                 example: "<EMAIL>"
 *               adminName:
 *                 type: string
 *                 minLength: 2
 *                 description: Admin user's full name
 *                 example: "Jane Smith"
 *               adminPassword:
 *                 type: string
 *                 minLength: 8
 *                 description: Admin user's password (minimum 8 characters)
 *                 example: "adminpassword123"
 *             example:
 *               name: "Acme Corporation"
 *               domain: "acme.com"
 *               adminEmail: "<EMAIL>"
 *               adminName: "Jane Smith"
 *               adminPassword: "adminpassword123"
 *     responses:
 *       201:
 *         description: Organization and admin user created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 access_token:
 *                   type: string
 *                   description: JWT token for immediate login
 *                 user:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     email:
 *                       type: string
 *                       format: email
 *                     full_name:
 *                       type: string
 *                     role:
 *                       type: string
 *                       example: "admin"
 *                     organization_id:
 *                       type: string
 *                       format: uuid
 *                     is_approved:
 *                       type: boolean
 *                       example: true
 *                 organization:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     name:
 *                       type: string
 *                     domain:
 *                       type: string
 *                       nullable: true
 *               example:
 *                 access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                 user:
 *                   id: "123e4567-e89b-12d3-a456-************"
 *                   email: "<EMAIL>"
 *                   full_name: "Jane Smith"
 *                   role: "admin"
 *                   organization_id: "987fcdeb-51d2-43a1-b789-012345678901"
 *                   is_approved: true
 *                 organization:
 *                   id: "987fcdeb-51d2-43a1-b789-012345678901"
 *                   name: "Acme Corporation"
 *                   domain: "acme.com"
 *       400:
 *         description: Validation error or conflicts
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   enum: ["Organization domain already exists", "Admin email already in use", "Invalid request data"]
 *       500:
 *         description: Internal server error
 */
router.post('/organizations', async (req, res) => {
  try {
    const { name, domain, adminEmail, adminName, adminPassword } = createOrganizationSchema.parse(req.body);

    // Check if organization or admin already exists
    const existingOrg = domain ? await db
      .select()
      .from(organizations)
      .where(eq(organizations.domain, domain))
      .limit(1) : [];

    const existingUser = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.email, adminEmail))
      .limit(1);

    if (existingOrg.length) {
      return res.status(400).json({ error: 'Organization domain already exists' });
    }

    if (existingUser.length) {
      return res.status(400).json({ error: 'Admin email already in use' });
    }

    // Create organization
    const newOrg = await db
      .insert(organizations)
      .values({
        name,
        domain,
      })
      .returning();

    // Create admin user
    const hashedPassword = hashPassword(adminPassword);
    const adminUser = await db
      .insert(authUsers)
      .values({
        email: adminEmail,
        fullName: adminName,
        hashedPassword,
        organizationId: newOrg[0].id,
        role: 'admin',
        isApproved: true, // Auto-approve organization admins
      })
      .returning();

    // Generate token for auto-login
    const tokenPayload = {
      userId: adminUser[0].id,
      email: adminUser[0].email,
      organizationId: newOrg[0].id,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    };
    const token = jwt.sign(tokenPayload, process.env.JWT_SECRET || 'ats-multi-tenant-secret');

    res.status(201).json({
      access_token: token,
      user: {
        id: adminUser[0].id,
        email: adminUser[0].email,
        fullName: adminUser[0].fullName,
        role: adminUser[0].role,
        organizationId: newOrg[0].id,
        isApproved: adminUser[0].isApproved,
      },
      organization: {
        id: newOrg[0].id,
        name: newOrg[0].name,
        domain: newOrg[0].domain,
      },
    });
  } catch (error) {
    console.error('Organization creation error:', error);
    res.status(400).json({ error: 'Invalid request data' });
  }
});

/**
 * @swagger
 * /api/auth/user:
 *   get:
 *     summary: Get current authenticated user
 *     description: Retrieve information about the currently authenticated user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current user information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized - invalid or missing token
 */
router.get('/user', authenticateToken, async (req: AuthenticatedRequest, res) => {
  res.json({
    user: req.user,
  });
});

/**
 * @swagger
 * /api/auth/users/{userId}/approve:
 *   post:
 *     summary: Approve user registration
 *     description: Approve a pending user registration within the organization (admin only)
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         required: true
 *         description: ID of the user to approve
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: User approved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "User approved successfully"
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Admin access required
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Admin access required"
 *       404:
 *         description: User not found in organization
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "User not found"
 *       500:
 *         description: Internal server error
 */
router.post('/users/:userId/approve', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    if (req.user?.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    const { userId } = req.params;

    // Update user approval status (only within same organization)
    const result = await db
      .update(authUsers)
      .set({ isApproved: true })
      .where(and(
        eq(authUsers.id, userId),
        eq(authUsers.organizationId, req.user.organizationId)
      ))
      .returning();

    if (!result.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ message: 'User approved successfully' });
  } catch (error) {
    console.error('User approval error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @swagger
 * /api/auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     description: Get a new access token using refresh token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refresh_token
 *             properties:
 *               refresh_token:
 *                 type: string
 *                 description: Valid refresh token
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 access_token:
 *                   type: string
 *                 refresh_token:
 *                   type: string
 *                 token_type:
 *                   type: string
 *                 expires_in:
 *                   type: string
 *       401:
 *         description: Invalid or expired refresh token
 *       400:
 *         description: Missing refresh token
 */
router.post('/refresh', async (req, res) => {
  try {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      return res.status(400).json({ 
        error: 'Refresh token required',
        code: 'MISSING_REFRESH_TOKEN' 
      });
    }

    // Verify refresh token
    const decoded = verifyRefreshToken(refresh_token);
    
    // Get user from database to ensure they still exist and are active
    const userResult = await db
      .select({
        id: authUsers.id,
        email: authUsers.email,
        fullName: authUsers.fullName,
        role: authUsers.role,
        organizationId: authUsers.organizationId,
        isActive: authUsers.isActive,
        isApproved: authUsers.isApproved,
        organizationName: organizations.name,
      })
      .from(authUsers)
      .leftJoin(organizations, eq(authUsers.organizationId, organizations.id))
      .where(eq(authUsers.id, decoded.userId))
      .limit(1);

    if (!userResult.length) {
      return res.status(401).json({ 
        error: 'User not found',
        code: 'USER_NOT_FOUND' 
      });
    }

    const user = userResult[0];

    if (!user.isActive) {
      return res.status(401).json({ 
        error: 'Account is inactive',
        code: 'ACCOUNT_INACTIVE' 
      });
    }

    if (!user.isApproved) {
      return res.status(401).json({ 
        error: 'Account pending approval',
        code: 'ACCOUNT_PENDING' 
      });
    }

    // Generate new tokens
    const newAccessToken = generateToken({
      userId: user.id,
      email: user.email,
      organizationId: user.organizationId,
    });

    const newRefreshToken = generateRefreshToken({
      userId: user.id,
      email: user.email,
      organizationId: user.organizationId,
    });

    // Update session if it exists
    if ((req as any).session) {
      (req as any).session.refreshToken = newRefreshToken;
      (req as any).session.userId = user.id;
      (req as any).session.organizationId = user.organizationId;
      (req as any).session.role = user.role;
    }

    res.json({
      access_token: newAccessToken,
      refresh_token: newRefreshToken,
      token_type: 'Bearer',
      expires_in: process.env.ACCESS_TOKEN_EXPIRES_IN || '15m',
      user: {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        role: user.role,
        organizationId: user.organizationId,
        organizationName: user.organizationName,
        isActive: user.isActive,
        isApproved: user.isApproved,
      },
    });

  } catch (error) {
    console.error('Token refresh error:', error);
    
    if (error.message?.includes('expired') || error.message?.includes('invalid')) {
      return res.status(401).json({ 
        error: 'Invalid or expired refresh token',
        code: 'INVALID_REFRESH_TOKEN',
        message: 'Please log in again.'
      });
    }
    
    res.status(500).json({ 
      error: 'Internal server error',
      code: 'REFRESH_ERROR' 
    });
  }
});

/**
 * @swagger
 * /api/auth/validate:
 *   get:
 *     summary: Validate current session
 *     description: Check if the current session/token is valid and return user info
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Session is valid
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 valid:
 *                   type: boolean
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *       401:
 *         description: Session invalid or expired
 */
router.get('/validate', authenticateToken, async (req: AuthenticatedRequest, res) => {
  // If we reach here, the token is valid (middleware passed)
  res.json({
    valid: true,
    user: req.user
  });
});

export default router;