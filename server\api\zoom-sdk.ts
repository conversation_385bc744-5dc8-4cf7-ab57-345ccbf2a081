import { Router } from 'express';
import { authenticateToken } from '../auth';
import { zoomVideoSDKService } from '../services/zoomVideoSDKService';
import { db } from '../db';
import { interviewsV2, candidates, authUsers } from '@shared/schema';
import { eq, and } from 'drizzle-orm';

const router = Router();

/**
 * @swagger
 * /zoom/token:
 *   get:
 *     summary: Generate Zoom Video SDK JWT token
 *     description: Generate JWT token for Zoom Video SDK access with role-based permissions
 *     tags: [Zoom SDK]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: role
 *         required: true
 *         schema:
 *           type: string
 *           enum: [candidate, host]
 *         description: Role for the token (candidate or host)
 *       - in: query
 *         name: interviewId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Interview ID
 *     responses:
 *       200:
 *         description: JWT token generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 token:
 *                   type: string
 *                 sessionName:
 *                   type: string
 *                 sessionKey:
 *                   type: string
 *                 expiresAt:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Missing or invalid parameters
 *       404:
 *         description: Interview not found
 *       500:
 *         description: Token generation failed
 */
// Public endpoint for candidate token generation (no authentication required)
router.get('/candidate-token/:interviewId', async (req, res) => {
  try {
    const { interviewId } = req.params;
    console.log('🎥 Generating candidate token for interview:', interviewId);

    if (!interviewId) {
      return res.status(400).json({
        success: false,
        error: 'Interview ID is required'
      });
    }

    // Check if Zoom SDK is configured
    if (!zoomVideoSDKService.isConfigured()) {
      console.error('❌ Zoom Video SDK not configured');
      return res.status(500).json({
        success: false,
        error: 'Zoom Video SDK not configured'
      });
    }

    // Get interview details (no organization check for candidates)
    const [interview] = await db
      .select()
      .from(interviewsV2)
      .leftJoin(candidates, eq(interviewsV2.candidateId, candidates.id))
      .where(eq(interviewsV2.id, interviewId))
      .limit(1);

    if (!interview) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    // Generate or get session
    let sessionName = interview.interviews_v2.roomOrMeetingId;
    if (!sessionName) {
      sessionName = zoomVideoSDKService.generateInterviewSessionName(interview.interviews_v2.id);

      // Update interview with session name
      await db
        .update(interviewsV2)
        .set({ roomOrMeetingId: sessionName })
        .where(eq(interviewsV2.id, interview.interviews_v2.id));
    }

    // Create session if it doesn't exist
    let session = zoomVideoSDKService.getSession(sessionName);
    if (!session) {
      session = zoomVideoSDKService.createSession(sessionName);
    }

    // Generate candidate token
    const token = zoomVideoSDKService.generateSDKToken({
      role: 'participant',
      sessionName,
      userIdentity: interview.interviews_v2.candidateId,
      sessionKey: session.sessionKey
    });

    console.log('✅ Generated candidate token successfully');

    res.json({
      success: true,
      token,
      sessionName,
      sessionKey: session.sessionKey,
      expiresAt: session.expiresAt.toISOString()
    });

  } catch (error) {
    console.error('❌ Error generating candidate token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate token'
    });
  }
});

router.get('/token', async (req, res) => {
  try {
    const { role, interviewId } = req.query;

    console.log('🎥 Old token endpoint called with:', { role, interviewId, hasAuth: !!req.user });

    // If this is a candidate request without authentication, handle it directly
    if (role === 'candidate' && interviewId && !req.user) {
      console.log('🔄 Handling candidate request in old endpoint (fallback)');

      // Check if Zoom SDK is configured
      if (!zoomVideoSDKService.isConfigured()) {
        console.error('❌ Zoom Video SDK not configured');
        return res.status(500).json({
          success: false,
          error: 'Zoom Video SDK not configured'
        });
      }

      // Get interview details (no organization check for candidates)
      const [interview] = await db
        .select()
        .from(interviewsV2)
        .leftJoin(candidates, eq(interviewsV2.candidateId, candidates.id))
        .where(eq(interviewsV2.id, interviewId as string))
        .limit(1);

      if (!interview || !interview.interviews_v2) {
        return res.status(404).json({
          success: false,
          error: 'Interview not found'
        });
      }

      // Generate or get session
      let sessionName = interview.interviews_v2.roomOrMeetingId;
      if (!sessionName) {
        sessionName = zoomVideoSDKService.generateInterviewSessionName(interview.interviews_v2.id);

        // Update interview with session name
        await db
          .update(interviewsV2)
          .set({ roomOrMeetingId: sessionName })
          .where(eq(interviewsV2.id, interview.interviews_v2.id));
      }

      // Create session if it doesn't exist
      let session = zoomVideoSDKService.getSession(sessionName);
      if (!session) {
        session = zoomVideoSDKService.createSession(sessionName);
      }

      // Generate candidate token
      const token = zoomVideoSDKService.generateSDKToken({
        role: 'participant',
        sessionName,
        userIdentity: interview.interviews_v2.candidateId,
        sessionKey: session.sessionKey
      });

      console.log('✅ Generated candidate token successfully (fallback)');

      return res.json({
        success: true,
        token,
        sessionName,
        sessionKey: session.sessionKey,
        expiresAt: session.expiresAt.toISOString()
      });
    }

    // For authenticated requests, require authentication
    if (!req.user) {
      console.log('❌ Authentication required for non-candidate requests');
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const userId = req.user.id;

    if (!role || !interviewId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: role and interviewId'
      });
    }

    if (!['candidate', 'host'].includes(role as string)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid role. Must be "candidate" or "host"'
      });
    }

    // Get interview details
    const [interview] = await db
      .select()
      .from(interviewsV2)
      .where(eq(interviewsV2.id, interviewId as string))
      .limit(1);

    if (!interview) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    // Verify access permissions
    if (role === 'candidate') {
      // For candidates, verify they are the interview candidate
      const [candidate] = await db
        .select()
        .from(candidates)
        .where(eq(candidates.id, interview.candidateId))
        .limit(1);

      if (!candidate) {
        return res.status(404).json({
          success: false,
          error: 'Candidate not found'
        });
      }
    } else if (role === 'host') {
      // For hosts, verify they belong to the same organization
      const [user] = await db
        .select()
        .from(authUsers)
        .where(eq(authUsers.id, userId))
        .limit(1);

      if (!user || user.organizationId !== interview.organizationId) {
        return res.status(403).json({
          success: false,
          error: 'Access denied'
        });
      }
    }

    // Generate or get session
    let sessionName = interview.roomOrMeetingId;
    if (!sessionName) {
      sessionName = zoomVideoSDKService.generateInterviewSessionName(interview.id);
      
      // Update interview with session name
      await db
        .update(interviewsV2)
        .set({ roomOrMeetingId: sessionName })
        .where(eq(interviewsV2.id, interview.id));
    }

    // Create session if it doesn't exist
    let session = zoomVideoSDKService.getSession(sessionName);
    if (!session) {
      session = zoomVideoSDKService.createSession(sessionName);
    }

    // Generate token
    const token = zoomVideoSDKService.generateSDKToken({
      role: role === 'host' ? 'host' : 'participant',
      sessionName,
      userIdentity: role === 'candidate' ? interview.candidateId : userId,
      sessionKey: session.sessionKey
    });

    res.json({
      success: true,
      token,
      sessionName,
      sessionKey: session.sessionKey,
      expiresAt: session.expiresAt.toISOString()
    });

  } catch (error) {
    console.error('Error generating Zoom SDK token:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate token'
    });
  }
});

/**
 * @swagger
 * /zoom/session:
 *   post:
 *     summary: Create new Zoom Video SDK session
 *     description: Create a new video session for an interview
 *     tags: [Zoom SDK]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - interviewId
 *             properties:
 *               interviewId:
 *                 type: string
 *                 format: uuid
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Session created successfully
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Session creation failed
 */
router.post('/session', authenticateToken, async (req, res) => {
  try {
    const { interviewId, password } = req.body;
    const userId = req.user?.id;

    if (!interviewId) {
      return res.status(400).json({
        success: false,
        error: 'Interview ID is required'
      });
    }

    // Verify interview exists and user has access
    const [interview] = await db
      .select()
      .from(interviewsV2)
      .where(eq(interviewsV2.id, interviewId))
      .limit(1);

    if (!interview) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    // Verify user belongs to same organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || user.organizationId !== interview.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Generate session name
    const sessionName = zoomVideoSDKService.generateInterviewSessionName(interviewId);
    
    // Create session
    const session = zoomVideoSDKService.createSession(sessionName, password);

    // Update interview with session details
    await db
      .update(interviewsV2)
      .set({
        roomOrMeetingId: sessionName,
        sdkType: 'video'
      })
      .where(eq(interviewsV2.id, interviewId));

    res.json({
      success: true,
      sessionName: session.sessionName,
      sessionKey: session.sessionKey,
      createdAt: session.createdAt,
      expiresAt: session.expiresAt
    });

  } catch (error) {
    console.error('Error creating Zoom session:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create session'
    });
  }
});

/**
 * @swagger
 * /zoom/sessions:
 *   get:
 *     summary: Get active Zoom sessions
 *     description: List all active video sessions
 *     tags: [Zoom SDK]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Active sessions retrieved successfully
 */
router.get('/sessions', authenticateToken, async (req, res) => {
  try {
    const sessions = zoomVideoSDKService.getActiveSessions();
    
    res.json({
      success: true,
      sessions: sessions.map(session => ({
        sessionName: session.sessionName,
        createdAt: session.createdAt,
        expiresAt: session.expiresAt
      }))
    });

  } catch (error) {
    console.error('Error fetching Zoom sessions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch sessions'
    });
  }
});

export default router;
