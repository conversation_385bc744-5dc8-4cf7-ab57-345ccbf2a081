import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function addTestCandidates() {
  try {
    console.log('🚀 Adding test candidates for interview scheduling...');
    
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    const sql = neon(process.env.DATABASE_URL);
    
    // Get the first organization ID
    const organizations = await sql`SELECT id FROM organizations LIMIT 1`;
    if (organizations.length === 0) {
      throw new Error('No organizations found. Please create an organization first.');
    }
    
    const organizationId = organizations[0].id;
    console.log(`📋 Using organization ID: ${organizationId}`);
    
    // Test candidates data
    const testCandidates = [
      {
        email: '<EMAIL>',
        fullName: '<PERSON>',
        phone: '******-0101',
        location: 'San Francisco, CA',
        currentCompany: 'Tech Corp',
        currentPosition: 'Senior Software Engineer',
        skills: ['JavaScript', 'React', 'Node.js', 'Python'],
        experienceYears: 5,
        education: 'BS Computer Science',
        status: 'approved_for_interview'
      },
      {
        email: '<EMAIL>',
        fullName: '<PERSON>',
        phone: '******-0102',
        location: 'New York, NY',
        currentCompany: 'StartupXYZ',
        currentPosition: 'Full Stack Developer',
        skills: ['TypeScript', 'Angular', 'PostgreSQL', 'AWS'],
        experienceYears: 3,
        education: 'MS Software Engineering',
        status: 'approved_for_interview'
      },
      {
        email: '<EMAIL>',
        fullName: 'Mike Johnson',
        phone: '******-0103',
        location: 'Austin, TX',
        currentCompany: 'DevCorp',
        currentPosition: 'Frontend Developer',
        skills: ['Vue.js', 'CSS', 'HTML', 'JavaScript'],
        experienceYears: 2,
        education: 'BS Information Technology',
        status: 'approved_for_interview'
      },
      {
        email: '<EMAIL>',
        fullName: 'Sarah Wilson',
        phone: '******-0104',
        location: 'Seattle, WA',
        currentCompany: 'CloudTech',
        currentPosition: 'DevOps Engineer',
        skills: ['Docker', 'Kubernetes', 'Jenkins', 'Python'],
        experienceYears: 4,
        education: 'BS Computer Engineering',
        status: 'approved_for_interview'
      }
    ];
    
    // Insert test candidates
    for (const candidate of testCandidates) {
      try {
        // Check if candidate already exists
        const existing = await sql`
          SELECT id FROM candidates 
          WHERE email = ${candidate.email} AND organization_id = ${organizationId}
        `;
        
        if (existing.length > 0) {
          console.log(`⚠️  Candidate ${candidate.fullName} already exists, updating status...`);
          await sql`
            UPDATE candidates 
            SET status = ${candidate.status},
                current_position = ${candidate.currentPosition},
                current_company = ${candidate.currentCompany},
                skills = ${candidate.skills},
                experience_years = ${candidate.experienceYears},
                education = ${candidate.education},
                phone = ${candidate.phone},
                location = ${candidate.location}
            WHERE email = ${candidate.email} AND organization_id = ${organizationId}
          `;
        } else {
          console.log(`✅ Adding candidate: ${candidate.fullName}`);
          await sql`
            INSERT INTO candidates (
              email, full_name, phone, location, current_company, current_position,
              skills, experience_years, education, status, organization_id
            ) VALUES (
              ${candidate.email}, ${candidate.fullName}, ${candidate.phone}, 
              ${candidate.location}, ${candidate.currentCompany}, ${candidate.currentPosition},
              ${candidate.skills}, ${candidate.experienceYears}, ${candidate.education}, 
              ${candidate.status}, ${organizationId}
            )
          `;
        }
      } catch (error) {
        console.error(`❌ Error processing candidate ${candidate.fullName}:`, error);
      }
    }
    
    console.log('🎉 Test candidates added successfully!');
    console.log('📋 Summary:');
    console.log(`   - ${testCandidates.length} candidates with "approved_for_interview" status`);
    console.log('   - Ready for interview scheduling');
    console.log('   - Each candidate has realistic job positions for auto-fill testing');
    
  } catch (error) {
    console.error('❌ Error adding test candidates:', error);
    process.exit(1);
  }
}

addTestCandidates();
