#!/bin/bash

# Database Backup Script for HRMS Platform
# This script creates automated backups of the PostgreSQL database

set -e

# Configuration
BACKUP_DIR="/backups"
DB_HOST="postgres"
DB_NAME="hrms_production"
DB_USER="hrms_user"
RETENTION_DAYS=30

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Generate backup filename with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/hrms_backup_$TIMESTAMP.sql"
COMPRESSED_FILE="$BACKUP_FILE.gz"

echo "🗄️  Starting database backup..."

# Create database backup
if pg_dump -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" > "$BACKUP_FILE"; then
    print_status "Database backup created: $BACKUP_FILE"
    
    # Compress the backup
    if gzip "$BACKUP_FILE"; then
        print_status "Backup compressed: $COMPRESSED_FILE"
        
        # Get file size
        BACKUP_SIZE=$(du -h "$COMPRESSED_FILE" | cut -f1)
        print_status "Backup size: $BACKUP_SIZE"
    else
        print_error "Failed to compress backup"
        exit 1
    fi
else
    print_error "Failed to create database backup"
    exit 1
fi

# Clean up old backups
echo "🧹 Cleaning up old backups..."
find "$BACKUP_DIR" -name "hrms_backup_*.sql.gz" -type f -mtime +$RETENTION_DAYS -delete

REMAINING_BACKUPS=$(find "$BACKUP_DIR" -name "hrms_backup_*.sql.gz" -type f | wc -l)
print_status "Cleanup completed. $REMAINING_BACKUPS backups remaining."

# Verify backup integrity
echo "🔍 Verifying backup integrity..."
if gzip -t "$COMPRESSED_FILE"; then
    print_status "Backup integrity verified"
else
    print_error "Backup integrity check failed"
    exit 1
fi

# Log backup completion
echo "$(date): Backup completed successfully - $COMPRESSED_FILE" >> "$BACKUP_DIR/backup.log"

print_status "Backup process completed successfully!"

# Optional: Upload to cloud storage (uncomment and configure as needed)
# echo "☁️  Uploading to cloud storage..."
# aws s3 cp "$COMPRESSED_FILE" s3://your-backup-bucket/database-backups/
# print_status "Backup uploaded to cloud storage"
