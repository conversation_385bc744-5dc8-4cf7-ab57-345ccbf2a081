import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testInterviewCreation() {
  try {
    console.log('🧪 Testing interview creation flow...');
    
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    const sql = neon(process.env.DATABASE_URL);
    
    // 1. Check if Suresh candidate exists
    console.log('1️⃣ Checking for Suresh candidate...');
    const candidates = await sql`
      SELECT id, full_name, email, status, current_position, organization_id
      FROM candidates 
      WHERE email = '<EMAIL>'
    `;
    
    if (candidates.length === 0) {
      console.log('❌ Suresh candidate not found!');
      return;
    }
    
    const candidate = candidates[0];
    console.log('✅ Found candidate:', {
      id: candidate.id,
      name: candidate.full_name,
      email: candidate.email,
      status: candidate.status,
      position: candidate.current_position,
      orgId: candidate.organization_id
    });
    
    // 2. Check organization and users
    console.log('2️⃣ Checking organization and users...');
    const users = await sql`
      SELECT id, email, role, organization_id
      FROM auth_users 
      WHERE organization_id = ${candidate.organization_id}
      LIMIT 5
    `;
    
    console.log(`✅ Found ${users.length} users in organization:`, users.map(u => ({
      id: u.id,
      email: u.email,
      role: u.role
    })));
    
    // 3. Check existing interviews
    console.log('3️⃣ Checking existing interviews...');
    const existingInterviews = await sql`
      SELECT id, candidate_id, role, scheduled_at, status, created_at
      FROM interviews_v2 
      WHERE organization_id = ${candidate.organization_id}
      ORDER BY created_at DESC
      LIMIT 5
    `;
    
    console.log(`📋 Found ${existingInterviews.length} existing interviews:`, existingInterviews);
    
    // 4. Check agent profiles
    console.log('4️⃣ Checking agent profiles...');
    const agentProfiles = await sql`
      SELECT id, name, organization_id
      FROM agent_profiles 
      WHERE organization_id = ${candidate.organization_id}
      LIMIT 3
    `;
    
    console.log(`🤖 Found ${agentProfiles.length} agent profiles:`, agentProfiles);
    
    // 5. Test payload structure
    console.log('5️⃣ Testing payload structure...');
    const testPayload = {
      candidateId: candidate.id,
      role: candidate.current_position || 'Software Engineer',
      scheduledAt: new Date(Date.now() + 3 * 60 * 1000).toISOString(),
      durationMin: 60,
      agentProfileId: null,
      notes: 'Test interview created via script'
    };
    
    console.log('📝 Test payload:', JSON.stringify(testPayload, null, 2));
    
    // 6. Validate datetime format
    console.log('6️⃣ Validating datetime format...');
    const scheduledDate = new Date(testPayload.scheduledAt);
    console.log('🕐 Scheduled date:', {
      iso: testPayload.scheduledAt,
      parsed: scheduledDate,
      isValid: !isNaN(scheduledDate.getTime()),
      minutesFromNow: Math.round((scheduledDate.getTime() - Date.now()) / 60000)
    });
    
    console.log('✅ All checks completed! Ready for testing.');
    console.log('');
    console.log('🎯 Next steps:');
    console.log('1. Open browser console and check for frontend logs');
    console.log('2. Select "Suresh Arumugam" in the dropdown');
    console.log('3. Click "Quick Test (3 min)" button');
    console.log('4. Click "Schedule Interview" button');
    console.log('5. Watch server logs for API call');
    
  } catch (error) {
    console.error('❌ Error during testing:', error);
    process.exit(1);
  }
}

testInterviewCreation();
