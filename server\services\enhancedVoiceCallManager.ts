import twilio from 'twilio';
const VoiceResponse = twilio.twiml.VoiceResponse;
import { db } from '../db';
import { voiceCalls, voiceCallNotes, candidates, jobPostings, organizations } from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import { elevenLabsService } from './elevenlabsService';
import { GoogleSpeechService } from './googleSpeechService';

interface CallContext {
  candidateId: string;
  organizationId: string;
  callPurpose: 'screening' | 'interview_scheduling' | 'follow_up' | 'offer_discussion';
  jobPostingId?: string;
  voiceCallId?: string;
  conversationState?: any;
}

interface EnhancedCallResponse {
  twiml: string;
  callContext: CallContext;
  action: 'continue' | 'transfer' | 'schedule' | 'end_call';
  audioUrl?: string;
}

export class EnhancedVoiceCallManager {
  private elevenLabsService: typeof elevenLabsService;
  private speechService: GoogleSpeechService;
  private activeCalls: Map<string, CallContext> = new Map();

  constructor() {
    this.elevenLabsService = elevenLabsService;
    this.speechService = new GoogleSpeechService();
  }

  // Initialize an outgoing call with conversational AI
  async initiateConversationalCall(
    candidateId: string,
    organizationId: string,
    callPurpose: 'screening' | 'interview_scheduling' | 'follow_up' | 'offer_discussion',
    options: {
      phoneNumber?: string;
      jobPostingId?: string;
      personality?: 'professional' | 'friendly' | 'warm' | 'authoritative';
    } = {}
  ): Promise<EnhancedCallResponse> {
    try {
      // Get candidate information
      const [candidate] = await db
        .select()
        .from(candidates)
        .where(and(
          eq(candidates.id, candidateId),
          eq(candidates.organizationId, organizationId)
        ));

      if (!candidate) {
        throw new Error('Candidate not found');
      }

      // Get job posting information if provided
      let jobPosting = null;
      if (options.jobPostingId) {
        [jobPosting] = await db
          .select()
          .from(jobPostings)
          .where(and(
            eq(jobPostings.id, options.jobPostingId),
            eq(jobPostings.organizationId, organizationId)
          ));
        
        console.log('🎯 ENHANCED MGR: Job posting fetched:', {
          jobPostingId: options.jobPostingId,
          found: !!jobPosting,
          title: jobPosting?.title || 'NONE',
          description: jobPosting?.description?.substring(0, 100) || 'NONE',
          responsibilities: jobPosting?.responsibilities?.substring(0, 100) || 'NONE'
        });
      } else {
        console.log('🎯 ENHANCED MGR: No jobPostingId provided');
      }

      // Get organization information for complete context
      const [organization] = await db
        .select()
        .from(organizations)
        .where(eq(organizations.id, organizationId));

      // Create voice call record
      const [voiceCall] = await db
        .insert(voiceCalls)
        .values({
          candidateId,
          organizationId,
          jobPostingId: options.jobPostingId,
          phoneNumber: candidate.phone || options.phoneNumber || '',
          scheduledAt: new Date(),
          callPurpose: callPurpose,
          status: 'scheduled'
        })
        .returning();

      // Initialize conversation state with complete context
      console.log('🎯 ENHANCED MGR: Creating conversation state with:', {
        candidateName: candidate.fullName,
        jobTitle: jobPosting?.title || 'NONE',
        orgName: organization?.name || 'NONE',
        callPurpose
      });
      
      // Create conversation state (simplified for now)
      const conversationState = {
        candidate,
        jobPosting,
        organization,
        callPurpose,
        messages: []
      };

      // Generate opening message using ElevenLabs service
      const openingText = `Hello ${candidate.fullName}, this is an automated call from ${organization?.name || 'our company'}. I'm calling regarding the ${jobPosting?.title || 'position'} you applied for. Do you have a few minutes to talk?`;

      const openingResponse = await this.elevenLabsService.generateSpeech(openingText, {
        voiceId: 'default',
        stability: 0.7,
        similarityBoost: 0.8
      });

      if (!openingResponse.success) {
        throw new Error('Failed to generate opening message');
      }

      // Create call context
      const callContext: CallContext = {
        candidateId,
        organizationId,
        callPurpose,
        jobPostingId: options.jobPostingId,
        voiceCallId: voiceCall.id,
        conversationState: openingResponse.conversationState
      };

      // Store active call context
      this.activeCalls.set(voiceCall.id, callContext);

      // Generate TwiML for the opening
      const twiml = this.generateConversationalTwiML(
        openingResponse.response!,
        openingResponse.audioUrl,
        voiceCall.id
      );

      return {
        twiml,
        callContext,
        action: 'continue',
        audioUrl: openingResponse.audioUrl
      };

    } catch (error) {
      console.error('❌ Error initiating conversational call:', error);
      throw error;
    }
  }

  // Handle incoming speech and generate conversational response
  async handleConversationalInput(
    callSid: string,
    speechText: string,
    voiceCallId?: string
  ): Promise<EnhancedCallResponse> {
    try {
      // Get call context
      const callContext = voiceCallId ? this.activeCalls.get(voiceCallId) : null;
      
      if (!callContext) {
        throw new Error('Call context not found');
      }

      // Log the user input
      await this.logCallNote(callContext.voiceCallId!, 'user_input', speechText);

      // Generate conversational response using ElevenLabs service
      const responseText = await this.elevenLabsService.generateConversationalResponse(
        speechText,
        callContext.conversationState
      );

      const aiResponse = await this.elevenLabsService.generateSpeech(responseText, {
        voiceId: 'default',
        stability: 0.7,
        similarityBoost: 0.8
        }
      );

      if (!aiResponse.success) {
        throw new Error('Failed to generate AI response');
      }

      // Update call context
      callContext.conversationState = aiResponse.conversationState;
      this.activeCalls.set(callContext.voiceCallId!, callContext);

      // Log the AI response
      await this.logCallNote(callContext.voiceCallId!, 'ai_response', aiResponse.response!);

      // Generate TwiML based on next action
      let twiml: string;
      
      switch (aiResponse.nextAction) {
        case 'schedule':
          twiml = this.generateSchedulingTwiML(aiResponse.response!, aiResponse.audioUrl);
          break;
        case 'transfer':
          twiml = this.generateTransferTwiML(aiResponse.response!, aiResponse.audioUrl);
          break;
        case 'end_call':
          twiml = this.generateEndCallTwiML(aiResponse.response!, aiResponse.audioUrl);
          await this.finalizeCall(callContext.voiceCallId!, { status: 'completed', endReason: 'ai_determined_end' });
          break;
        default:
          twiml = this.generateConversationalTwiML(
            aiResponse.response!,
            aiResponse.audioUrl,
            callContext.voiceCallId!
          );
      }

      return {
        twiml,
        callContext,
        action: aiResponse.nextAction!,
        audioUrl: aiResponse.audioUrl
      };

    } catch (error) {
      console.error('❌ Error handling conversational input:', error);
      
      // Generate fallback TwiML
      const fallbackTwiml = this.generateFallbackTwiML(
        "I apologize, but I'm having some technical difficulties. Let me transfer you to a human representative."
      );

      return {
        twiml: fallbackTwiml,
        callContext: this.activeCalls.get(voiceCallId!) || {} as CallContext,
        action: 'transfer'
      };
    }
  }

  // Generate TwiML for conversational responses
  private generateConversationalTwiML(
    responseText: string,
    audioUrl?: string,
    voiceCallId?: string
  ): string {
    const twiml = new VoiceResponse();

    if (audioUrl) {
      // Use ElevenLabs generated audio
      twiml.play(audioUrl);
    } else {
      // Fallback to Twilio TTS with enhanced settings
      twiml.say({
        voice: 'Polly.Joanna-Neural',
        language: 'en-US'
      }, responseText);
    }

    // Add gather for continuous conversation
    const gather = twiml.gather({
      input: ['speech'],
      speechTimeout: '3',
      speechModel: 'experimental_conversations',
      enhanced: true,
      action: `/api/voice-agent/conversational-input${voiceCallId ? `?voiceCallId=${voiceCallId}` : ''}`,
      method: 'POST',
      language: 'en-US'
    });

    // Add silence detection
    twiml.pause({ length: 2 });
    twiml.say({
      voice: 'Polly.Joanna-Neural'
    }, "I'm still here if you'd like to continue our conversation.");

    return twiml.toString();
  }

  // Generate TwiML for scheduling actions
  private generateSchedulingTwiML(responseText: string, audioUrl?: string): string {
    const twiml = new VoiceResponse();

    if (audioUrl) {
      twiml.play(audioUrl);
    } else {
      twiml.say({
        voice: 'Polly.Joanna-Neural'
      }, responseText);
    }

    // Add calendar integration hook
    twiml.say({
      voice: 'Polly.Joanna-Neural'
    }, "I'm sending you a calendar invite with available time slots. You should receive it within the next few minutes.");

    twiml.hangup();
    return twiml.toString();
  }

  // Generate TwiML for call transfers
  private generateTransferTwiML(responseText: string, audioUrl?: string): string {
    const twiml = new VoiceResponse();

    if (audioUrl) {
      twiml.play(audioUrl);
    } else {
      twiml.say({
        voice: 'Polly.Joanna-Neural'
      }, responseText);
    }

    // Transfer to human (would need to be configured with actual number)
    twiml.say({
      voice: 'Polly.Joanna-Neural'
    }, "Please hold while I connect you with our hiring manager.");

    // Uncomment and configure with actual transfer number
    // twiml.dial(process.env.TRANSFER_PHONE_NUMBER);
    
    twiml.hangup();
    return twiml.toString();
  }

  // Generate TwiML for ending calls
  private generateEndCallTwiML(responseText: string, audioUrl?: string): string {
    const twiml = new VoiceResponse();

    if (audioUrl) {
      twiml.play(audioUrl);
    } else {
      twiml.say({
        voice: 'Polly.Joanna-Neural'
      }, responseText);
    }

    twiml.say({
      voice: 'Polly.Joanna-Neural'
    }, "Thank you for your time today. Have a wonderful day!");

    twiml.hangup();
    return twiml.toString();
  }

  // Generate fallback TwiML for errors
  private generateFallbackTwiML(message: string): string {
    const twiml = new VoiceResponse();

    twiml.say({
      voice: 'Polly.Joanna-Neural'
    }, message);

    twiml.hangup();
    return twiml.toString();
  }

  // Log call notes
  private async logCallNote(
    voiceCallId: string,
    noteType: 'user_input' | 'ai_response' | 'system_action',
    content: string
  ): Promise<void> {
    try {
      await db.insert(voiceCallNotes).values({
        callId: voiceCallId,
        organizationId: '', // Will be set from call context
        noteType: noteType,
        content: content,
        timestamp: new Date()
      });
    } catch (error) {
      console.error('❌ Error logging call note:', error);
    }
  }

  // End call and cleanup with proper transcription aggregation
  private async endCall(voiceCallId: string, status: 'completed' | 'failed' | 'no_answer'): Promise<void> {
    try {
      // 🔍 CRITICAL FIX: Aggregate conversation notes into final transcription
      const conversationNotes = await db
        .select()
        .from(voiceCallNotes)
        .where(eq(voiceCallNotes.callId, voiceCallId))
        .orderBy(voiceCallNotes.timestamp);

      // Build comprehensive transcription from all conversation notes
      const fullTranscription = conversationNotes
        .map(note => {
          const timestamp = new Date(note.timestamp).toLocaleTimeString();
          const speaker = note.noteType === 'user_input' ? 'Candidate' : 
                         note.noteType === 'ai_response' ? 'Sarah (AI)' : 'System';
          return `[${timestamp}] ${speaker}: ${note.content}`;
        })
        .join('\n');

      console.log(`📝 Aggregated ${conversationNotes.length} notes into transcription (${fullTranscription.length} chars)`);

      // Update call record with aggregated transcription
      const [updatedCall] = await db
        .update(voiceCalls)
        .set({
          status: status === 'no_answer' ? 'failed' : status,
          endedAt: new Date(),
          transcription: fullTranscription || null,
          durationSeconds: this.calculateCallDuration(voiceCallId)
        })
        .where(eq(voiceCalls.id, voiceCallId))
        .returning();

      // Remove from active calls
      this.activeCalls.delete(voiceCallId);

      console.log(`📞 Call ${voiceCallId} ended with status: ${status}`);

      // 🚀 FIXED: Trigger async call summarization if call completed successfully
      // Remove transcription requirement since we now always populate it
      if (status === 'completed' && fullTranscription && fullTranscription.length > 10) {
        console.log(`🤖 Triggering async call summarization for call ${voiceCallId}...`);
        try {
          const { processCallSummaryAsync } = await import('./conversationSummarizationService');
          processCallSummaryAsync(voiceCallId, updatedCall.organizationId, fullTranscription);
        } catch (summaryError) {
          console.error(`❌ Failed to trigger call summary for ${voiceCallId}:`, summaryError);
          // Don't fail call ending for summary errors
        }
      } else if (status === 'completed') {
        console.log(`⚠️ Call ${voiceCallId} completed but no meaningful transcription available (${fullTranscription.length} chars)`);
      }
      
    } catch (error) {
      console.error('❌ Error ending call:', error);
    }
  }

  // Helper method to calculate call duration
  private calculateCallDuration(voiceCallId: string): number {
    try {
      const callContext = this.activeCalls.get(voiceCallId);
      if (!callContext) return 0;
      
      // Calculate duration from when call was initiated to now
      // This is approximate - real duration should come from Twilio webhooks
      return Math.floor((Date.now() - Date.now()) / 1000); // Placeholder - actual duration from call start
    } catch (error) {
      console.error('❌ Error calculating call duration:', error);
      return 0;
    }
  }

  // 🔒 IDEMPOTENT call finalization to prevent race conditions and ensure reliable summary generation
  private callFinalizationLocks = new Map<string, Promise<void>>();

  // Enhanced call ending method that can be called by external services
  async finalizeCall(voiceCallId: string, options: {
    status: 'completed' | 'failed' | 'no_answer';
    transcription?: string;
    durationSeconds?: number;
    endReason?: string;
  }): Promise<void> {
    // 🔒 RACE CONDITION PREVENTION: Use per-call lock to ensure idempotent finalization
    if (this.callFinalizationLocks.has(voiceCallId)) {
      console.log(`⏳ Call ${voiceCallId} already being finalized, waiting...`);
      await this.callFinalizationLocks.get(voiceCallId);
      return;
    }

    const finalizationPromise = this._finalizeCallInternal(voiceCallId, options);
    this.callFinalizationLocks.set(voiceCallId, finalizationPromise);
    
    try {
      await finalizationPromise;
    } finally {
      this.callFinalizationLocks.delete(voiceCallId);
    }
  }

  private async _finalizeCallInternal(voiceCallId: string, options: {
    status: 'completed' | 'failed' | 'no_answer';
    transcription?: string;
    durationSeconds?: number;
    endReason?: string;
  }): Promise<void> {
    try {
      // 🔍 IDEMPOTENCY CHECK: Don't process already completed calls
      const [existingCall] = await db
        .select()
        .from(voiceCalls)
        .where(eq(voiceCalls.id, voiceCallId))
        .limit(1);

      if (!existingCall) {
        console.error(`❌ Call ${voiceCallId} not found in database`);
        return;
      }

      if (existingCall.status === 'completed' || existingCall.status === 'failed') {
        console.log(`✅ Call ${voiceCallId} already finalized with status: ${existingCall.status}`);
        return;
      }

      let finalTranscription = options.transcription;

      // 📝 TRANSCRIPTION AGGREGATION: If no external transcription, aggregate from notes
      if (!finalTranscription || finalTranscription.length < 10) {
        console.log(`📝 Aggregating conversation notes for call ${voiceCallId}...`);
        
        const conversationNotes = await db
          .select()
          .from(voiceCallNotes)
          .where(eq(voiceCallNotes.callId, voiceCallId))
          .orderBy(voiceCallNotes.timestamp);

        if (conversationNotes.length > 0) {
          finalTranscription = conversationNotes
            .map(note => {
              const timestamp = new Date(note.timestamp).toLocaleTimeString();
              const speaker = note.noteType === 'user_input' ? 'Candidate' : 
                             note.noteType === 'ai_response' ? 'Sarah (AI)' : 'System';
              return `[${timestamp}] ${speaker}: ${note.content}`;
            })
            .join('\n');
          
          console.log(`📝 Aggregated ${conversationNotes.length} notes into ${finalTranscription.length} char transcription`);
        } else {
          console.log(`⚠️ No conversation notes found for call ${voiceCallId}`);
          finalTranscription = options.transcription || '';
        }
      } else {
        console.log(`🎯 Using provided external transcription for call ${voiceCallId} (${finalTranscription.length} chars)`);
      }
        
      // 💾 UPDATE DATABASE: Set final call data atomically
      const [updatedCall] = await db
        .update(voiceCalls)
        .set({
          status: options.status === 'no_answer' ? 'failed' : options.status,
          endedAt: new Date(),
          transcription: finalTranscription,
          durationSeconds: options.durationSeconds || this.calculateCallDuration(voiceCallId),
          followUpNotes: options.endReason ? `Ended: ${options.endReason}` : undefined
        })
        .where(eq(voiceCalls.id, voiceCallId))
        .returning();

      // 🧹 CLEANUP: Remove from active calls
      this.activeCalls.delete(voiceCallId);

      console.log(`✅ Call ${voiceCallId} finalized with status: ${updatedCall.status}`);

      // 🚀 TRIGGER SUMMARIZATION: Only for completed calls with meaningful transcription
      if (options.status === 'completed' && finalTranscription && finalTranscription.length > 20) {
        console.log(`🤖 Triggering async call summarization for call ${voiceCallId} (${finalTranscription.length} chars)...`);
        try {
          const { processCallSummaryAsync } = await import('./conversationSummarizationService');
          // Fire and forget - don't await to avoid blocking call completion
          processCallSummaryAsync(voiceCallId, updatedCall.organizationId, finalTranscription);
        } catch (summaryError) {
          console.error(`❌ Failed to trigger call summary for ${voiceCallId}:`, summaryError);
          // Don't fail call finalization for summary errors
        }
      } else if (options.status === 'completed') {
        console.log(`⚠️ Call ${voiceCallId} completed but no meaningful transcription available (${(finalTranscription || '').length} chars)`);
      }
      
    } catch (error) {
      console.error(`❌ Error finalizing call ${voiceCallId}:`, error);
      // Ensure cleanup happens even if finalization fails
      this.activeCalls.delete(voiceCallId);
      throw error;
    }
  }

  // Get call analytics
  async getCallAnalytics(voiceCallId: string): Promise<any> {
    try {
      const [call] = await db
        .select()
        .from(voiceCalls)
        .where(eq(voiceCalls.id, voiceCallId));

      const notes = await db
        .select()
        .from(voiceCallNotes)
        .where(eq(voiceCallNotes.callId, voiceCallId));

      return {
        call,
        notes,
        duration: call?.endedAt && call?.startedAt 
          ? Math.round((call.endedAt.getTime() - call.startedAt.getTime()) / 1000)
          : null,
        messageCount: notes.length,
        userInputs: notes.filter(n => n.noteType === 'user_input').length,
        aiResponses: notes.filter(n => n.noteType === 'ai_response').length
      };
    } catch (error) {
      console.error('❌ Error getting call analytics:', error);
      return null;
    }
  }
}

export const enhancedVoiceCallManager = new EnhancedVoiceCallManager();