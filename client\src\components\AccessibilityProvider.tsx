import React, { useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { ARIA_LABELS } from '@/utils/accessibility';

interface AccessibilityProviderProps {
  children: React.ReactNode;
}

export const SkipLink = () => {
  const skipLinkRef = useRef<HTMLAnchorElement>(null);
  
  const handleSkipToContent = (e: React.MouseEvent) => {
    e.preventDefault();
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      mainContent.focus();
      mainContent.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <a
      ref={skipLinkRef}
      href="#main-content"
      onClick={handleSkipToContent}
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:bg-blue-600 focus:text-white focus:px-4 focus:py-2 focus:rounded-md focus:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
      aria-label={ARIA_LABELS.skipToContent}
    >
      Skip to main content
    </a>
  );
};

export const ScreenReaderAnnouncements = () => {
  return (
    <div
      id="screen-reader-announcements"
      className="sr-only"
      aria-live="polite"
      aria-atomic="true"
      role="status"
    />
  );
};

export const AccessibilityProvider: React.FC<AccessibilityProviderProps> = ({ children }) => {
  useEffect(() => {
    // Add focus management on route changes
    const handleRouteChange = () => {
      // Focus the main content area when navigating
      const mainContent = document.getElementById('main-content');
      if (mainContent) {
        mainContent.focus();
      }
    };

    // Listen for route changes (this is a simplified approach)
    window.addEventListener('popstate', handleRouteChange);
    
    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  return (
    <>
      <SkipLink />
      <ScreenReaderAnnouncements />
      {children}
    </>
  );
};

export default AccessibilityProvider;