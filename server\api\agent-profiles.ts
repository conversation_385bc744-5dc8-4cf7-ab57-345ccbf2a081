import { Router } from 'express';
import { authenticateToken } from '../auth';
import { db } from '../db';
import { agentProfiles, authUsers } from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';

const router = Router();

// Validation schemas
const createAgentProfileSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  scriptVersion: z.string().default('v1.0'),
  rubricJson: z.object({
    competencies: z.array(z.object({
      name: z.string(),
      weight: z.number().min(0).max(1),
      criteria: z.array(z.string())
    })),
    scoringScale: z.object({
      min: z.number(),
      max: z.number(),
      levels: z.array(z.object({
        score: z.number(),
        label: z.string(),
        description: z.string()
      }))
    })
  }).optional(),
  safetyJson: z.object({
    prohibitedTopics: z.array(z.string()),
    timeoutSettings: z.object({
      maxInterviewDuration: z.number(),
      maxSilenceDuration: z.number(),
      warningThresholds: z.array(z.number())
    }),
    escalationRules: z.array(z.object({
      condition: z.string(),
      action: z.string()
    }))
  }).optional(),
  promptTemplate: z.string().optional(),
  voiceSettings: z.object({
    voiceId: z.string(),
    stability: z.number().min(0).max(1),
    similarityBoost: z.number().min(0).max(1),
    style: z.number().min(0).max(1).optional(),
    useSpeakerBoost: z.boolean().optional()
  }).optional()
});

const updateAgentProfileSchema = createAgentProfileSchema.partial();

/**
 * @swagger
 * /agent-profiles:
 *   get:
 *     summary: Get agent profiles
 *     description: Retrieve all agent profiles for the organization
 *     tags: [Agent Profiles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Agent profiles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 profiles:
 *                   type: array
 *                   items:
 *                     type: object
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Get agent profiles for the organization
    const profiles = await db
      .select()
      .from(agentProfiles)
      .where(and(
        eq(agentProfiles.organizationId, user.organizationId),
        eq(agentProfiles.isActive, true)
      ))
      .orderBy(agentProfiles.createdAt);

    res.json({
      success: true,
      profiles
    });

  } catch (error) {
    console.error('Error fetching agent profiles:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch agent profiles'
    });
  }
});

/**
 * @swagger
 * /agent-profiles:
 *   post:
 *     summary: Create agent profile
 *     description: Create a new agent profile with interview configuration
 *     tags: [Agent Profiles]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               scriptVersion:
 *                 type: string
 *               rubricJson:
 *                 type: object
 *               safetyJson:
 *                 type: object
 *               promptTemplate:
 *                 type: string
 *               voiceSettings:
 *                 type: object
 *     responses:
 *       201:
 *         description: Agent profile created successfully
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Profile creation failed
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Validate request data
    const validatedData = createAgentProfileSchema.parse(req.body);

    // Set default values for complex objects
    const defaultRubric = {
      competencies: [
        {
          name: 'Technical Skills',
          weight: 0.4,
          criteria: ['Problem solving', 'Code quality', 'System design']
        },
        {
          name: 'Communication',
          weight: 0.3,
          criteria: ['Clarity', 'Listening', 'Explanation ability']
        },
        {
          name: 'Cultural Fit',
          weight: 0.3,
          criteria: ['Team collaboration', 'Learning mindset', 'Adaptability']
        }
      ],
      scoringScale: {
        min: 1,
        max: 5,
        levels: [
          { score: 1, label: 'Poor', description: 'Does not meet expectations' },
          { score: 2, label: 'Below Average', description: 'Partially meets expectations' },
          { score: 3, label: 'Average', description: 'Meets expectations' },
          { score: 4, label: 'Good', description: 'Exceeds expectations' },
          { score: 5, label: 'Excellent', description: 'Far exceeds expectations' }
        ]
      }
    };

    const defaultSafety = {
      prohibitedTopics: [
        'Personal relationships',
        'Political views',
        'Religious beliefs',
        'Health conditions',
        'Financial status'
      ],
      timeoutSettings: {
        maxInterviewDuration: 3600, // 1 hour
        maxSilenceDuration: 30, // 30 seconds
        warningThresholds: [2700, 3300] // 45 min, 55 min warnings
      },
      escalationRules: [
        {
          condition: 'inappropriate_content',
          action: 'redirect_conversation'
        },
        {
          condition: 'extended_silence',
          action: 'prompt_candidate'
        },
        {
          condition: 'time_limit_exceeded',
          action: 'graceful_conclusion'
        }
      ]
    };

    const defaultPrompt = `You are an AI interviewer conducting a professional technical interview. 

Your role:
- Conduct a structured interview following the provided rubric
- Ask relevant technical and behavioral questions
- Evaluate responses fairly and objectively
- Maintain a professional, friendly demeanor
- Ensure the interview stays on track and within time limits

Guidelines:
- Start with a brief introduction and overview
- Progress through technical, behavioral, and role-specific questions
- Allow time for candidate questions
- Conclude with next steps
- Avoid prohibited topics and maintain professional boundaries

Remember to be encouraging while maintaining evaluation standards.`;

    const defaultVoiceSettings = {
      voiceId: 'sarah', // ElevenLabs voice ID
      stability: 0.5,
      similarityBoost: 0.8,
      style: 0.0,
      useSpeakerBoost: true
    };

    // Create agent profile
    const [newProfile] = await db
      .insert(agentProfiles)
      .values({
        organizationId: user.organizationId,
        name: validatedData.name,
        scriptVersion: validatedData.scriptVersion || 'v1.0',
        rubricJson: validatedData.rubricJson || defaultRubric,
        safetyJson: validatedData.safetyJson || defaultSafety,
        promptTemplate: validatedData.promptTemplate || defaultPrompt,
        voiceSettings: validatedData.voiceSettings || defaultVoiceSettings,
        isActive: true
      })
      .returning();

    res.status(201).json({
      success: true,
      profile: newProfile
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      });
    }

    console.error('Error creating agent profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create agent profile'
    });
  }
});

/**
 * @swagger
 * /agent-profiles/{id}:
 *   get:
 *     summary: Get agent profile by ID
 *     description: Retrieve a specific agent profile
 *     tags: [Agent Profiles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Agent profile retrieved successfully
 *       404:
 *         description: Profile not found
 */
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Get agent profile
    const [profile] = await db
      .select()
      .from(agentProfiles)
      .where(and(
        eq(agentProfiles.id, id),
        eq(agentProfiles.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!profile) {
      return res.status(404).json({
        success: false,
        error: 'Agent profile not found'
      });
    }

    res.json({
      success: true,
      profile
    });

  } catch (error) {
    console.error('Error fetching agent profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch agent profile'
    });
  }
});

/**
 * @swagger
 * /agent-profiles/{id}:
 *   put:
 *     summary: Update agent profile
 *     description: Update an existing agent profile
 *     tags: [Agent Profiles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Agent profile updated successfully
 *       404:
 *         description: Profile not found
 *       400:
 *         description: Invalid request data
 */
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Validate request data
    const validatedData = updateAgentProfileSchema.parse(req.body);

    // Check if profile exists and belongs to organization
    const [existingProfile] = await db
      .select()
      .from(agentProfiles)
      .where(and(
        eq(agentProfiles.id, id),
        eq(agentProfiles.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!existingProfile) {
      return res.status(404).json({
        success: false,
        error: 'Agent profile not found'
      });
    }

    // Update profile
    const [updatedProfile] = await db
      .update(agentProfiles)
      .set({
        ...validatedData,
        updatedAt: new Date()
      })
      .where(eq(agentProfiles.id, id))
      .returning();

    res.json({
      success: true,
      profile: updatedProfile
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      });
    }

    console.error('Error updating agent profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update agent profile'
    });
  }
});

/**
 * @swagger
 * /agent-profiles/{id}:
 *   delete:
 *     summary: Delete agent profile
 *     description: Soft delete an agent profile (mark as inactive)
 *     tags: [Agent Profiles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Agent profile deleted successfully
 *       404:
 *         description: Profile not found
 */
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Check if profile exists and belongs to organization
    const [existingProfile] = await db
      .select()
      .from(agentProfiles)
      .where(and(
        eq(agentProfiles.id, id),
        eq(agentProfiles.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!existingProfile) {
      return res.status(404).json({
        success: false,
        error: 'Agent profile not found'
      });
    }

    // Soft delete (mark as inactive)
    await db
      .update(agentProfiles)
      .set({
        isActive: false,
        updatedAt: new Date()
      })
      .where(eq(agentProfiles.id, id));

    res.json({
      success: true,
      message: 'Agent profile deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting agent profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete agent profile'
    });
  }
});

export default router;
