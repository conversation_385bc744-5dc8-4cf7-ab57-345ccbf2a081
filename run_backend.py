#!/usr/bin/env python3
"""
Backend server runner for Multi-Tenant SaaS Application
Runs FastAPI server with proper configuration
"""

import sys
import os
sys.path.append('./backend')

# Set environment variables
os.environ.setdefault('PYTHONPATH', './backend')

import uvicorn

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        app_dir="./backend"
    )