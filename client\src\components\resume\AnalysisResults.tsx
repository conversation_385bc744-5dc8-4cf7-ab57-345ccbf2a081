
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { User, CheckCircle, XCircle } from "lucide-react";
import { EnhancedAnalysisResult } from "@/types/resumeAnalysis";
import { useCandidateContext } from "@/contexts/CandidateContext";
import CandidateOverview from "./CandidateOverview";
import SkillAnalysis from "./SkillAnalysis";
import ExperienceAnalysis from "./ExperienceAnalysis";
import InterviewQuestions from "./InterviewQuestions";

interface AnalysisResultsProps {
  analysisResult: EnhancedAnalysisResult;
  onDecision: (decision: 'approve' | 'reject') => Promise<void>;
}

const AnalysisResults: React.FC<AnalysisResultsProps> = ({ analysisResult, onDecision }) => {
  const { addApprovedCandidate } = useCandidateContext();

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'HIRE': return 'bg-green-100 text-green-800';
      case 'INTERVIEW': return 'bg-yellow-100 text-yellow-800';
      case 'REJECT': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleApprove = async () => {
    // Use the actual email from analysis result, fallback to a placeholder if not available
    const candidateEmail = analysisResult.email || `${analysisResult.name.toLowerCase().replace(/\s+/g, '.')}@placeholder.com`;
    
    // Add candidate to approved list for scheduling
    addApprovedCandidate({
      id: `candidate-${Date.now()}`, // Generate unique ID
      name: analysisResult.name,
      email: candidateEmail,
      position: analysisResult.recent_roles?.[0]?.title || 'Position TBD',
      match_score: analysisResult.match_score,
      status: 'pending_schedule'
    });

    // Call the original onDecision function
    await onDecision('approve');
  };

  // Provide safe defaults for potentially missing fields
  const safeAnalysisResult = {
    ...analysisResult,
    skill_analysis: analysisResult.skill_analysis || {
      matched_skills: [],
      missing_skills: [],
      transferable_skills: []
    },
    experience_analysis: analysisResult.experience_analysis || {
      relevant_experience: 'Analysis not available',
      experience_gap: 'Analysis not available',
      career_progression: 'Analysis not available'
    },
    interview_questions: analysisResult.interview_questions || [],
    detailed_feedback: analysisResult.detailed_feedback || 'Detailed feedback not available.'
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <User className="w-5 h-5 mr-2" />
            Enhanced Analysis Results
          </span>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">Match: {analysisResult.match_score}%</Badge>
            <Badge className={getRecommendationColor(analysisResult.recommendation)}>
              {analysisResult.recommendation}
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <CandidateOverview analysisResult={safeAnalysisResult} />
        
        <SkillAnalysis skillAnalysis={safeAnalysisResult.skill_analysis} />
        
        <ExperienceAnalysis experienceAnalysis={safeAnalysisResult.experience_analysis} />

        {/* Detailed Feedback */}
        <div>
          <h4 className="font-medium text-lg mb-2">Detailed Assessment</h4>
          <Card className="p-4 bg-gray-50">
            <p className="text-sm leading-relaxed">{safeAnalysisResult.detailed_feedback}</p>
          </Card>
        </div>

        <InterviewQuestions questions={safeAnalysisResult.interview_questions} />

        {/* Decision Buttons */}
        <div className="flex space-x-4 pt-4 border-t">
          <Button 
            onClick={handleApprove} 
            className="flex-1 bg-green-600 hover:bg-green-700"
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            Approve & Move to Next Stage
          </Button>
          <Button 
            onClick={() => onDecision('reject')} 
            variant="outline"
            className="flex-1 text-red-600 border-red-600 hover:bg-red-50"
          >
            <XCircle className="w-4 h-4 mr-2" />
            Reject Application
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default AnalysisResults;
