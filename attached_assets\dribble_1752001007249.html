<html>
  <head>
    <title>Candidate Details - Recruit</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"></link>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
      body {
        font-family: 'Inter', sans-serif;
      }
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }
      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
    </style>
  </head>
  <body class="bg-[#f7f8fa] min-h-screen">
    <div class="flex w-full min-h-screen">
      <!-- Left Panel -->
      <div class="w-[38%] bg-white border-r border-[#e5e7eb] pt-6 pl-8 pr-4 pb-8 relative z-10 shadow-md" style="min-width: 480px;">
        <!-- Header -->
        <div class="flex items-center mb-8">
          <span class="text-2xl font-bold tracking-tight text-black mr-2">recruit</span>
          <div class="flex-1"></div>
          <i class="fas fa-bars text-gray-400 text-xl"></i>
        </div>
        <!-- Nav -->
        <div class="flex items-center space-x-8 mb-8">
          <a href="#" class="flex items-center text-gray-400 text-base font-medium">
            <i class="fas fa-home mr-2"></i> Home
          </a>
          <a href="#" class="flex items-center text-gray-400 text-base font-medium">
            <i class="fas fa-briefcase mr-2"></i> Jobs
          </a>
          <a href="#" class="flex items-center text-[#5b6dfa] text-base font-medium border-b-2 border-[#5b6dfa] pb-1">
            <i class="fas fa-users mr-2"></i> Matches
          </a>
          <a href="#" class="flex items-center text-gray-400 text-base font-medium">
            <i class="fas fa-search mr-2"></i> Screening
          </a>
        </div>
        <!-- Job Title -->
        <div class="mb-2">
          <div class="flex items-center space-x-2">
            <span class="text-lg font-semibold text-gray-900">Senior Product Designer</span>
            <span class="text-xs text-[#5b6dfa] border border-[#5b6dfa] rounded-full px-2 py-0.5 font-medium ml-1">Open</span>
          </div>
          <div class="flex items-center mt-1 space-x-2">
            <i class="fas fa-map-marker-alt text-gray-400 text-xs"></i>
            <span class="text-xs text-gray-400">Canada</span>
          </div>
        </div>
        <!-- Job Details -->
        <div class="flex flex-wrap gap-x-8 gap-y-2 text-xs text-gray-500 mb-4 mt-2">
          <div>
            <span class="block font-medium text-gray-700">Category</span>
            <span>Product</span>
          </div>
          <div>
            <span class="block font-medium text-gray-700">Experience</span>
            <span>3+ years</span>
          </div>
          <div>
            <span class="block font-medium text-gray-700">Availability</span>
            <span>Full-time</span>
          </div>
          <div>
            <span class="block font-medium text-gray-700">Salary</span>
            <span>$80k - $95k</span>
          </div>
        </div>
        <!-- Tabs -->
        <div class="flex items-center space-x-8 border-b border-[#e5e7eb] mb-2">
          <button class="text-[#5b6dfa] font-medium text-sm pb-2 border-b-2 border-[#5b6dfa]">All Matches</button>
          <button class="text-gray-400 font-medium text-sm pb-2">Screened</button>
          <button class="text-gray-400 font-medium text-sm pb-2">Scheduled</button>
          <button class="text-gray-400 font-medium text-sm pb-2">Hired</button>
          <button class="text-gray-400 font-medium text-sm pb-2">Dismissed</button>
        </div>
        <!-- Candidates List -->
        <div class="overflow-y-auto scrollbar-hide" style="max-height: 60vh;">
          <div class="flex items-center py-3 border-b border-[#f1f1f1]">
            <img src="https://placehold.co/40x40/8b5cf6/fff?text=M" alt="Avatar of Marvin McKinney, a man with short hair and glasses" class="w-10 h-10 rounded-full object-cover mr-3">
            <span class="text-gray-900 font-medium flex-1">Marvin McKinney</span>
            <span class="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-600 font-semibold text-sm">85</span>
          </div>
          <div class="flex items-center py-3 border-b border-[#f1f1f1] bg-[#f7f8fa]">
            <img src="https://placehold.co/40x40/60a5fa/fff?text=S" alt="Avatar of Savannah Nguyen, a woman with long hair" class="w-10 h-10 rounded-full object-cover mr-3">
            <span class="text-gray-900 font-medium flex-1">Savannah Nguyen</span>
            <span class="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-600 font-semibold text-sm">85</span>
          </div>
          <div class="flex items-center py-3 border-b border-[#f1f1f1]">
            <img src="https://placehold.co/40x40/fbbf24/fff?text=K" alt="Avatar of Kathryn Murphy, a woman with short hair" class="w-10 h-10 rounded-full object-cover mr-3">
            <span class="text-gray-900 font-medium flex-1">Kathryn Murphy</span>
            <span class="flex items-center justify-center w-8 h-8 rounded-full bg-yellow-100 text-yellow-600 font-semibold text-sm">75</span>
          </div>
          <div class="flex items-center py-3 border-b border-[#f1f1f1]">
            <img src="https://placehold.co/40x40/60a5fa/fff?text=R" alt="Avatar of Robert Fox, a man with beard" class="w-10 h-10 rounded-full object-cover mr-3">
            <span class="text-gray-900 font-medium flex-1">Robert Fox</span>
            <span class="flex items-center justify-center w-8 h-8 rounded-full bg-yellow-100 text-yellow-600 font-semibold text-sm">70</span>
          </div>
          <div class="flex items-center py-3 border-b border-[#f1f1f1]">
            <img src="https://placehold.co/40x40/f87171/fff?text=J" alt="Avatar of Jane Cooper, a woman with curly hair" class="w-10 h-10 rounded-full object-cover mr-3">
            <span class="text-gray-900 font-medium flex-1">Jane Cooper</span>
            <span class="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-600 font-semibold text-sm">85</span>
          </div>
          <div class="flex items-center py-3 border-b border-[#f1f1f1]">
            <img src="https://placehold.co/40x40/34d399/fff?text=R" alt="Avatar of Ralph Edwards, a man with glasses" class="w-10 h-10 rounded-full object-cover mr-3">
            <span class="text-gray-900 font-medium flex-1">Ralph Edwards</span>
            <span class="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 text-green-600 font-semibold text-sm">85</span>
          </div>
          <div class="flex items-center py-3 border-b border-[#f1f1f1]">
            <img src="https://placehold.co/40x40/818cf8/fff?text=A" alt="Avatar of Arlene McCoy, a woman with short hair" class="w-10 h-10 rounded-full object-cover mr-3">
            <span class="text-gray-900 font-medium flex-1">Arlene McCoy</span>
            <span class="flex items-center justify-center w-8 h-8 rounded-full bg-yellow-100 text-yellow-600 font-semibold text-sm">85</span>
          </div>
          <div class="flex items-center py-3">
            <img src="https://placehold.co/40x40/818cf8/fff?text=M" alt="Avatar of McCoy, a man with short hair" class="w-10 h-10 rounded-full object-cover mr-3">
            <span class="text-gray-900 font-medium flex-1">McCoy</span>
            <span class="flex items-center justify-center w-8 h-8 rounded-full bg-yellow-100 text-yellow-600 font-semibold text-sm">85</span>
          </div>
        </div>
      </div>
      <!-- Right Panel -->
      <div class="flex-1 bg-[#f7f8fa] flex flex-col items-center justify-start pt-8 pb-8 px-8">
        <div class="w-full max-w-3xl bg-white rounded-2xl shadow-lg p-8 relative">
          <!-- Top Navigation -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-2">
              <button class="text-gray-400 hover:text-[#5b6dfa] text-base font-medium flex items-center">
                <i class="fas fa-chevron-left mr-1"></i> Previous
              </button>
              <button class="text-gray-400 hover:text-[#5b6dfa] text-base font-medium flex items-center">
                Next <i class="fas fa-chevron-right ml-1"></i>
              </button>
            </div>
            <div class="flex items-center space-x-4">
              <button class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 text-gray-400">
                <i class="fas fa-expand"></i>
              </button>
              <button class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 text-gray-400">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
          <!-- Candidate Header -->
          <div class="flex items-center mb-8">
            <img src="https://placehold.co/64x64/60a5fa/fff?text=S" alt="Profile photo of Savannah Nguyen, a woman with long hair" class="w-16 h-16 rounded-full object-cover mr-5">
            <div>
              <div class="flex items-center space-x-2">
                <span class="text-xl font-semibold text-gray-900">Savannah Nguyen</span>
                <span class="text-sm text-gray-400">Toronto, Canada</span>
              </div>
              <div class="flex items-center space-x-4 mt-2">
                <span class="flex items-center text-green-500 text-sm font-medium">
                  <i class="fas fa-check-circle mr-1"></i> 85% Matched
                </span>
                <span class="flex items-center text-gray-400 text-sm font-medium">
                  <i class="fas fa-briefcase mr-1"></i> 3 years
                </span>
                <span class="flex items-center text-gray-400 text-sm font-medium">
                  <i class="fas fa-dollar-sign mr-1"></i> $90k
                </span>
              </div>
            </div>
            <div class="flex-1"></div>
            <div class="flex items-center space-x-3">
              <button class="px-5 py-2 rounded-lg border border-[#e5e7eb] text-gray-700 font-medium text-sm hover:bg-gray-50 flex items-center">
                <i class="fas fa-desktop mr-2"></i> Screen
              </button>
              <button class="px-5 py-2 rounded-lg border border-[#e5e7eb] text-gray-700 font-medium text-sm hover:bg-gray-50 flex items-center">
                <i class="fas fa-calendar-alt mr-2"></i> Schedule Call
              </button>
            </div>
          </div>
          <!-- Candidate Info -->
          <div class="grid grid-cols-3 gap-6 mb-6">
            <div>
              <div class="text-xs text-gray-400 font-medium mb-1">License</div>
              <div class="text-sm text-gray-900 font-medium">Yes</div>
            </div>
            <div>
              <div class="text-xs text-gray-400 font-medium mb-1">Availability</div>
              <div class="text-sm text-gray-900 font-medium">Full-time</div>
            </div>
            <div>
              <div class="text-xs text-gray-400 font-medium mb-1">Match date</div>
              <div class="text-sm text-gray-900 font-medium">12 May, 2023</div>
            </div>
          </div>
          <!-- Tabs -->
          <div class="flex items-center space-x-8 border-b border-[#e5e7eb] mb-6">
            <button class="text-[#5b6dfa] font-medium text-base pb-2 border-b-2 border-[#5b6dfa]">Application</button>
            <button class="text-gray-400 font-medium text-base pb-2">Experiences</button>
            <button class="text-gray-400 font-medium text-base pb-2">Certification & Others</button>
            <button class="text-gray-400 font-medium text-base pb-2">Nguyen Calendar</button>
          </div>
          <!-- Application Content -->
          <div>
            <!-- About -->
            <div class="mb-6">
              <div class="text-sm font-semibold text-gray-900 mb-2">About Marvin</div>
              <div class="text-sm text-gray-500">
                UI/UX designers - how would you like to work within a successful Saas based firm in downtown Toronto, building customized tools for one of the largest genomics sequencing projects in the world?
              </div>
            </div>
            <!-- Top Skills -->
            <div class="mb-6">
              <div class="text-sm font-semibold text-gray-900 mb-2">Top Skills</div>
              <div class="flex flex-wrap gap-2">
                <span class="px-4 py-1 rounded-full bg-[#f7f8fa] text-gray-700 text-sm font-medium">Product Design</span>
                <span class="px-4 py-1 rounded-full bg-[#f7f8fa] text-gray-700 text-sm font-medium">UI/UX Design</span>
                <span class="px-4 py-1 rounded-full bg-[#f7f8fa] text-gray-700 text-sm font-medium">Prototyping</span>
                <span class="px-4 py-1 rounded-full bg-[#f7f8fa] text-gray-700 text-sm font-medium">Interaction Design</span>
                <span class="px-4 py-1 rounded-full bg-[#f7f8fa] text-gray-700 text-sm font-medium">Wireframe</span>
                <span class="px-4 py-1 rounded-full bg-[#f7f8fa] text-gray-700 text-sm font-medium">Design System</span>
                <span class="px-4 py-1 rounded-full bg-[#f7f8fa] text-gray-700 text-sm font-medium">Documentation</span>
              </div>
            </div>
            <!-- Resume -->
            <div class="mb-6">
              <div class="text-sm font-semibold text-gray-900 mb-2">Resume</div>
              <div class="flex items-center justify-between bg-[#f7f8fa] rounded-lg px-4 py-3">
                <div class="flex items-center">
                  <i class="fas fa-file-pdf text-[#f87171] text-xl mr-3"></i>
                  <div>
                    <div class="text-sm text-gray-900 font-medium">Savannah Nguyen_Resume.pdf</div>
                    <div class="text-xs text-gray-400">280KB</div>
                  </div>
                </div>
                <a href="#" class="flex items-center text-[#5b6dfa] text-sm font-medium hover:underline">
                  Download <i class="fas fa-download ml-2"></i>
                </a>
              </div>
            </div>
            <!-- Portfolio -->
            <div class="mb-6">
              <div class="text-sm font-semibold text-gray-900 mb-2">Portfolio</div>
              <a href="#" class="flex items-center text-[#5b6dfa] text-sm font-medium hover:underline">
                Savannah_portfolio <i class="fas fa-external-link-alt ml-2 text-xs"></i>
              </a>
            </div>
            <!-- Education -->
            <div class="mb-6">
              <div class="text-sm font-semibold text-gray-900 mb-2">Education</div>
              <div class="text-sm text-gray-900 font-medium">B.Sc in Computer Science</div>
              <div class="text-xs text-gray-400">Jan 2017 - Dec 2020</div>
              <div class="text-sm text-gray-500">North South University, Dhaka</div>
            </div>
            <!-- Culture -->
            <div class="mb-2">
              <div class="text-sm font-semibold text-gray-900 mb-2">Culture</div>
              <div class="text-sm text-gray-900 font-medium mb-1">What motivates you?</div>
              <div class="text-sm text-gray-500">
                I am passionate about the real estate industry and love working with organizations that are open minded.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
