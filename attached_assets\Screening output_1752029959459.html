<html>
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
  <title>
   Candidate Details
  </title>
  <script src="https://cdn.tailwindcss.com">
  </script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet"/>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
  <style>
   body {
            font-family: 'Inter', sans-serif;
            background-color: #e5e7eb;
        }
  </style>
 </head>
 <body class="flex items-center justify-center min-h-screen bg-gray-200">
  <div class="bg-white rounded-xl shadow-lg w-full max-w-3xl mx-4">
   <!-- Header -->
   <div class="p-6 flex justify-between items-center">
    <h1 class="text-xl font-bold">
     Candidate Details
    </h1>
    <div class="flex items-center space-x-4">
     <div class="flex items-center text-blue-500">
      <i class="fas fa-chevron-left mr-2">
      </i>
      <span>
       Previous
      </span>
     </div>
     <div class="flex items-center text-blue-500">
      <span>
       Next
      </span>
      <i class="fas fa-chevron-right ml-2">
      </i>
     </div>
     <button class="text-gray-500">
      <i class="fas fa-expand-alt">
      </i>
     </button>
     <button class="text-gray-500">
      <i class="fas fa-times">
      </i>
     </button>
    </div>
   </div>
   <!-- Profile Section -->
   <div class="px-6 pb-4">
    <div class="flex items-start">
     <div class="mr-4">
      <img alt="Profile picture of Savannah Nguyen with blue background" class="rounded-full w-16 h-16" height="100" src="https://replicate.delivery/xezq/mJuMpioFoZ78Ht8Pls7o3JLjqz20CQUv1CvHafAiMGpdYcfUA/out-0.png" width="100"/>
     </div>
     <div class="flex-1">
      <div class="flex justify-between items-center">
       <div>
        <h2 class="text-xl font-bold">
         Savannah Nguyen
        </h2>
        <div class="flex items-center text-gray-500 text-sm mt-1">
         <i class="fas fa-map-marker-alt mr-1">
         </i>
         <span>
          Toronto, Canada
         </span>
        </div>
       </div>
       <div class="flex space-x-2">
        <button class="flex items-center px-4 py-2 border border-gray-300 rounded-full text-gray-700">
         <i class="fas fa-desktop mr-2">
         </i>
         <span>
          Screen
         </span>
        </button>
        <button class="flex items-center px-4 py-2 border border-gray-300 rounded-full text-gray-700">
         <i class="far fa-calendar-alt mr-2">
         </i>
         <span>
          Schedule Call
         </span>
        </button>
        <button class="px-2 py-2 border border-gray-300 rounded-full text-gray-700">
         <i class="fas fa-ellipsis-h">
         </i>
        </button>
       </div>
      </div>
     </div>
    </div>
    <!-- Stats -->
    <div class="flex mt-4 space-x-2">
     <div class="flex items-center bg-gray-100 rounded-full px-4 py-2">
      <i class="fas fa-user-check text-green-500 mr-2">
      </i>
      <span class="font-semibold text-green-500">
       85%
      </span>
      <span class="ml-1 text-gray-700">
       Matched
      </span>
     </div>
     <div class="flex items-center bg-gray-100 rounded-full px-4 py-2">
      <i class="far fa-calendar-alt text-gray-500 mr-2">
      </i>
      <span class="text-gray-700">
       3 years
      </span>
     </div>
     <div class="flex items-center bg-gray-100 rounded-full px-4 py-2">
      <i class="fas fa-dollar-sign text-gray-500 mr-2">
      </i>
      <span class="text-gray-700">
       $90k
      </span>
     </div>
    </div>
    <!-- Details Grid -->
    <div class="grid grid-cols-3 gap-4 mt-6 border-b pb-6">
     <div>
      <p class="text-gray-500 text-sm">
       License
      </p>
      <p class="font-medium">
       Yes
      </p>
     </div>
     <div>
      <p class="text-gray-500 text-sm">
       Availability
      </p>
      <p class="font-medium">
       Full-time
      </p>
     </div>
     <div>
      <p class="text-gray-500 text-sm">
       Match date
      </p>
      <p class="font-medium">
       12 May, 2023
      </p>
     </div>
    </div>
    <!-- Tabs -->
    <div class="flex border-b mt-4">
     <button class="px-4 py-2 text-indigo-600 border-b-2 border-indigo-600 font-medium">
      Application
     </button>
     <button class="px-4 py-2 text-gray-500">
      Experiences
     </button>
     <button class="px-4 py-2 text-gray-500">
      Certification &amp; Others
     </button>
     <button class="px-4 py-2 text-gray-500">
      Nguyen Calendar
     </button>
    </div>
    <!-- About Section -->
    <div class="mt-6">
     <h3 class="font-bold text-lg mb-3">
      About Marvin
     </h3>
     <p class="text-gray-700">
      UI/UX designers - how would you like to work within a successful SaaS based firm in downtown Toronto, building customized tools for one of the largest genomics sequencing projects in the world?
     </p>
    </div>
    <!-- Skills Section -->
    <div class="mt-6">
     <h3 class="font-bold text-lg mb-3">
      Top Skills
     </h3>
     <div class="flex flex-wrap gap-2">
      <span class="px-4 py-2 bg-gray-100 rounded-full text-gray-700">
       Product Design
      </span>
      <span class="px-4 py-2 bg-gray-100 rounded-full text-gray-700">
       UI/UX Design
      </span>
      <span class="px-4 py-2 bg-gray-100 rounded-full text-gray-700">
       Prototyping
      </span>
      <span class="px-4 py-2 bg-gray-100 rounded-full text-gray-700">
       Interaction Design
      </span>
      <span class="px-4 py-2 bg-gray-100 rounded-full text-gray-700">
       Wireframe
      </span>
      <span class="px-4 py-2 bg-gray-100 rounded-full text-gray-700">
       Design System
      </span>
      <span class="px-4 py-2 bg-gray-100 rounded-full text-gray-700">
       Documentation
      </span>
     </div>
    </div>
    <!-- Resume Section -->
    <div class="mt-6">
     <h3 class="font-bold text-lg mb-3">
      Resume
     </h3>
     <div class="bg-gray-50 p-4 rounded-lg flex justify-between items-center">
      <div class="flex items-center">
       <div class="bg-red-100 p-2 rounded mr-3">
        <i class="far fa-file-pdf text-red-500">
        </i>
       </div>
       <div>
        <p class="font-medium">
         Savannah Nguyen_Resume.pdf
        </p>
        <p class="text-gray-500 text-sm">
         280KB
        </p>
       </div>
      </div>
      <button class="text-gray-700 flex items-center">
       <span>
        Download
       </span>
       <i class="fas fa-download ml-1">
       </i>
      </button>
     </div>
    </div>
    <!-- Portfolio Section -->
    <div class="mt-6">
     <h3 class="font-bold text-lg mb-3">
      Portfolio
     </h3>
     <div class="bg-gray-50 p-4 rounded-lg flex justify-between items-center">
      <div class="flex items-center">
       <i class="fas fa-link text-gray-500 mr-3">
       </i>
       <p class="font-medium underline">
        Savannah_portfolio
       </p>
      </div>
      <button class="text-gray-700">
       <i class="fas fa-external-link-alt">
       </i>
      </button>
     </div>
    </div>
    <!-- Education Section -->
    <div class="mt-6">
     <h3 class="font-bold text-lg mb-3">
      Education
     </h3>
     <div>
      <p class="font-medium">
       B.sc in Computer Science
      </p>
      <p class="text-gray-500 text-sm">
       Jan 2017 - Dec 2020
      </p>
      <p class="text-gray-500 text-sm">
       North South University, Dhaka
      </p>
     </div>
    </div>
    <!-- Culture Section -->
    <div class="mt-6">
     <h3 class="font-bold text-lg mb-3">
      Culture
     </h3>
     <p class="font-medium">
      What motivates you?
     </p>
     <p class="text-gray-700 mt-1">
      I am passionate about the real estate industry because it allows for limitless income and growth opportunities while also allowing people to build their life by design.
     </p>
    </div>
   </div>
  </div>
 </body>
</html>