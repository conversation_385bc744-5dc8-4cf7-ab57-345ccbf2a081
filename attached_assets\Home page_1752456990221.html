<html>
 <head>
  <title>
   HireFlow - AI-powered recruitment dashboard
  </title>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
  <script src="https://cdn.tailwindcss.com">
  </script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet"/>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&amp;display=swap" rel="stylesheet"/>
  <style>
   body {
        font-family: 'Inter', sans-serif;
      }
  </style>
 </head>
 <body class="bg-[#f3f4fa] min-h-screen">
  <!-- Background Gradient and Pattern -->
  <div class="fixed inset-0 z-0">
   <div class="absolute inset-0" style="background: linear-gradient(120deg, #a259e6 0%, #7b5cff 100%);">
   </div>
   <div class="absolute inset-0 opacity-80" style="background: url('https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&amp;fit=crop&amp;w=1500&amp;q=80'); background-size: cover; background-position: center;">
   </div>
   <div class="absolute inset-0 bg-gradient-to-br from-[#a259e6]/80 via-[#7b5cff]/80 to-[#f3f4fa]/60">
   </div>
  </div>
  <!-- Main Content -->
  <div class="relative z-10 flex flex-col items-center pt-10 pb-24">
   <!-- Header Card -->
   <div class="w-[95%] max-w-6xl rounded-2xl bg-[#7b5cff] shadow-xl px-0 pt-8 pb-24 flex flex-col items-center">
    <!-- Navbar -->
    <div class="w-full flex items-center justify-between px-12">
     <div class="flex items-center space-x-3">
      <div class="flex items-center">
       <span class="inline-block w-8 h-8 bg-white rounded-lg flex items-center justify-center mr-2">
        <img alt="HireFlow logo, abstract star shape in white on purple" class="w-6 h-6" height="28" src="https://replicate.delivery/xezq/EWlaBYYVki5KNlc1C7966HieXm23WgVqYdLEEq23nhgZegAVA/out-0.png" width="28"/>
       </span>
       <span class="text-white text-xl font-semibold tracking-tight">
        HireFlow
       </span>
      </div>
     </div>
     <nav class="flex items-center space-x-10">
      <a class="text-white text-base font-medium hover:underline" href="#">
       Home
      </a>
      <a class="text-white text-base font-medium hover:underline" href="#">
       About Us
      </a>
      <a class="text-white text-base font-medium hover:underline" href="#">
       Pricing
      </a>
      <a class="text-white text-base font-medium hover:underline" href="#">
       Contact Us
      </a>
     </nav>
     <div>
      <button class="bg-black text-white px-6 py-2 rounded-lg font-medium text-base shadow hover:bg-gray-900 transition flex items-center space-x-2">
       <span>
        Sign Up
       </span>
       <i class="fas fa-arrow-right ml-2">
       </i>
      </button>
     </div>
    </div>
    <!-- Hero Section -->
    <div class="flex flex-col items-center mt-16">
     <span class="text-white text-xs tracking-[0.2em] font-medium mb-2">
      MEET HIREFLOW
     </span>
     <h1 class="text-white text-5xl font-light text-center leading-tight mb-2" style="font-size:3.2rem;">
      AI-powered recruitment
      <br/>
      dashboard
     </h1>
     <p class="text-[#e0d8ff] text-base font-normal text-center mt-2 mb-8 max-w-xl">
      Designed to streamline and enhance the hiring
      <br/>
      process for companies of all sizes
     </p>
     <div class="flex space-x-4">
      <button class="bg-white text-[#7b5cff] px-7 py-2 rounded-lg font-medium text-base shadow hover:bg-gray-100 transition">
       Try Demo
      </button>
      <button class="bg-black text-white px-7 py-2 rounded-lg font-medium text-base shadow hover:bg-gray-900 transition">
       Get Started
      </button>
     </div>
    </div>
    <!-- Dashboard Card -->
    <div class="w-full flex justify-center mt-16">
     <div class="w-[90%] max-w-5xl bg-white rounded-xl shadow-2xl flex flex-row p-0">
      <!-- Sidebar -->
      <div class="w-64 border-r border-gray-200 py-8 px-6 flex flex-col">
       <div class="flex items-center mb-8">
        <div class="w-10 h-10 bg-[#f3f4fa] rounded-lg flex items-center justify-center mr-3">
         <img alt="HireFlow logo, abstract star shape in purple on white" class="w-7 h-7" height="32" src="https://replicate.delivery/xezq/TLIsExGrYfzAGCeTxDm2rC3K7efzMs1aWsfe5Kpih8K8MPIQF/out-0.png" width="32"/>
        </div>
        <div>
         <div class="text-gray-900 font-semibold text-base">
          HireFlow
         </div>
         <div class="text-xs text-gray-400">
          Professional Plan
         </div>
        </div>
       </div>
       <div class="mb-2">
        <input class="w-full px-3 py-2 rounded-md bg-[#f3f4fa] text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#7b5cff]" placeholder="Search anything here" type="text"/>
       </div>
       <div class="mt-4">
        <div class="text-xs text-gray-400 font-semibold mb-2">
         MAIN MENU
        </div>
        <ul class="space-y-2 mb-6">
         <li>
          <a class="flex items-center text-[#7b5cff] font-semibold bg-[#f3f4fa] rounded-md px-3 py-2" href="#">
           <i class="fas fa-tachometer-alt mr-3">
           </i>
           Dashboard
          </a>
         </li>
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="far fa-calendar-alt mr-3">
           </i>
           Calendar
          </a>
         </li>
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-tasks mr-3">
           </i>
           Ongoing Recruitment
          </a>
         </li>
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-chart-bar mr-3">
           </i>
           Analytics
          </a>
         </li>
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-file-alt mr-3">
           </i>
           Reports
          </a>
         </li>
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-comments mr-3">
           </i>
           Chat
          </a>
         </li>
        </ul>
        <div class="text-xs text-gray-400 font-semibold mb-2">
         RECRUITMENT
        </div>
        <ul class="space-y-2 mb-6">
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-briefcase mr-3">
           </i>
           Vacancies
          </a>
         </li>
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-user-friends mr-3">
           </i>
           Candidates
          </a>
         </li>
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-comments mr-3">
           </i>
           Interviews
          </a>
         </li>
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-handshake mr-3">
           </i>
           Offers
          </a>
         </li>
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-user-plus mr-3">
           </i>
           Onboarding
          </a>
         </li>
        </ul>
        <div class="text-xs text-gray-400 font-semibold mb-2">
         ACCOUNT
        </div>
        <ul class="space-y-2 mb-6">
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-user-circle mr-3">
           </i>
           Account
          </a>
         </li>
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-users mr-3">
           </i>
           Members
          </a>
         </li>
        </ul>
        <div class="text-xs text-gray-400 font-semibold mb-2">
         OTHERS
        </div>
        <ul class="space-y-2">
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-cog mr-3">
           </i>
           Settings
          </a>
         </li>
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-comment-dots mr-3">
           </i>
           Feedback
          </a>
         </li>
         <li>
          <a class="flex items-center text-gray-700 hover:text-[#7b5cff] px-3 py-2" href="#">
           <i class="fas fa-sign-out-alt mr-3">
           </i>
           Logout
          </a>
         </li>
        </ul>
       </div>
      </div>
      <!-- Main Dashboard Content -->
      <div class="flex-1 py-8 px-8">
       <div class="flex items-center justify-between mb-6">
        <div>
         <h2 class="text-lg font-semibold text-gray-900">
          Dashboard
         </h2>
         <p class="text-xs text-gray-400">
          A glance at important information
         </p>
        </div>
        <div class="flex items-center space-x-3">
         <button class="flex items-center px-3 py-1.5 bg-[#f3f4fa] text-gray-500 rounded-md text-sm font-medium">
          <i class="fas fa-bell mr-2">
          </i>
          Notifications
          <span class="ml-2 w-2 h-2 bg-red-500 rounded-full">
          </span>
         </button>
         <button class="flex items-center px-3 py-1.5 bg-[#f3f4fa] text-gray-500 rounded-md text-sm font-medium">
          <i class="fas fa-filter mr-2">
          </i>
          Filters
         </button>
         <button class="flex items-center px-3 py-1.5 bg-[#f3f4fa] text-gray-500 rounded-md text-sm font-medium">
          <i class="fas fa-edit mr-2">
          </i>
          Edit Widget
         </button>
        </div>
       </div>
       <!-- Dashboard Widgets -->
       <div class="grid grid-cols-4 gap-6 mb-8">
        <div class="bg-[#f3f4fa] rounded-lg p-5">
         <div class="text-xs text-gray-400 font-medium mb-1">
          Headcount
         </div>
         <div class="text-2xl font-bold text-gray-900 mb-1">
          625
         </div>
         <div class="text-xs text-green-500 font-medium">
          +7% compared last year
         </div>
        </div>
        <div class="bg-[#f3f4fa] rounded-lg p-5">
         <div class="text-xs text-gray-400 font-medium mb-1">
          Turnover Rate
         </div>
         <div class="text-2xl font-bold text-gray-900 mb-1">
          5%
         </div>
         <div class="text-xs text-red-500 font-medium">
          +20% compared last year
         </div>
        </div>
        <div class="bg-[#f3f4fa] rounded-lg p-5">
         <div class="text-xs text-gray-400 font-medium mb-1">
          Employee Engagement
         </div>
         <div class="text-2xl font-bold text-gray-900 mb-1">
          90%
         </div>
         <div class="text-xs text-green-500 font-medium">
          +25% compared last year
         </div>
        </div>
        <div class="bg-[#f3f4fa] rounded-lg p-5">
         <div class="text-xs text-gray-400 font-medium mb-1">
          Average Time to Hire
         </div>
         <div class="text-2xl font-bold text-gray-900 mb-1">
          30 days
         </div>
         <div class="text-xs text-green-500 font-medium">
          +10% compared last year
         </div>
        </div>
       </div>
       <!-- Graphs and Tables -->
       <div class="grid grid-cols-3 gap-6">
        <!-- Comparison Data Graph -->
        <div class="bg-[#f3f4fa] rounded-lg p-5 col-span-2">
         <div class="text-xs text-gray-400 font-medium mb-2">
          Comparison Data
         </div>
         <img alt="Line graph showing 2023 vs 2024 headcount, engagement, turnover" class="w-full h-28 object-cover rounded" height="120" src="https://replicate.delivery/xezq/Wa7WoQEvA8oiL1qlBJGfQto74VbCxadrM4RC4ONQ9cVaegAVA/out-0.png" width="420"/>
        </div>
        <!-- Employee Engagement Ratings -->
        <div class="bg-[#f3f4fa] rounded-lg p-5">
         <div class="text-xs text-gray-400 font-medium mb-2">
          Employee Engagement Ratings
         </div>
         <img alt="Radar chart showing employee engagement ratings" class="mx-auto w-24 h-24 object-cover" height="120" src="https://replicate.delivery/xezq/X9Kvw1bDFCYtEVIR90EU7mKgN9rYRQAsdffrNX38lYv08gAVA/out-0.png" width="120"/>
         <div class="text-xs text-gray-400 text-center mt-2">
          The higher number (B) is the maximum value
         </div>
        </div>
       </div>
       <div class="grid grid-cols-2 gap-6 mt-8">
        <!-- Upcoming Interview Table -->
        <div class="bg-[#f3f4fa] rounded-lg p-5">
         <div class="text-xs text-gray-400 font-medium mb-2">
          Upcoming Interview
         </div>
         <table class="w-full text-sm">
          <thead>
           <tr class="text-gray-500">
            <th class="text-left font-medium pb-2">
             Name
            </th>
            <th class="text-left font-medium pb-2">
             Position
            </th>
            <th class="text-left font-medium pb-2">
             Team
            </th>
            <th class="text-left font-medium pb-2">
             Time
            </th>
           </tr>
          </thead>
          <tbody>
           <tr class="border-t border-gray-200">
            <td class="py-2 flex items-center">
             <img alt="Profile photo of Ronald Richards" class="w-7 h-7 rounded-full mr-2" height="28" src="https://replicate.delivery/xezq/SK6gRg5btIIoP5fx6iEH1XttREqT7awo6VjOeWVpfzOp5BBqA/out-0.png" width="28"/>
             Ronald Richards
            </td>
            <td class="py-2">
             Senior Front End
            </td>
            <td class="py-2">
             Customer Demand
            </td>
            <td class="py-2">
             Mon, 4:00 PM
            </td>
           </tr>
           <tr class="border-t border-gray-200">
            <td class="py-2 flex items-center">
             <img alt="Profile photo of Jerome Bell" class="w-7 h-7 rounded-full mr-2" height="28" src="https://replicate.delivery/xezq/H1jGseg0USxnKyUt7MHAGnaXfUUFunvF13FD2tlMgxb08gAVA/out-0.png" width="28"/>
             Jerome Bell
            </td>
            <td class="py-2">
             Senior Front End
            </td>
            <td class="py-2">
             Customer Demand
            </td>
            <td class="py-2">
             Mon, 6:00 PM
            </td>
           </tr>
           <tr class="border-t border-gray-200">
            <td class="py-2 flex items-center">
             <img alt="Profile photo of Cody Fisher" class="w-7 h-7 rounded-full mr-2" height="28" src="https://replicate.delivery/xezq/ewS8mbrf4BtHgke1n2fOzqrKexskBLZyuji3YFxsUEYbmHEoC/out-0.png" width="28"/>
             Cody Fisher
            </td>
            <td class="py-2">
             Design Lead
            </td>
            <td class="py-2">
             UI/UX
            </td>
            <td class="py-2">
             Tue, 1:00 PM
            </td>
           </tr>
          </tbody>
         </table>
        </div>
        <!-- Country Insight Map -->
        <div class="bg-[#f3f4fa] rounded-lg p-5">
         <div class="text-xs text-gray-400 font-medium mb-2">
          Country Insight
         </div>
         <img alt="World map with highlighted regions for country insight" class="w-full h-24 object-cover rounded" height="120" src="https://replicate.delivery/xezq/bXrbhmQozAp8PxMef7XgejwYNzRDhvqVHg2UYMPh8Drm5BBqA/out-0.png" width="220"/>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
 </body>
</html>