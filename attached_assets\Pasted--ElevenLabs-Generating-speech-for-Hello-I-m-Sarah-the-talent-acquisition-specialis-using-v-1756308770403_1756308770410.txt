🎤 ElevenLabs: Generating speech for "Hello, I'm <PERSON>, the talent acquisition specialis..." using voice EXAVITQu4vr4xnSDxMaL
✅ ElevenLabs: Successfully generated 132956 bytes of audio
3:27:02 PM [express] POST /api/voice-agent/test-conversational-ai 200 in 2971ms :: {"success":true,"…
Found userId from session: fc0a601d-9a56-4e8a-8859-22d4aedfac7a
Found userId: fc0a601d-9a56-4e8a-8859-22d4aedfac7a from session. Session data: {
  "cookie": {
    "originalMaxAge": ********,
    "expires": "2025-08-28T15:27:02.004Z",
    "secure": false,
    "httpOnly": true,
    "path": "/"
  },
  "userId": "fc0a601d-9a56-4e8a-8859-22d4aedfac7a",
  "organizationId": "74cbd810-0e90-4a59-ab9e-36c3d32e4ca3",
  "role": "admin"
}
3:27:06 PM [express] GET /api/twilio-setup/info 200 in 673ms :: {"account":{"sid":"AC19aeb4eeca5b65a…
Found userId from session: fc0a601d-9a56-4e8a-8859-22d4aedfac7a
Found userId: fc0a601d-9a56-4e8a-8859-22d4aedfac7a from session. Session data: {
  "cookie": {
    "originalMaxAge": ********,
    "expires": "2025-08-28T15:27:06.322Z",
    "secure": false,
    "httpOnly": true,
    "path": "/"
  },
  "userId": "fc0a601d-9a56-4e8a-8859-22d4aedfac7a",
  "organizationId": "74cbd810-0e90-4a59-ab9e-36c3d32e4ca3",
  "role": "admin"
}
3:27:27 PM [express] GET /api/job-postings/all 304 in 594ms :: [{"id":"efe576f2-360d-4bac-b65d-d5905…
Failed to log audit event: NeonDbError: relation "audit_logs" does not exist
    at execute (file:///home/<USER>/workspace/node_modules/@neondatabase/serverless/index.mjs:1556:55)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async logAuditEvent (/home/<USER>/workspace/server/middleware/auditLogger.ts:26:5)
    at async ServerResponse.<anonymous> (/home/<USER>/workspace/server/middleware/auditLogger.ts:102:7) {
  severity: 'ERROR',
  code: '42P01',
  detail: undefined,
  hint: undefined,
  position: '13',
  internalPosition: undefined,
  internalQuery: undefined,
  where: undefined,
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'parse_relation.c',
  line: '1449',
  routine: 'parserOpenTable',
  sourceError: undefined
}