import { Router } from 'express';
import { authenticateToken } from '../auth';
import { db } from '../db';
import { interviewsV2, candidates, authUsers, agentProfiles } from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';
import { interviewInvitationService } from '../services/interviewInvitationService';
import { zoomVideoSDKService } from '../services/zoomVideoSDKService';
import { botRunnerService } from '../services/botRunnerService';
import { gmailService } from '../services/gmailService';

const router = Router();

// Test endpoint to verify API is working
router.post('/test', authenticateToken, (req, res) => {
  console.log('🧪 TEST ENDPOINT HIT - interviews-v2/test');
  console.log('📝 Request body:', JSON.stringify(req.body, null, 2));
  console.log('👤 User:', req.user?.email);
  res.json({ success: true, message: 'Test endpoint working', receivedData: req.body, user: req.user?.email });
});

// Public endpoint for candidate interview access (no authentication required)
router.get('/candidate/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('👤 Candidate accessing interview:', id);

    // Get interview with related data (no organization check for candidates)
    const [result] = await db
      .select({
        interview: interviewsV2,
        candidate: {
          id: candidates.id,
          fullName: candidates.fullName,
          email: candidates.email,
          phone: candidates.phone
        },
        agentProfile: {
          id: agentProfiles.id,
          name: agentProfiles.name,
          scriptVersion: agentProfiles.scriptVersion
        }
      })
      .from(interviewsV2)
      .leftJoin(candidates, eq(interviewsV2.candidateId, candidates.id))
      .leftJoin(agentProfiles, eq(interviewsV2.agentProfileId, agentProfiles.id))
      .where(eq(interviewsV2.id, id))
      .limit(1);

    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    // Return interview data for candidate
    res.json({
      success: true,
      interview: {
        ...result.interview,
        candidate: result.candidate,
        agentProfile: result.agentProfile
      }
    });

  } catch (error) {
    console.error('Error fetching candidate interview:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Validation schemas
const createInterviewSchema = z.object({
  candidateId: z.string().uuid('Invalid candidate ID'),
  role: z.string().min(1, 'Role is required'),
  scheduledAt: z.string().datetime('Invalid scheduled time'),
  durationMin: z.number().min(15).max(240).default(60),
  agentProfileId: z.string().uuid().optional().nullable(),
  notes: z.string().optional()
});

const updateInterviewSchema = z.object({
  scheduledAt: z.string().datetime().optional(),
  durationMin: z.number().min(15).max(240).optional(),
  agentProfileId: z.string().uuid().optional(),
  notes: z.string().optional(),
  status: z.enum(['scheduled', 'completed', 'cancelled', 'rescheduled']).optional()
});

/**
 * @swagger
 * /interviews-v2:
 *   post:
 *     summary: Schedule new interview
 *     description: Create a new interview with Zoom Video SDK session and email invitation
 *     tags: [Interviews V2]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - candidateId
 *               - role
 *               - scheduledAt
 *             properties:
 *               candidateId:
 *                 type: string
 *                 format: uuid
 *               role:
 *                 type: string
 *               scheduledAt:
 *                 type: string
 *                 format: date-time
 *               durationMin:
 *                 type: number
 *                 minimum: 15
 *                 maximum: 240
 *               agentProfileId:
 *                 type: string
 *                 format: uuid
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Interview scheduled successfully
 *       400:
 *         description: Invalid request data
 *       404:
 *         description: Candidate not found
 *       500:
 *         description: Interview scheduling failed
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    console.log('🎯 Interview creation started');
    console.log('📝 Request body:', JSON.stringify(req.body, null, 2));

    const userId = req.user?.id;

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      console.log('❌ Access denied - no user or organization');
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    console.log('✅ User authenticated:', user.email, 'Org:', user.organizationId);

    // Validate request data
    console.log('🔍 Validating request data...');
    const validatedData = createInterviewSchema.parse(req.body);
    console.log('✅ Data validated:', validatedData);

    // Verify candidate exists and belongs to organization
    console.log('🔍 Looking for candidate:', validatedData.candidateId);
    const [candidate] = await db
      .select()
      .from(candidates)
      .where(and(
        eq(candidates.id, validatedData.candidateId),
        eq(candidates.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!candidate) {
      console.log('❌ Candidate not found:', validatedData.candidateId);
      return res.status(404).json({
        success: false,
        error: 'Candidate not found'
      });
    }

    console.log('✅ Candidate found:', candidate.fullName, candidate.email);

    // Verify agent profile if provided
    if (validatedData.agentProfileId) {
      const [agentProfile] = await db
        .select()
        .from(agentProfiles)
        .where(and(
          eq(agentProfiles.id, validatedData.agentProfileId),
          eq(agentProfiles.organizationId, user.organizationId),
          eq(agentProfiles.isActive, true)
        ))
        .limit(1);

      if (!agentProfile) {
        return res.status(400).json({
          success: false,
          error: 'Invalid agent profile'
        });
      }
    }

    // Create Zoom session
    const sessionName = zoomVideoSDKService.generateInterviewSessionName('temp');
    const session = zoomVideoSDKService.createSession(sessionName);

    // Create interview
    console.log('💾 Creating interview in database...');
    const [newInterview] = await db
      .insert(interviewsV2)
      .values({
        candidateId: validatedData.candidateId,
        organizationId: user.organizationId,
        role: validatedData.role,
        scheduledAt: new Date(validatedData.scheduledAt),
        durationMin: validatedData.durationMin,
        status: 'scheduled',
        sdkType: 'video',
        roomOrMeetingId: sessionName,
        agentProfileId: validatedData.agentProfileId,
        notes: validatedData.notes,
        joinUrls: {
          candidate: `${process.env.BASE_URL || 'http://localhost:5000'}/interview-join?interviewId=`,
          host: `${process.env.BASE_URL || 'http://localhost:5000'}/interview-host?interviewId=`
        }
      })
      .returning();

    console.log('✅ Interview created with ID:', newInterview.id);

    // Update join URLs with actual interview ID
    await db
      .update(interviewsV2)
      .set({
        joinUrls: {
          candidate: `${process.env.BASE_URL || 'http://localhost:5000'}/interview-join?interviewId=${newInterview.id}`,
          host: `${process.env.BASE_URL || 'http://localhost:5000'}/interview-host?interviewId=${newInterview.id}`
        }
      })
      .where(eq(interviewsV2.id, newInterview.id));

    // Schedule bot auto-join (T-2 minutes)
    console.log('🤖 Scheduling bot auto-join...');
    try {
      await botRunnerService.scheduleAutoJoin(newInterview.id, new Date(validatedData.scheduledAt));
      console.log('✅ Bot auto-join scheduled successfully');
    } catch (botError) {
      console.error('❌ Failed to schedule bot auto-join:', botError);
    }

    // Send email invitation
    console.log('📧 Sending email invitation...');
    try {
      await sendInterviewInvitation(newInterview, candidate, user);
      console.log('✅ Email invitation sent successfully');
    } catch (emailError) {
      console.error('❌ Failed to send interview invitation email:', emailError);
      // Don't fail the interview creation if email fails
    }

    res.status(201).json({
      success: true,
      interview: {
        ...newInterview,
        candidate: {
          id: candidate.id,
          fullName: candidate.fullName,
          email: candidate.email
        }
      }
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      });
    }

    console.error('Error creating interview:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create interview'
    });
  }
});

/**
 * @swagger
 * /interviews-v2:
 *   get:
 *     summary: Get interviews
 *     description: Retrieve interviews for the organization
 *     tags: [Interviews V2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [scheduled, completed, cancelled, rescheduled]
 *       - in: query
 *         name: candidateId
 *         schema:
 *           type: string
 *           format: uuid
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *     responses:
 *       200:
 *         description: Interviews retrieved successfully
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user?.id;
    const { status, candidateId, limit = 20 } = req.query;

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Build query conditions
    const conditions = [eq(interviewsV2.organizationId, user.organizationId)];
    
    if (status) {
      conditions.push(eq(interviewsV2.status, status as any));
    }
    
    if (candidateId) {
      conditions.push(eq(interviewsV2.candidateId, candidateId as string));
    }

    // Get interviews with candidate details
    const interviews = await db
      .select({
        interview: interviewsV2,
        candidate: {
          id: candidates.id,
          fullName: candidates.fullName,
          email: candidates.email,
          phone: candidates.phone
        }
      })
      .from(interviewsV2)
      .leftJoin(candidates, eq(interviewsV2.candidateId, candidates.id))
      .where(conditions.length > 1 ? and(...conditions) : conditions[0])
      .orderBy(interviewsV2.scheduledAt)
      .limit(Number(limit));

    res.json({
      success: true,
      interviews: interviews.map(row => ({
        ...row.interview,
        candidate: row.candidate
      }))
    });

  } catch (error) {
    console.error('Error fetching interviews:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch interviews'
    });
  }
});

/**
 * @swagger
 * /interviews-v2/{id}:
 *   get:
 *     summary: Get interview by ID
 *     description: Retrieve a specific interview with full details
 *     tags: [Interviews V2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Interview retrieved successfully
 *       404:
 *         description: Interview not found
 */
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Get interview with related data
    const [result] = await db
      .select({
        interview: interviewsV2,
        candidate: {
          id: candidates.id,
          fullName: candidates.fullName,
          email: candidates.email,
          phone: candidates.phone
        },
        agentProfile: {
          id: agentProfiles.id,
          name: agentProfiles.name,
          scriptVersion: agentProfiles.scriptVersion
        }
      })
      .from(interviewsV2)
      .leftJoin(candidates, eq(interviewsV2.candidateId, candidates.id))
      .leftJoin(agentProfiles, eq(interviewsV2.agentProfileId, agentProfiles.id))
      .where(and(
        eq(interviewsV2.id, id),
        eq(interviewsV2.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    res.json({
      success: true,
      interview: {
        ...result.interview,
        candidate: result.candidate,
        agentProfile: result.agentProfile
      }
    });

  } catch (error) {
    console.error('Error fetching interview:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch interview'
    });
  }
});

/**
 * @swagger
 * /interviews-v2/{id}:
 *   put:
 *     summary: Update interview
 *     description: Update interview details
 *     tags: [Interviews V2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Interview updated successfully
 *       404:
 *         description: Interview not found
 *       400:
 *         description: Invalid request data
 */
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Validate request data
    const validatedData = updateInterviewSchema.parse(req.body);

    // Check if interview exists and belongs to organization
    const [existingInterview] = await db
      .select()
      .from(interviewsV2)
      .where(and(
        eq(interviewsV2.id, id),
        eq(interviewsV2.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!existingInterview) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    // Verify agent profile if provided
    if (validatedData.agentProfileId) {
      const [agentProfile] = await db
        .select()
        .from(agentProfiles)
        .where(and(
          eq(agentProfiles.id, validatedData.agentProfileId),
          eq(agentProfiles.organizationId, user.organizationId),
          eq(agentProfiles.isActive, true)
        ))
        .limit(1);

      if (!agentProfile) {
        return res.status(400).json({
          success: false,
          error: 'Invalid agent profile'
        });
      }
    }

    // Update interview
    const updateData: any = {
      ...validatedData,
      updatedAt: new Date()
    };

    if (validatedData.scheduledAt) {
      updateData.scheduledAt = new Date(validatedData.scheduledAt);
    }

    const [updatedInterview] = await db
      .update(interviewsV2)
      .set(updateData)
      .where(eq(interviewsV2.id, id))
      .returning();

    // If scheduled time changed, reschedule bot auto-join
    if (validatedData.scheduledAt) {
      await botRunnerService.scheduleAutoJoin(id, new Date(validatedData.scheduledAt));
    }

    res.json({
      success: true,
      interview: updatedInterview
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      });
    }

    console.error('Error updating interview:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update interview'
    });
  }
});

/**
 * @swagger
 * /interviews-v2/{id}/cancel:
 *   post:
 *     summary: Cancel interview
 *     description: Cancel a scheduled interview
 *     tags: [Interviews V2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Interview cancelled successfully
 *       404:
 *         description: Interview not found
 */
router.post('/:id/cancel', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const userId = req.user?.id;

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Check if interview exists and belongs to organization
    const [existingInterview] = await db
      .select()
      .from(interviewsV2)
      .where(and(
        eq(interviewsV2.id, id),
        eq(interviewsV2.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!existingInterview) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    // Update interview status
    const [cancelledInterview] = await db
      .update(interviewsV2)
      .set({
        status: 'cancelled',
        notes: reason ? `${existingInterview.notes || ''}\nCancellation reason: ${reason}` : existingInterview.notes,
        updatedAt: new Date()
      })
      .where(eq(interviewsV2.id, id))
      .returning();

    // End Zoom session if exists
    if (existingInterview.roomOrMeetingId) {
      zoomVideoSDKService.endSession(existingInterview.roomOrMeetingId);
    }

    // Send cancellation notification
    try {
      const [candidate] = await db
        .select()
        .from(candidates)
        .where(eq(candidates.id, existingInterview.candidateId))
        .limit(1);

      if (candidate) {
        await sendCancellationNotification(cancelledInterview, candidate, user, reason);
      }
    } catch (emailError) {
      console.warn('Failed to send cancellation notification:', emailError);
    }

    res.json({
      success: true,
      interview: cancelledInterview,
      message: 'Interview cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling interview:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel interview'
    });
  }
});

/**
 * @swagger
 * /interviews-v2/{id}/reschedule:
 *   post:
 *     summary: Reschedule interview
 *     description: Reschedule an interview to a new time
 *     tags: [Interviews V2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - newScheduledAt
 *             properties:
 *               newScheduledAt:
 *                 type: string
 *                 format: date-time
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Interview rescheduled successfully
 *       404:
 *         description: Interview not found
 *       400:
 *         description: Invalid request data
 */
router.post('/:id/reschedule', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { newScheduledAt, reason } = req.body;
    const userId = req.user?.id;

    if (!newScheduledAt) {
      return res.status(400).json({
        success: false,
        error: 'New scheduled time is required'
      });
    }

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Check if interview exists and belongs to organization
    const [existingInterview] = await db
      .select()
      .from(interviewsV2)
      .where(and(
        eq(interviewsV2.id, id),
        eq(interviewsV2.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!existingInterview) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    // Update interview with new time
    const [rescheduledInterview] = await db
      .update(interviewsV2)
      .set({
        scheduledAt: new Date(newScheduledAt),
        status: 'rescheduled',
        notes: reason ? `${existingInterview.notes || ''}\nRescheduled: ${reason}` : existingInterview.notes,
        updatedAt: new Date()
      })
      .where(eq(interviewsV2.id, id))
      .returning();

    // Reschedule bot auto-join
    await botRunnerService.scheduleAutoJoin(id, new Date(newScheduledAt));

    // Send reschedule notification
    try {
      const [candidate] = await db
        .select()
        .from(candidates)
        .where(eq(candidates.id, existingInterview.candidateId))
        .limit(1);

      if (candidate) {
        await sendRescheduleNotification(rescheduledInterview, candidate, user, reason);
      }
    } catch (emailError) {
      console.warn('Failed to send reschedule notification:', emailError);
    }

    res.json({
      success: true,
      interview: rescheduledInterview,
      message: 'Interview rescheduled successfully'
    });

  } catch (error) {
    console.error('Error rescheduling interview:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reschedule interview'
    });
  }
});

// Helper functions for email notifications
async function sendInterviewInvitation(interview: any, candidate: any, user: any): Promise<void> {
  if (!gmailService.isAuthenticated()) {
    console.warn('📧 Gmail service not authenticated, skipping email invitation');
    throw new Error('Gmail service not authenticated');
  }

  const joinUrl = (interview.joinUrls as any)?.candidate || `${process.env.BASE_URL}/interview-join?interviewId=${interview.id}`;

  const subject = `Interview Invitation - ${interview.role}`;
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Interview Invitation</h2>
      <p>Dear ${candidate.fullName},</p>

      <p>You have been invited to an interview for the <strong>${interview.role}</strong> position.</p>

      <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>Interview Details</h3>
        <p><strong>Date & Time:</strong> ${new Date(interview.scheduledAt).toLocaleString()}</p>
        <p><strong>Duration:</strong> ${interview.durationMin} minutes</p>
        <p><strong>Format:</strong> Video Interview (AI-Powered)</p>
      </div>

      <div style="text-align: center; margin: 30px 0;">
        <a href="${joinUrl}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Join Interview
        </a>
      </div>

      <div style="background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h4>What to Expect:</h4>
        <ul>
          <li>This is an AI-powered technical interview</li>
          <li>Please ensure you have a stable internet connection</li>
          <li>Test your camera and microphone beforehand</li>
          <li>Join the meeting 5 minutes early</li>
        </ul>
      </div>

      ${interview.notes ? `<p><strong>Additional Notes:</strong> ${interview.notes}</p>` : ''}

      <p>If you have any questions or need to reschedule, please contact us immediately.</p>

      <p>Best regards,<br>The Hiring Team</p>
    </div>
  `;

  await gmailService.sendEmail({
    to: candidate.email,
    subject,
    html: htmlContent
  });
}

async function sendCancellationNotification(interview: any, candidate: any, user: any, reason?: string): Promise<void> {
  if (!gmailService.isAuthenticated()) return;

  const subject = `Interview Cancelled - ${interview.role}`;
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Interview Cancellation</h2>
      <p>Dear ${candidate.fullName},</p>

      <p>We regret to inform you that your interview for the <strong>${interview.role}</strong> position scheduled for ${new Date(interview.scheduledAt).toLocaleString()} has been cancelled.</p>

      ${reason ? `<p><strong>Reason:</strong> ${reason}</p>` : ''}

      <p>We apologize for any inconvenience this may cause. Our team will be in touch if we would like to reschedule.</p>

      <p>Best regards,<br>The Hiring Team</p>
    </div>
  `;

  await gmailService.sendEmail({
    to: candidate.email,
    subject,
    html: htmlContent
  });
}

async function sendRescheduleNotification(interview: any, candidate: any, user: any, reason?: string): Promise<void> {
  if (!gmailService.isAuthenticated()) return;

  const joinUrl = (interview.joinUrls as any)?.candidate || `${process.env.BASE_URL}/interview-join?interviewId=${interview.id}`;

  const subject = `Interview Rescheduled - ${interview.role}`;
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>Interview Rescheduled</h2>
      <p>Dear ${candidate.fullName},</p>

      <p>Your interview for the <strong>${interview.role}</strong> position has been rescheduled.</p>

      <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>New Interview Details</h3>
        <p><strong>Date & Time:</strong> ${new Date(interview.scheduledAt).toLocaleString()}</p>
        <p><strong>Duration:</strong> ${interview.durationMin} minutes</p>
      </div>

      ${reason ? `<p><strong>Reason for Reschedule:</strong> ${reason}</p>` : ''}

      <div style="text-align: center; margin: 30px 0;">
        <a href="${joinUrl}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">
          Join Interview
        </a>
      </div>

      <p>Please make note of the new time. We apologize for any inconvenience.</p>

      <p>Best regards,<br>The Hiring Team</p>
    </div>
  `;

  await gmailService.sendEmail({
    to: candidate.email,
    subject,
    html: htmlContent
  });
}

/**
 * @swagger
 * /interviews-v2/{id}/send-invitation:
 *   post:
 *     summary: Send interview invitation
 *     description: Send email invitation with calendar attachment to candidate
 *     tags: [Interviews V2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Invitation sent successfully
 *       404:
 *         description: Interview not found
 *       500:
 *         description: Failed to send invitation
 */
router.post('/:id/send-invitation', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Get interview with candidate details
    const [result] = await db
      .select({
        interview: interviewsV2,
        candidate: candidates
      })
      .from(interviewsV2)
      .leftJoin(candidates, eq(interviewsV2.candidateId, candidates.id))
      .where(and(
        eq(interviewsV2.id, id),
        eq(interviewsV2.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    const { interview, candidate } = result;

    if (!candidate) {
      return res.status(400).json({
        success: false,
        error: 'Candidate information not found'
      });
    }

    if (!interview.scheduledAt) {
      return res.status(400).json({
        success: false,
        error: 'Interview must be scheduled before sending invitation'
      });
    }

    // Prepare invitation data
    const baseUrl = process.env.BASE_URL || 'http://localhost:5000';
    const invitationData = {
      interviewId: interview.id,
      candidateEmail: candidate.email,
      candidateName: candidate.fullName || candidate.name || 'Candidate',
      interviewerName: user.fullName || user.email || 'Interviewer',
      role: interview.role,
      scheduledAt: interview.scheduledAt,
      duration: interview.duration || 60,
      zoomJoinUrl: interview.zoomJoinUrl || `${baseUrl}/zoom/join/${interview.id}`,
      candidateJoinUrl: `${baseUrl}/candidate/join/${interview.id}`,
      organizationName: user.organizationName || 'Company'
    };

    // Send invitation
    const success = await interviewInvitationService.sendInterviewInvitation(invitationData);

    if (success) {
      // Update interview status
      await db
        .update(interviewsV2)
        .set({
          status: 'invited',
          invitationSentAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(interviewsV2.id, id));

      res.json({
        success: true,
        message: 'Interview invitation sent successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to send interview invitation'
      });
    }

  } catch (error) {
    console.error('Error sending interview invitation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send interview invitation'
    });
  }
});

/**
 * @swagger
 * /interviews-v2/{id}/cancel:
 *   post:
 *     summary: Cancel interview and notify candidate
 *     description: Cancel interview and send cancellation notification
 *     tags: [Interviews V2]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *                 description: Reason for cancellation
 *     responses:
 *       200:
 *         description: Interview cancelled successfully
 *       404:
 *         description: Interview not found
 */
router.post('/:id/cancel', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const userId = req.user?.id;

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Get interview with candidate details
    const [result] = await db
      .select({
        interview: interviewsV2,
        candidate: candidates
      })
      .from(interviewsV2)
      .leftJoin(candidates, eq(interviewsV2.candidateId, candidates.id))
      .where(and(
        eq(interviewsV2.id, id),
        eq(interviewsV2.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    const { interview, candidate } = result;

    // Send cancellation notification if candidate exists and invitation was sent
    if (candidate && interview.invitationSentAt) {
      const baseUrl = process.env.BASE_URL || 'http://localhost:5000';
      const invitationData = {
        interviewId: interview.id,
        candidateEmail: candidate.email,
        candidateName: candidate.fullName || candidate.name || 'Candidate',
        interviewerName: user.fullName || user.email || 'Interviewer',
        role: interview.role,
        scheduledAt: interview.scheduledAt || new Date(),
        duration: interview.duration || 60,
        zoomJoinUrl: interview.zoomJoinUrl || `${baseUrl}/zoom/join/${interview.id}`,
        candidateJoinUrl: `${baseUrl}/candidate/join/${interview.id}`,
        organizationName: user.organizationName || 'Company'
      };

      await interviewInvitationService.sendCancellationNotification(invitationData, reason);
    }

    // Update interview status
    await db
      .update(interviewsV2)
      .set({
        status: 'cancelled',
        cancelledAt: new Date(),
        cancellationReason: reason,
        updatedAt: new Date()
      })
      .where(eq(interviewsV2.id, id));

    res.json({
      success: true,
      message: 'Interview cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling interview:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel interview'
    });
  }
});

export default router;

