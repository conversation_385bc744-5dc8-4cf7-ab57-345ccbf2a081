# Interview Automation System Architecture

## Overview
This document outlines the architecture for the comprehensive interview automation system that integrates Zoom Video SDK, ElevenLabs Conversational AI, and automated interview orchestration.

## System Components

### 1. Core Flow
```
Admin Schedules → Email Invite → Candidate Joins → Agent Auto-Joins → Interview → Recording → Transcript → Summary
```

### 2. Technology Stack

#### Backend (Express/Node.js)
- **Zoom Video SDK**: Meeting container with deep audio/video control
- **ElevenLabs Conversational AI**: WebRTC/WebSocket session for interview agent
- **Puppeteer**: Headless browser automation for bot auto-join
- **Existing Services**: Twilio, OpenAI, Gmail OAuth, PostgreSQL

#### Frontend (React/TypeScript)
- **Admin Interface**: Interview scheduling and management
- **Candidate Join Page**: Zoom Video SDK web client wrapper
- **Existing UI**: Radix UI components, Tailwind CSS

### 3. Database Schema Extensions

#### New Tables
```sql
-- Interview management
interviews_v2 (
  id, candidate_id, role, scheduled_at, duration_min, status,
  sdk_type, room_or_meeting_id, join_urls, host_token_scope,
  organization_id, created_at, updated_at
)

-- Agent configuration
agent_profiles (
  id, name, script_version, rubric_json, safety_json,
  organization_id, created_at, updated_at
)

-- Interview execution tracking
interview_runs (
  id, interview_id, started_at, ended_at, status, metrics_json,
  organization_id, created_at, updated_at
)

-- Artifacts storage
interview_artifacts (
  id, interview_id, type, uri, meta_json,
  organization_id, created_at, updated_at
)
```

### 4. API Endpoints

#### Interview Management
- `POST /api/interviews` - Schedule interview + send email
- `GET /api/interviews/:id` - Get interview details
- `POST /api/interviews/:id/cancel` - Cancel interview
- `POST /api/interviews/:id/reschedule` - Reschedule interview

#### Zoom Integration
- `GET /api/zoom/token?role=candidate|host&interviewId=...` - JWT minting
- `POST /api/webhooks/zoom` - Meeting events webhook

#### Agent Management
- `GET /api/agent-profiles` - List agent profiles
- `POST /api/agent-profiles` - Create agent profile
- `PUT /api/agent-profiles/:id` - Update agent profile

#### Internal Services
- `POST /api/jobs/auto-join` - Enqueue bot auto-join
- `POST /api/summaries/:interviewId` - Generate summary

### 5. Audio Pipeline Architecture

#### Zoom → ElevenLabs (Upstream)
1. Zoom Video SDK captures remote participant audio
2. WebAudio API processes PCM/Opus frames
3. Audio forwarded to ElevenLabs streaming API via WebSocket

#### ElevenLabs → Zoom (Downstream)
1. ElevenLabs generates response audio frames
2. Audio injected as bot "microphone" track in Zoom
3. Virtual audio device handles frame injection

#### Recording Pipeline
1. Dual-channel recording (participant + agent)
2. Local recording with cloud upload to S3/GCS
3. Real-time transcription using Whisper/STT
4. Automated summary generation with scoring

### 6. Bot Auto-Join Process

#### T-2 Minutes Before Interview
1. Job scheduler triggers Bot Runner
2. Puppeteer launches headless Chromium
3. Loads minimal Video SDK web app as host
4. Subscribes to remote audio streams
5. Initializes WebRTC peer to ElevenLabs
6. Starts recording session

#### During Interview
1. ElevenLabs follows structured instruction set
2. Agent enforces timing and adjusts difficulty
3. Optional supervisor shadow join (read-only)
4. Continuous audio bridging and recording

#### Interview End
1. Agent issues polite close
2. Recording stops and uploads
3. Bot leaves meeting
4. Server flags interview completed

### 7. Security & Compliance

#### Data Protection
- Organization-scoped data isolation
- Encrypted audio storage
- Secure token management
- Audit logging for all actions

#### Consent Management
- Recording consent UI display
- Verbal consent at interview start
- GDPR/CCPA compliance measures
- Data retention policies

### 8. Deployment Considerations

#### Environment Variables
```
ZOOM_SDK_KEY=...
ZOOM_SDK_SECRET=...
ELEVENLABS_API_KEY=...
ELEVENLABS_AGENT_ID=...
OPENAI_API_KEY=...
```

#### Infrastructure Requirements
- Headless browser support (Puppeteer)
- WebSocket connections for real-time audio
- File storage for recordings (S3/GCS)
- Job queue for scheduled tasks

### 9. Integration Points

#### Existing Systems
- Multi-tenant authentication (JWT)
- Gmail OAuth for email invitations
- Candidate management system
- Job posting integration
- Resume analysis pipeline

#### New Integrations
- Zoom Video SDK for meetings
- ElevenLabs for AI interviewer
- Puppeteer for automation
- Audio processing pipeline
- Transcription services

### 10. Scalability Considerations

#### Performance
- Concurrent interview support
- Audio processing optimization
- Database query optimization
- CDN for static assets

#### Monitoring
- Interview success rates
- Audio quality metrics
- Bot performance tracking
- Error rate monitoring

## Next Steps

1. **Database Schema Implementation** - Extend existing schema
2. **Zoom SDK Integration** - Token minting and session management
3. **Agent Profile System** - Configuration management
4. **Bot Runner Development** - Puppeteer automation
5. **Audio Pipeline** - WebRTC bridge implementation
6. **Frontend Development** - Admin and candidate interfaces
7. **Testing & QA** - Comprehensive test coverage
8. **Documentation** - Technical and user guides

## Risk Mitigation

### Technical Risks
- Audio latency and quality issues
- Browser compatibility challenges
- Zoom SDK rate limiting
- ElevenLabs service availability

### Mitigation Strategies
- Fallback audio processing
- Progressive enhancement
- Rate limiting and queuing
- Service health monitoring

This architecture provides a robust foundation for the interview automation system while leveraging existing infrastructure and maintaining security standards.
