import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function AuthDebugger() {
  const { user } = useAuth();
  
  const testAuth = async () => {
    const token = localStorage.getItem('auth_token');
    console.log('Current token:', token ? `${token.substring(0, 50)}...` : 'None');
    console.log('Current user:', user);
    
    try {
      const response = await fetch('/api/auth/user', {
        credentials: 'include',
        headers: token ? { 'Authorization': `Bearer ${token}` } : {}
      });
      const data = await response.json();
      console.log('Auth check response:', data);
    } catch (error) {
      console.error('Auth check error:', error);
    }
  };

  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle>Authentication Debug</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <p><strong>User:</strong> {user ? user.email : 'Not logged in'}</p>
          <p><strong>Token:</strong> {localStorage.getItem('auth_token') ? 'Present' : 'Missing'}</p>
          <p><strong>Organization:</strong> {user?.organization_id || 'None'}</p>
          <Button onClick={testAuth}>Test Auth</Button>
        </div>
      </CardContent>
    </Card>
  );
}