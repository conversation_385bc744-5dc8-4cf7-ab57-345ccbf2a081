📊 Full Status Data: {
  callSid: 'CA08bbb113dfb79ae13ac3efca09aa66f9',
  callStatus: 'completed',
  answeredBy: undefined,
  duration: '2',
  timestamp: '2025-09-10T18:12:00.094Z',
  from: '+14089006967',
  to: '+14086219491',
  direction: 'outbound-api'
}
6:12:00 PM [express] POST /api/twilio-status 200 in 401ms
📞 STEP 4: ElevenLabs connection closed
📞 STEP 4: Close code: 1002 Reason: No user message received for a long period of time - probably a network error occurred.
6:12:07 PM [express] POST /api/elevenlabs/conversation-ended 200 in 9ms
Token verification error: Error: Invalid token
    at verifyToken (/home/<USER>/workspace/server/auth.ts:46:11)
    at authenticateToken (/home/<USER>/workspace/server/auth.ts:90:25)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/router/index.js:175:3)
    at router (/home/<USER>/workspace/node_modules/express/lib/router/index.js:47:12)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:61:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at Immediate._onImmediate (/home/<USER>/workspace/node_modules/express-session/index.js:514:7)
    at process.processImmediate (node:internal/timers:485:21)
6:12:32 PM [express] GET /api/auth/validate 401 in 2ms :: {"error":"Invalid token"}
Token verification error: Error: Invalid token
    at verifyToken (/home/<USER>/workspace/server/auth.ts:46:11)
    at authenticateToken (/home/<USER>/workspace/server/auth.ts:90:25)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)