import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function addSureshCandidate() {
  try {
    console.log('🚀 Adding <PERSON><PERSON> Arumugam as test candidate...');
    
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    const sql = neon(process.env.DATABASE_URL);
    
    // Get the first organization ID
    const organizations = await sql`SELECT id FROM organizations LIMIT 1`;
    if (organizations.length === 0) {
      throw new Error('No organizations found. Please create an organization first.');
    }
    
    const organizationId = organizations[0].id;
    console.log(`📋 Using organization ID: ${organizationId}`);
    
    // Suresh candidate data
    const sureshCandidate = {
      email: '<EMAIL>',
      fullName: 'Suresh Arumugam',
      phone: '******-0199',
      location: 'Chennai, India',
      currentCompany: 'Tech Solutions Inc',
      currentPosition: 'Senior Full Stack Developer',
      skills: ['JavaScript', 'React', 'Node.js', 'Python', 'AWS', 'MongoDB'],
      experienceYears: 8,
      education: 'BE Computer Science',
      status: 'approved_for_interview'
    };
    
    try {
      // Check if candidate already exists
      const existing = await sql`
        SELECT id FROM candidates 
        WHERE email = ${sureshCandidate.email} AND organization_id = ${organizationId}
      `;
      
      if (existing.length > 0) {
        console.log(`⚠️  Candidate ${sureshCandidate.fullName} already exists, updating status...`);
        await sql`
          UPDATE candidates 
          SET status = ${sureshCandidate.status},
              current_position = ${sureshCandidate.currentPosition},
              current_company = ${sureshCandidate.currentCompany},
              skills = ${sureshCandidate.skills},
              experience_years = ${sureshCandidate.experienceYears},
              education = ${sureshCandidate.education},
              phone = ${sureshCandidate.phone},
              location = ${sureshCandidate.location}
          WHERE email = ${sureshCandidate.email} AND organization_id = ${organizationId}
        `;
        console.log('✅ Candidate updated successfully');
      } else {
        console.log(`✅ Adding candidate: ${sureshCandidate.fullName}`);
        await sql`
          INSERT INTO candidates (
            email, full_name, phone, location, current_company, current_position,
            skills, experience_years, education, status, organization_id
          ) VALUES (
            ${sureshCandidate.email}, ${sureshCandidate.fullName}, ${sureshCandidate.phone}, 
            ${sureshCandidate.location}, ${sureshCandidate.currentCompany}, ${sureshCandidate.currentPosition},
            ${sureshCandidate.skills}, ${sureshCandidate.experienceYears}, ${sureshCandidate.education}, 
            ${sureshCandidate.status}, ${organizationId}
          )
        `;
        console.log('✅ Candidate added successfully');
      }
    } catch (error) {
      console.error(`❌ Error processing candidate ${sureshCandidate.fullName}:`, error);
    }
    
    // Verify the candidate was added/updated
    const verification = await sql`
      SELECT id, full_name, email, status, current_position 
      FROM candidates 
      WHERE email = ${sureshCandidate.email} AND organization_id = ${organizationId}
    `;
    
    if (verification.length > 0) {
      console.log('🎉 Verification successful!');
      console.log('📋 Candidate details:');
      console.log(`   - ID: ${verification[0].id}`);
      console.log(`   - Name: ${verification[0].full_name}`);
      console.log(`   - Email: ${verification[0].email}`);
      console.log(`   - Status: ${verification[0].status}`);
      console.log(`   - Position: ${verification[0].current_position}`);
      console.log('✅ Ready for interview scheduling!');
    } else {
      console.log('❌ Verification failed - candidate not found');
    }
    
  } catch (error) {
    console.error('❌ Error adding Suresh candidate:', error);
    process.exit(1);
  }
}

addSureshCandidate();
