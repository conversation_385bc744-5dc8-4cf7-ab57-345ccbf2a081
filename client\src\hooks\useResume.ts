import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Create apiRequest function for API calls
async function apiRequest(method: string, url: string, body?: any) {
  const options: RequestInit = {
    method,
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (body) {
    options.body = JSON.stringify(body);
  }

  const response = await fetch(url, options);
  if (!response.ok) {
    throw new Error(`${response.status}: ${response.statusText}`);
  }
  return response;
}

interface ResumeInfo {
  resumeUrl: string;
  fileName: string;
  fileSize: number;
  hasResume: boolean;
}

export function useResumeInfo(candidateId: string) {
  return useQuery({
    queryKey: ['/api/files/info', candidateId],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/files/info/${candidateId}`);
      return response.json() as Promise<ResumeInfo>;
    },
    enabled: !!candidateId,
    retry: false
  });
}

export function useResumeUpload() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ candidateId, file }: { candidateId: string; file: File }) => {
      const formData = new FormData();
      formData.append('resume', file);

      const response = await fetch(`/api/files/upload/${candidateId}`, {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to upload resume');
      }

      return response.json();
    },
    onSuccess: (_, { candidateId }) => {
      // Invalidate resume info query to refetch updated data
      queryClient.invalidateQueries({
        queryKey: ['/api/files/info', candidateId]
      });
    }
  });
}

export function useResumeDownload() {
  return {
    downloadResume: (candidateId: string, filename: string) => {
      // Create a direct download link
      const downloadUrl = `/api/files/download/${candidateId}/${filename}`;
      
      // Create temporary link and trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = '';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}