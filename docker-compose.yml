version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: hrms-postgres
    environment:
      POSTGRES_DB: hrms_production
      POSTGRES_USER: hrms_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-secure_password_change_this}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"
    networks:
      - hrms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hrms_user -d hrms_production"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: hrms-redis
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - hrms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Main Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: hrms-app
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://hrms_user:${DB_PASSWORD:-secure_password_change_this}@postgres:5432/hrms_production
      REDIS_URL: redis://redis:6379
    env_file:
      - .env.production
    volumes:
      - ./uploads:/app/uploads
      - ./recordings:/app/recordings
      - ./logs:/app/logs
    ports:
      - "5000:5000"
      - "9090:9090"  # Metrics port
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - hrms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: hrms-nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - app
    networks:
      - hrms-network
    restart: unless-stopped

  # Monitoring with Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: hrms-prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9091:9090"
    networks:
      - hrms-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana for visualization (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: hrms-grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - hrms-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: hrms-backup
    environment:
      PGPASSWORD: ${DB_PASSWORD:-secure_password_change_this}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    depends_on:
      - postgres
    networks:
      - hrms-network
    restart: "no"
    profiles:
      - backup
    command: >
      sh -c "
        echo '0 2 * * * /backup.sh' | crontab - &&
        crond -f
      "

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  hrms-network:
    driver: bridge
