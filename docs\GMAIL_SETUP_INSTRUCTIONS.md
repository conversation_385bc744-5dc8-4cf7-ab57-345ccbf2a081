# Gmail OAuth Setup Instructions

## Issue: Redirect URI Mismatch

The Gmail OAuth configuration is currently pointing to the old Lovable domain (`steorra.lovable.app`). To fix this, you need to update the Google Cloud Console OAuth settings.

## Steps to Fix:

### 1. Access Google Cloud Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project (the one associated with the client secret file)
3. Navigate to "APIs & Services" > "Credentials"

### 2. Update OAuth 2.0 Client IDs
1. Find your OAuth 2.0 client ID in the credentials list
2. Click on it to edit
3. In the "Authorized redirect URIs" section, you need to:
   - Remove: `https://steorra.lovable.app/api/gmail/callback`
   - Add: `https://[YOUR-REPLIT-DOMAIN]/api/gmail/callback`

### 3. Get Your Replit Domain
Your current Replit domain should be something like:
`https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev`

You can find this in your browser's address bar when viewing the app.

### 4. Update the Redirect URI
Add this exact URL to your Google OAuth settings:
`https://[YOUR-REPLIT-DOMAIN]/api/gmail/callback`

### 5. Save Changes
Click "Save" in the Google Cloud Console.

## Alternative Solution: Use Manual Token

If you can't update the Google Cloud Console settings, we can implement a manual token flow where:
1. You authorize the app using a temporary redirect
2. Copy the authorization code manually
3. Paste it into the app interface

Would you like me to implement the manual token flow instead?