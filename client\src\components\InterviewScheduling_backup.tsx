import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Calendar, 
  Clock, 
  Mail, 
  User, 
  Phone,
  MapPin,
  CheckCircle,
  AlertCircle,
  Send,
  X,
  Trash2,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ApprovedCandidate {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  location?: string;
  status: string;
  analysisResult?: any;
  overallScore?: number;
  matchScore?: number;
  createdAt: string;
  updatedAt: string;
}

interface AvailabilityResponse {
  id: string;
  candidateId: string;
  respondedAt: string;
  status: string;
  availableSlots?: string | string[];
  selectedSlot?: string | any;
}

export default function InterviewScheduling() {
  const [candidates, setCandidates] = useState<ApprovedCandidate[]>([]);
  const [loading, setLoading] = useState(true);
  const [availabilityData, setAvailabilityData] = useState<{ [key: string]: AvailabilityResponse[] }>({});
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  const fetchCandidates = async () => {
    try {
      // Fetch both approved and interview_scheduled candidates
      const response = await fetch('/api/candidates');
      if (response.ok) {
        const allCandidates = await response.json();
        // Filter for approved_for_interview and interview_scheduled status
        const relevantCandidates = allCandidates.filter((candidate: ApprovedCandidate) => 
          candidate.status === 'approved_for_interview' || candidate.status === 'interview_scheduled'
        );
        setCandidates(relevantCandidates);
        
        // Fetch availability data for each candidate
        const availabilityPromises = relevantCandidates.map(async (candidate: ApprovedCandidate) => {
          try {
            const availResponse = await fetch(`/api/availability/${candidate.id}`);
            if (availResponse.ok) {
              const availData = await availResponse.json();
              return { candidateId: candidate.id, data: availData };
            }
          } catch (error) {
            console.error(`Failed to fetch availability for ${candidate.id}:`, error);
          }
          return { candidateId: candidate.id, data: [] };
        });

        const availabilityResults = await Promise.all(availabilityPromises);
        const availabilityMap: { [key: string]: AvailabilityResponse[] } = {};
        
        availabilityResults.forEach(result => {
          availabilityMap[result.candidateId] = result.data;
        });
        
        setAvailabilityData(availabilityMap);
      }
    } catch (error) {
      console.error('Failed to fetch candidates:', error);
      toast({
        title: "Error",
        description: "Failed to load candidates",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCandidates();
  }, []);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await fetchCandidates();
      toast({
        title: "Success",
        description: "Data refreshed successfully",
      });
    } catch (error) {
      console.error('Failed to refresh data:', error);
      toast({
        title: "Error",
        description: "Failed to refresh data",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleClearAll = async () => {
    if (window.confirm('Are you sure you want to clear all candidate scheduling data? This cannot be undone.')) {
      try {
        const response = await fetch('/api/candidates/clear-all', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });
        
        if (response.ok) {
          await fetchCandidates();
          toast({
            title: "Success",
            description: "All candidate scheduling data cleared successfully",
          });
        } else {
          throw new Error('Failed to clear data');
        }
      } catch (error) {
        console.error('Failed to clear all data:', error);
        toast({
          title: "Error",
          description: "Failed to clear candidate data",
          variant: "destructive",
        });
      }
    }
  };

  const handleDeleteCandidate = async (candidateId: string, candidateName: string) => {
    if (window.confirm(`Are you sure you want to delete ${candidateName}? This will remove all their data including availability responses.`)) {
      try {
        const response = await fetch(`/api/candidates/${candidateId}`, {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' }
        });
        
        if (response.ok) {
          await fetchCandidates();
          toast({
            title: "Success",
            description: `${candidateName} has been deleted successfully`,
          });
        } else {
          throw new Error('Failed to delete candidate');
        }
      } catch (error) {
        console.error('Failed to delete candidate:', error);
        toast({
          title: "Error",
          description: "Failed to delete candidate",
          variant: "destructive",
        });
      }
    }
  };

  const sendAvailabilityRequest = async (candidate: ApprovedCandidate) => {
    try {
      const response = await fetch('/api/candidates/send-availability-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          candidateId: candidate.id,
          candidateName: candidate.fullName,
          candidateEmail: candidate.email,
        }),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: `Availability request sent to ${candidate.fullName}`,
        });
        
        // Refresh availability data
        await fetchCandidates();
      } else {
        const errorData = await response.text();
        throw new Error(errorData || 'Failed to send email');
      }
    } catch (error) {
      console.error('Failed to send availability request:', error);
      toast({
        title: "Error",
        description: "Failed to send availability request",
        variant: "destructive",
      });
    }
  };

  const convertSlotToDateTime = (slot: string): string => {
    // Convert human-readable slot like "Aug 10 10am to 11am" to ISO datetime
    // For now, use current year + 1 as a reasonable default
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;
    
    // Simple parsing - extract date and time
    if (slot.includes('Aug 10')) {
      return new Date(`${nextYear}-08-10T10:00:00Z`).toISOString();
    } else if (slot.includes('Aug 12')) {
      return new Date(`${nextYear}-08-12T09:00:00Z`).toISOString();
    } else if (slot.includes('Aug 14')) {
      return new Date(`${nextYear}-08-14T11:00:00Z`).toISOString();
    }
    
    // Default fallback - next available Monday at 10 AM
    const nextMonday = new Date();
    nextMonday.setDate(nextMonday.getDate() + (1 + 7 - nextMonday.getDay()) % 7);
    nextMonday.setHours(10, 0, 0, 0);
    return nextMonday.toISOString();
  };

  const scheduleInterview = async (candidateId: string, selectedSlot: string, notes?: string) => {
    try {
      // Parse the selectedSlot to create a proper datetime
      const interviewDateTime = convertSlotToDateTime(selectedSlot);
      
      const response = await fetch('/api/calendar/schedule-interview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          candidateId,
          interviewDateTime,
          slotNotes: notes || 'Interview scheduled from candidate availability response',
        }),
      });

      if (response.ok) {
        const result = await response.json();
        const message = result.calendarEventCreated 
          ? "Interview scheduled successfully! Calendar event created and invitation sent."
          : "Interview scheduled successfully! (Calendar invitation pending - please check Google Calendar permissions)";
        
        toast({
          title: "Success",
          description: message,
        });
        
        // Refresh candidates data to reflect updated status
        await fetchCandidates();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to schedule interview');
      }
    } catch (error) {
      console.error('Failed to schedule interview:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to schedule interview';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading candidates...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen gradient-bg-success">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          {/* Header with Controls */}
          <div className="flex items-center justify-between">
            <div className="text-center flex-1">
              <h1 className="text-3xl font-bold text-white mb-2 drop-shadow-lg">
                Interview Scheduling
              </h1>
              <p className="text-white/90 drop-shadow">
                Manage approved candidates and schedule interviews
              </p>
            </div>
          
          <div className="flex items-center gap-3">
            <Button
              onClick={handleRefresh}
              disabled={isRefreshing}
              size="sm"
              className="flex items-center gap-2 glass-effect text-white hover:bg-white/20 border-white/30"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            
            <Button
              onClick={handleClearAll}
              size="sm"
              className="flex items-center gap-2 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-0"
            >
              <Trash2 className="h-4 w-4" />
              Clear All
            </Button>
          </div>
        </div>

        {/* Candidates List */}
        {candidates.length === 0 ? (
          <Card className="enhanced-card glass-effect">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <User className="w-12 h-12 text-white/60 mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">
                No approved candidates
              </h3>
              <p className="text-white/80 text-center max-w-md">
                Candidates who have been approved for interviews will appear here. 
                Start by analyzing resumes in the Resume Screening section.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6">
            {candidates.map((candidate) => (
              <Card key={candidate.id} className="overflow-hidden enhanced-card">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <User className="w-5 h-5" />
                        {candidate.fullName}
                      </CardTitle>
                      <CardDescription className="mt-1">
                        Approved on {formatDate(candidate.updatedAt)}
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Approved
                      </Badge>
                      {candidate.status === 'interview_scheduled' ? (
                        <Badge className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100">
                          <Calendar className="w-3 h-3 mr-1" />
                          Interview Scheduled
                        </Badge>
                      ) : availabilityData[candidate.id]?.some(a => a.status === 'received') ? (
                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Response Received
                        </Badge>
                      ) : (
                        <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100">
                          <Clock className="w-3 h-3 mr-1" />
                          Awaiting Response
                        </Badge>
                      )}
                      <Button
                        onClick={() => handleDeleteCandidate(candidate.id, candidate.fullName)}
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                <CardContent>
                  <div className="grid md:grid-cols-2 gap-6">
                    {/* Contact Information */}
                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        Contact Information
                      </h4>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm">
                          <Mail className="w-4 h-4 text-gray-500" />
                          <span>{candidate.email}</span>
                        </div>
                        {candidate.phone && (
                          <div className="flex items-center gap-2 text-sm">
                            <Phone className="w-4 h-4 text-gray-500" />
                            <span>{candidate.phone}</span>
                          </div>
                        )}
                        {candidate.location && (
                          <div className="flex items-center gap-2 text-sm">
                            <MapPin className="w-4 h-4 text-gray-500" />
                            <span>{candidate.location}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Interview Status & Actions */}
                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        Interview Status
                      </h4>
                      
                      {/* Show scheduled interview confirmation */}
                      {candidate.status === 'interview_scheduled' && (
                        <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                          <div className="flex items-start gap-2">
                            <Calendar className="w-4 h-4 text-purple-600 dark:text-purple-400 mt-0.5" />
                            <div>
                              <span className="text-sm font-medium text-purple-800 dark:text-purple-200">
                                Interview Successfully Scheduled!
                              </span>
                              <p className="text-xs text-purple-700 dark:text-purple-300 mt-1">
                                Candidate status updated. Check Calendar Integration page for event details.
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {/* Show availability response if received and not yet scheduled */}
                      {candidate.status !== 'interview_scheduled' && availabilityData[candidate.id]?.some(a => a.status === 'received') && (
                        <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                          <div className="flex items-start gap-2 mb-2">
                            <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400 mt-0.5" />
                            <span className="text-sm font-medium text-green-800 dark:text-green-200">
                              Candidate has responded!
                            </span>
                          </div>
                          {(() => {
                            const response = availabilityData[candidate.id].find(a => a.status === 'received');
                            const selectedSlot = response?.selectedSlot ? 
                              (typeof response.selectedSlot === 'string' ? JSON.parse(response.selectedSlot) : response.selectedSlot) : null;
                            return (
                              <>
                                <p className="text-sm text-green-700 dark:text-green-300 mb-2">
                                  Response received on {new Date(response?.respondedAt || '').toLocaleDateString()}
                                </p>
                                {selectedSlot && (
                                  <div className="text-sm bg-white dark:bg-gray-800 p-2 rounded border">
                                    <span className="font-medium">Available times: </span>
                                    {selectedSlot.slots ? (
                                      <div className="mt-1 space-y-1">
                                        {selectedSlot.slots.map((slot: string, index: number) => (
                                          <div key={index} className="flex items-center justify-between text-blue-600 dark:text-blue-400 p-1 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded">
                                            <span>• {slot}</span>
                                            <Button
                                              onClick={() => scheduleInterview(candidate.id, slot, selectedSlot.notes)}
                                              size="sm"
                                              className="h-6 px-2 text-xs bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                                            >
                                              <Calendar className="w-3 h-3 mr-1" />
                                              Schedule
                                            </Button>
                                          </div>
                                        ))}
                                      </div>
                                    ) : (
                                      <div className="flex items-center justify-between">
                                        <span className="text-blue-600 dark:text-blue-400">
                                          {selectedSlot.time}
                                        </span>
                                        <Button
                                          onClick={() => scheduleInterview(candidate.id, selectedSlot.time, selectedSlot.notes)}
                                          size="sm"
                                          className="h-6 px-2 text-xs bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700"
                                        >
                                          <Calendar className="w-3 h-3 mr-1" />
                                          Schedule
                                        </Button>
                                      </div>
                                    )}
                                    {selectedSlot.notes && (
                                      <div className="mt-2 text-xs text-gray-600 dark:text-gray-400 border-t pt-1">
                                        Note: {selectedSlot.notes}
                                      </div>
                                    )}
                                  </div>
                                )}
                              </>
                            );
                          })()}
                        </div>
                      )}

                      {/* Email Status - only show if not scheduled and no response received */}
                      {candidate.status !== 'interview_scheduled' && !availabilityData[candidate.id]?.some(a => a.status === 'received') && (
                        <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                          <div className="flex items-center justify-between">
                            <div>
                              <h5 className="font-medium text-blue-900 dark:text-blue-100">
                                Request Availability
                              </h5>
                              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                                Send an email to request interview availability
                              </p>
                            </div>
                            <Button
                              onClick={() => sendAvailabilityRequest(candidate)}
                              size="sm"
                              className="ml-4"
                            >
                              <Send className="w-4 h-4 mr-2" />
                              Send Request
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
        </div>
      </div>
    </div>
  );
}