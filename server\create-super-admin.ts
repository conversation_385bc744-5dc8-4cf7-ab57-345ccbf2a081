#!/usr/bin/env tsx

/**
 * CLI utility to create super admin users
 * Usage: npx tsx server/create-super-admin.ts
 */

import { db } from './db';
import { authUsers } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { hashPassword } from './auth';
import * as readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
}

function askPassword(question: string): Promise<string> {
  return new Promise((resolve) => {
    // Hide password input
    const stdin = process.stdin;
    stdin.setRawMode(true);
    stdin.resume();
    stdin.setEncoding('utf8');
    
    process.stdout.write(question);
    
    let password = '';
    const onData = (char: string) => {
      if (char === '\n' || char === '\r' || char === '\u0004') {
        stdin.removeListener('data', onData);
        stdin.setRawMode(false);
        stdin.pause();
        process.stdout.write('\n');
        resolve(password);
      } else if (char === '\u0003') {
        process.exit(1);
      } else if (char === '\b' || char === '\u007f') {
        if (password.length > 0) {
          password = password.slice(0, -1);
          process.stdout.write('\b \b');
        }
      } else {
        password += char;
        process.stdout.write('*');
      }
    };
    
    stdin.on('data', onData);
  });
}

async function createSuperAdmin() {
  console.log('🔧 Super Admin Creation Utility');
  console.log('================================\n');

  try {
    // Check if super admin already exists
    const existingSuperAdmin = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.role, 'super_admin'))
      .limit(1);

    if (existingSuperAdmin.length > 0) {
      console.log('⚠️  Super admin already exists:');
      console.log(`   Email: ${existingSuperAdmin[0].email}`);
      console.log(`   Name: ${existingSuperAdmin[0].fullName}`);
      console.log(`   Created: ${existingSuperAdmin[0].createdAt}`);
      
      const confirm = await askQuestion('\nDo you want to create another super admin? (y/N): ');
      if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
        console.log('Operation cancelled.');
        process.exit(0);
      }
    }

    // Collect super admin details
    const email = await askQuestion('Enter super admin email: ');
    if (!email || !email.includes('@')) {
      console.log('❌ Invalid email address');
      process.exit(1);
    }

    // Check if email already exists
    const existingUser = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.email, email))
      .limit(1);

    if (existingUser.length > 0) {
      console.log('❌ User with this email already exists');
      process.exit(1);
    }

    const fullName = await askQuestion('Enter super admin full name: ');
    if (!fullName || fullName.length < 2) {
      console.log('❌ Full name must be at least 2 characters');
      process.exit(1);
    }

    const password = await askPassword('Enter super admin password (min 8 chars): ');
    if (!password || password.length < 8) {
      console.log('❌ Password must be at least 8 characters');
      process.exit(1);
    }

    const confirmPassword = await askPassword('Confirm password: ');
    if (password !== confirmPassword) {
      console.log('❌ Passwords do not match');
      process.exit(1);
    }

    // Create super admin
    console.log('\n🚀 Creating super admin...');
    
    const hashedPassword = hashPassword(password);
    const superAdmin = await db
      .insert(authUsers)
      .values({
        email,
        fullName,
        hashedPassword,
        role: 'super_admin',
        organizationId: null, // Super admin is not tied to any organization
        isActive: true,
        isApproved: true, // Super admin is auto-approved
      })
      .returning();

    console.log('✅ Super admin created successfully!');
    console.log(`   ID: ${superAdmin[0].id}`);
    console.log(`   Email: ${superAdmin[0].email}`);
    console.log(`   Name: ${superAdmin[0].fullName}`);
    console.log(`   Role: ${superAdmin[0].role}`);
    console.log('\n🔑 Super admin can now log in and manage the system.');

  } catch (error) {
    console.error('❌ Error creating super admin:', error);
    process.exit(1);
  } finally {
    rl.close();
    process.exit(0);
  }
}

// Run the utility
createSuperAdmin();