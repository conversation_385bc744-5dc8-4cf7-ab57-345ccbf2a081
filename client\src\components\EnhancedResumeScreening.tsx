import React, { useState, useRef, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from '@/hooks/use-toast';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import CandidateDetailsModal from './CandidateDetailsModal';
import { authenticatedFetch } from '@/lib/api';
import { 
  Upload, 
  FileText, 
  Target, 
  Award, 
  Briefcase, 
  User,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Clock,
  User<PERSON><PERSON><PERSON>,
  <PERSON>r<PERSON>,
  CheckCircle
} from 'lucide-react';

interface ContactInfo {
  name?: string;
  email?: string;
  phone?: string;
  location?: string;
  linkedin?: string;
}

interface ResumeAnalysis {
  overall_score: number;
  match_score: number;
  experience_years: number;
  key_skills: string[];
  matched_skills: string[];
  missing_skills: string[];
  strengths: string[];
  concerns: string[];
  recommendation: 'HIRE' | 'INTERVIEW' | 'REJECT';
  detailed_feedback: string;
  interview_questions: string[];
}

interface JobPosting {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  department?: string;
  location?: string;
  skillsRequired?: string[];
  experienceLevel?: string;
  aiGeneratedSummary?: string;
}

interface AnalysisResult {
  contactInfo: ContactInfo;
  analysis?: ResumeAnalysis;
  extractedText: string;
  method: string;
  hasJobAnalysis: boolean;
  jobPosting?: JobPosting;
  autoMatchScore?: number;
  matchedJobs?: Array<{
    jobId: string;
    jobTitle: string;
    matchScore: number;
    matchedSkills: string[];
  }>;
}

export default function EnhancedResumeScreening() {
  const [file, setFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedJob, setSelectedJob] = useState<JobPosting | null>(null);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [showModal, setShowModal] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  // Fetch job postings
  const { data: jobPostings = [] } = useQuery({
    queryKey: ['/api/job-postings/all'],
    queryFn: async () => {
      const response = await fetch('/api/job-postings/all');
      if (!response.ok) throw new Error('Failed to fetch job postings');
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 🎯 Consider data fresh for 5 minutes to prevent unnecessary refetches
    cacheTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    const validFile = files.find(f => 
      f.type.includes('pdf') || 
      f.type.includes('doc') || 
      f.type.includes('docx') ||
      f.type === 'application/pdf' ||
      f.type === 'application/msword' ||
      f.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    );
    
    if (validFile) {
      setFile(validFile);
      toast({
        title: "File Uploaded",
        description: `Successfully uploaded ${validFile.name}`,
      });
    } else {
      toast({
        title: "Invalid File Type",
        description: "Please upload a PDF, DOC, or DOCX file.",
        variant: "destructive",
      });
    }
  }, [toast]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      const isValidType = selectedFile.type.includes('pdf') || 
                         selectedFile.type.includes('doc') || 
                         selectedFile.type.includes('docx') ||
                         selectedFile.type === 'application/pdf' ||
                         selectedFile.type === 'application/msword' ||
                         selectedFile.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      
      if (isValidType) {
        setFile(selectedFile);
        toast({
          title: "File Uploaded",
          description: `Successfully uploaded ${selectedFile.name}`,
        });
      } else {
        toast({
          title: "Invalid File Type",
          description: "Please upload a PDF, DOC, or DOCX file.",
          variant: "destructive",
        });
      }
    }
  };

  const processResume = async () => {
    if (!file) {
      toast({
        title: "No File Selected",
        description: "Please upload a resume first.",
        variant: "destructive",
      });
      return;
    }

    if (!selectedJob) {
      toast({
        title: "No Job Selected",
        description: "Please select a job posting for analysis.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    
    try {
      const formData = new FormData();
      formData.append('resume', file);
      formData.append('jobDescription', selectedJob.description || selectedJob.aiGeneratedSummary || 'General position analysis');
      formData.append('selectedJobId', selectedJob.id);

      const response = await fetch('/api/resume/analyze', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      if (!response.ok) {
        // Parse business validation errors from backend
        let errorData;
        try {
          errorData = await response.json();
        } catch (parseError) {
          // If response can't be parsed as JSON, use generic message
          throw new Error('Analysis failed - Unable to process response');
        }
        
        console.log('Error response data:', errorData);
        
        if (errorData.isBusinessRule && errorData.error) {
          // This is a business rule violation, show the detailed message
          const businessError = new Error(errorData.error);
          (businessError as any).isBusinessRule = true;
          (businessError as any).errorCode = errorData.errorCode;
          throw businessError;
        } else {
          // Generic API error
          throw new Error(errorData.error || 'Analysis failed');
        }
      }

      const result: AnalysisResult = await response.json();
      console.log('🔧 Analysis result received:', {
        hasResult: !!result,
        hasContactInfo: !!result.contactInfo,
        contactInfo: result.contactInfo,
        hasAnalysis: !!result.analysis
      });
      setAnalysisResult(result);
      setShowModal(true);
      
      // Auto-save candidate after analysis
      console.log('🔧 Calling autoSaveCandidate with result:', !!result, !!result.contactInfo);
      await autoSaveCandidate(result);
      
      // Trigger candidate added event for job posting page to refresh
      if (selectedJob?.id) {
        const candidateAddedEvent = new CustomEvent('candidateAdded', {
          detail: { jobId: selectedJob.id }
        });
        window.dispatchEvent(candidateAddedEvent);
      }
    } catch (error) {
      console.error('Resume analysis error:', error);
      const errorMessage = error instanceof Error ? error.message : "Failed to analyze resume. Please try again.";
      const isBusinessRule = (error as any)?.isBusinessRule;
      
      toast({
        title: isBusinessRule ? "Application Validation" : "Analysis Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const autoSaveCandidate = async (result: AnalysisResult) => {
    console.log('🔧 autoSaveCandidate called with:', { 
      result: !!result, 
      contactInfo: !!result?.contactInfo, 
      jobId: selectedJob?.id,
      resultSample: result ? { contactInfo: result.contactInfo, hasAnalysis: !!result.analysis } : null
    });
    
    try {
      // Create candidate with proper payload structure
      const candidateData = {
        email: result.contactInfo.email || '',
        fullName: result.contactInfo.name || 'Unknown',
        phone: result.contactInfo.phone || '',
        location: result.contactInfo.location || '',
        linkedinUrl: result.contactInfo.linkedin || '',
        resumeText: result.extractedText || '',
        skills: result.analysis?.key_skills || [],
        experienceYears: result.analysis?.experience_years || 0,
        status: 'pending_review',
        analysisResult: result.analysis,
        overallScore: result.analysis?.overall_score || 0,
        matchScore: result.analysis?.match_score || 0,
        recommendation: result.analysis?.recommendation || 'INTERVIEW',
        appliedJobId: selectedJob?.id || null,
        sourceChannel: 'resume_screening',
        aiSummary: result.analysis?.detailed_feedback || '',
        organizationId: null // Will be set by server from auth
      };
      
      console.log('🔧 Sending candidate data:', candidateData);
      
      const response = await authenticatedFetch('/api/candidates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(candidateData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔧 Auto-save failed:', response.status, errorText);
        throw new Error(`Auto-save failed: ${response.status}`);
      }

      const savedCandidate = await response.json();
      console.log('🔧 Candidate saved successfully:', savedCandidate.id);
      
      // Then upload the resume file if we have one
      if (file && savedCandidate.id) {
        await uploadResumeFile(savedCandidate.id);
      }
    } catch (error) {
      console.error('Auto-save error:', error);
    }
  };

  const uploadResumeFile = async (candidateId: string) => {
    try {
      const formData = new FormData();
      formData.append('resume', file);

      const response = await authenticatedFetch(`/api/files/upload/${candidateId}`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        console.error('Failed to upload resume file');
      }
    } catch (error) {
      console.error('Resume upload error:', error);
    }
  };

  const saveCandidate = async (decision: 'approve' | 'reject') => {
    if (!analysisResult) return;

    try {
      const response = await authenticatedFetch('/api/candidates/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          decision,
          analysisResult,
          jobId: selectedJob?.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save candidate');
      }

      const result = await response.json();
      
      toast({
        title: "Success",
        description: result.message || `Candidate ${decision}d successfully`,
        variant: "default",
      });
      
      // Close modal after successful save
      setShowModal(false);
      setAnalysisResult(null);
      
      // Reset form
      setFile(null);
      setSelectedJob(null);
      
      // Trigger candidate added event for job posting page to refresh
      if (selectedJob?.id) {
        const candidateAddedEvent = new CustomEvent('candidateAdded', {
          detail: { jobId: selectedJob.id }
        });
        window.dispatchEvent(candidateAddedEvent);
      }
    } catch (error) {
      console.error('Save candidate error:', error);
      toast({
        title: "Save Failed",
        description: error instanceof Error ? error.message : "Failed to save candidate decision",
        variant: "destructive",
      });
    }
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'HIRE': return 'bg-green-100 text-green-800 border-green-200';
      case 'INTERVIEW': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'REJECT': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <>
      <div className="h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 p-6 overflow-y-auto">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-blue-700 mb-2">
              AI-Powered Resume Screening
            </h1>
            <p className="text-gray-600 text-sm">
              Advanced ATS with intelligent job matching and candidate evaluation
            </p>
            
            {/* Authentication Status Alert */}
            {!user && (
              <Alert className="mt-4 mx-auto max-w-md border-orange-200 bg-orange-50">
                <AlertDescription className="text-center">
                  <strong>Please log in first!</strong> You need to be authenticated to save candidates.
                  <br />
                  <Button 
                    variant="link" 
                    className="text-blue-600 hover:text-blue-800 p-0 h-auto font-medium mt-1"
                    onClick={() => window.location.href = '/auth'}
                  >
                    Click here to log in →
                  </Button>
                </AlertDescription>
              </Alert>
            )}
            
            {user && (
              <Alert className="mt-4 mx-auto max-w-md border-green-200 bg-green-50">
                <AlertDescription className="text-center text-green-800">
                  ✓ Logged in as <strong>{user.email}</strong> - Ready to save candidates!
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Parallel Columns Layout */}
          <div className="grid grid-cols-2 gap-8">
            {/* Resume Upload Column */}
            <div className="bg-white/80 backdrop-blur-sm border-0 shadow-lg rounded-xl p-6" style={{
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.05)'
            }}>
              <div className="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                <Upload className="w-4 h-4" />
                Resume Upload
              </div>
              <div
                className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer ${
                  dragActive 
                    ? 'border-indigo-500 bg-indigo-50' 
                    : file
                      ? 'border-green-400 bg-green-50'
                      : 'border-gray-300 hover:border-indigo-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
                onClick={() => {
                  if (fileInputRef.current) {
                    fileInputRef.current.click();
                  }
                }}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                  onChange={handleFileChange}
                  className="hidden"
                />
                
                {file ? (
                  <div className="space-y-3">
                    <CheckCircle className="w-12 h-12 text-green-500 mx-auto" />
                    <div>
                      <p className="text-sm font-medium text-green-700">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {(file.size / 1024 / 1024).toFixed(2)} MB • {file.type.split('/')[1]?.toUpperCase()}
                      </p>
                    </div>
                    <div className="flex gap-2 justify-center">
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          if (fileInputRef.current) {
                            fileInputRef.current.click();
                          }
                        }}
                        variant="outline"
                        size="sm"
                      >
                        Replace
                      </Button>
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          setFile(null);
                          if (fileInputRef.current) {
                            fileInputRef.current.value = '';
                          }
                        }}
                        variant="outline"
                        size="sm"
                      >
                        Remove
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">
                        Drop resume here or click to browse
                      </p>
                      <p className="text-xs text-gray-500">
                        PDF, DOC, or DOCX files (max 10MB)
                      </p>
                    </div>
                    <Button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (fileInputRef.current) {
                          fileInputRef.current.click();
                        }
                      }}
                      variant="outline"
                      size="sm"
                    >
                      Choose File
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Job Matching Column */}
            <div className="bg-white/80 backdrop-blur-sm border-0 shadow-lg rounded-xl p-6" style={{
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.05)'
            }}>
              <div className="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                <Target className="w-4 h-4" />
                Job Matching
              </div>
              <div className="space-y-4">
                {/* Job Selection Dropdown */}
                <div className="space-y-2">
                  <Label htmlFor="job-select" className="text-sm font-medium text-gray-700">
                    Select Job Posting
                  </Label>
                  <Select onValueChange={(value) => {
                    const job = jobPostings.find((j: JobPosting) => j.id === value);
                    setSelectedJob(job || null);
                  }}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Choose a job posting..." />
                    </SelectTrigger>
                    <SelectContent>
                      {jobPostings.map((job: JobPosting) => (
                        <SelectItem key={job.id} value={job.id}>
                          {job.title} - {job.department}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Selected Job Display */}
                {selectedJob && (
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-blue-900">{selectedJob.title}</h3>
                      <Button
                        onClick={() => setSelectedJob(null)}
                        variant="ghost"
                        size="sm"
                      >
                        Clear
                      </Button>
                    </div>
                    <p className="text-sm text-blue-700 mb-3">
                      {selectedJob.description?.substring(0, 100)}...
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {selectedJob.skillsRequired?.slice(0, 3).map((skill, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                      {selectedJob.skillsRequired && selectedJob.skillsRequired.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{selectedJob.skillsRequired.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
                
                {/* Analyze Button */}
                <Button
                  onClick={processResume}
                  disabled={!file || !selectedJob || isProcessing}
                  className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                >
                  {isProcessing ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Analyzing...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Award className="w-4 h-4" />
                      Analyze Resume
                    </div>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {/* Analysis Results - Below Tiles */}
          {analysisResult && (
            <div className="bg-white/80 backdrop-blur-sm border-0 shadow-lg rounded-xl p-6 mt-6" style={{
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.05)'
            }}>
              <div className="space-y-6">
                {/* Candidate Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                      <User className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {analysisResult.contactInfo.name || 'Candidate'}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {analysisResult.contactInfo.email || 'No email provided'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Target className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-gray-700">
                        {analysisResult.analysis?.match_score || 0}% Match
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-600">
                        {analysisResult.analysis?.experience_years || 0} years exp
                      </span>
                    </div>
                  </div>
                </div>

                {/* Contact Info Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-700">
                      {analysisResult.contactInfo.email || 'Not provided'}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-700">
                      {analysisResult.contactInfo.phone || 'Not provided'}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-700">
                      {analysisResult.contactInfo.location || 'Not provided'}
                    </span>
                  </div>
                </div>

                {/* Analysis Summary */}
                {analysisResult.analysis && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">About Candidate</h4>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {analysisResult.analysis.detailed_feedback}
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">Top Skills</h4>
                      <div className="flex flex-wrap gap-2">
                        {analysisResult.analysis.key_skills.slice(0, 6).map((skill, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Decision Buttons */}
                <div className="flex gap-4 justify-center">
                  <Button
                    onClick={() => saveCandidate('approve')}
                    className="bg-green-600 hover:bg-green-700 text-white px-8 py-3"
                  >
                    <UserCheck className="w-5 h-5 mr-2" />
                    Approve for Interview
                  </Button>
                  <Button
                    onClick={() => saveCandidate('reject')}
                    variant="destructive"
                    className="px-8 py-3"
                  >
                    <UserX className="w-5 h-5 mr-2" />
                    Reject Candidate
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Ready for AI-Powered Analysis - Bottom */}
          {!analysisResult && (
            <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
              <CardContent className="p-12 text-center">
                <Briefcase className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold text-gray-700 mb-2">
                  Ready for AI-Powered Analysis
                </h3>
                <p className="text-gray-600 mb-6">
                  Upload a resume and select a job posting to get started with intelligent candidate screening
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-500">
                  <div className="flex items-center justify-center gap-2">
                    <FileText className="w-4 h-4" />
                    PDF & Word Support
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Target className="w-4 h-4" />
                    Smart Job Matching
                  </div>
                  <div className="flex items-center justify-center gap-2">
                    <Award className="w-4 h-4" />
                    AI-Powered Insights
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
      
      {/* Candidate Details Modal */}
      {analysisResult && (
        <CandidateDetailsModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          analysisResult={analysisResult}
          fileName={file?.name}
        />
      )}
    </>
  );
}