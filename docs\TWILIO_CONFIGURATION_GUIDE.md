# Twilio Configuration Guide for Voice Calls with ElevenLabs

## Overview
This guide explains the exact Twilio configuration needed for our voice calling system with ElevenLabs conversational AI integration.

## 1. Twilio Phone Number Configuration

**You DO NOT need to configure a webhook URL in your Twilio phone number settings.**

Our system works differently - we programmatically specify the webhook URL when initiating each call using the Twilio API.

### Current Configuration:
- **Phone Number**: +****************
- **Voice Configuration**: Can be left empty or set to "Webhooks"
- **Webhook URL**: Leave blank (we set this programmatically per call)

## 2. How Our System Works

When we initiate a call, our code does this:

```javascript
twilioClient.calls.create({
  to: candidatePhoneNumber,
  from: '+***********',
  url: 'https://[your-replit-domain]/api/voice-providers/twiml/[callId]'
})
```

This `url` parameter tells <PERSON><PERSON><PERSON> where to fetch TwiML instructions for THIS specific call.

## 3. The Complete Flow

```
1. User clicks "Call" in our app
   ↓
2. Our backend calls Twilio API with webhook URL
   ↓
3. <PERSON><PERSON><PERSON> dials the candidate
   ↓
4. When candidate answers, <PERSON><PERSON><PERSON> requests TwiML from our webhook:
   https://[replit-domain]/api/voice-providers/twiml/[callId]
   ↓
5. Our webhook returns TwiML with WebSocket instructions:
   <Connect>
     <Stream url="wss://[replit-domain]/elevenlabs-stream">
       ...parameters...
     </Stream>
   </Connect>
   ↓
6. Twilio attempts WebSocket connection to:
   wss://[replit-domain]/elevenlabs-stream
```

## 4. Required Twilio Account Features

Ensure your Twilio account has these features enabled:

### ✅ Required:
- **Programmable Voice**: Enabled (standard with all accounts)
- **Media Streams**: Enabled (for WebSocket streaming)
- **Outbound Calling**: Enabled

### 📍 Check These Settings:
1. Log into Twilio Console
2. Go to **Phone Numbers** → **Manage** → **Active Numbers**
3. Click on your phone number (****** 900 6967)
4. Under **Voice & Fax** section:
   - **Configure With**: Webhooks, TwiML Bins, Functions, Studio, or Proxy
   - **A Call Comes In**: Can be empty or "Webhook"
   - **Primary Handler Fails**: Can be empty

## 5. Twilio Console Settings to Verify

### Geographic Permissions:
- Go to **Voice** → **Settings** → **Geographic Permissions**
- Ensure United States is enabled for outbound calls

### Voice Settings:
- Go to **Voice** → **Settings** → **General**
- **Enable Media Streams**: Must be ON
- **Status Callback URL**: Not required (we handle per-call)

## 6. Common Issues and Solutions

### Issue: Error 31920 (WebSocket Handshake Error)
**This is a server-side issue, NOT a Twilio configuration issue.**

Twilio is correctly trying to connect but our server isn't completing the handshake.

### Issue: Calls not initiating
**Check:**
- Account SID and Auth Token are correct in environment variables
- Phone number is verified and active
- Geographic permissions allow calling to target country

### Issue: TwiML not being fetched
**Check:**
- The Replit app is publicly accessible
- The URL in our code is using HTTPS (not HTTP)
- The domain doesn't have `:5000` port in production

## 7. Testing Your Configuration

### Test 1: Verify Phone Number
```bash
curl -X GET "https://api.twilio.com/2010-04-01/Accounts/[AccountSID]/IncomingPhoneNumbers.json" \
  -u [AccountSID]:[AuthToken]
```

### Test 2: Check Media Streams Capability
Look for "STREAM" in capabilities when you query your phone number.

## 8. Environment Variables Required

```env
TWILIO_ACCOUNT_SID=AC...  # Your Account SID
TWILIO_AUTH_TOKEN=...      # Your Auth Token  
TWILIO_PHONE_NUMBER=+***********  # Your Twilio phone number
```

## 9. NO Additional Configuration Needed

You do NOT need to:
- ❌ Set up TwiML Apps
- ❌ Configure SIP Domains
- ❌ Set up Twilio Functions
- ❌ Configure Studio Flows
- ❌ Set webhook URLs in phone number settings

Our application handles everything programmatically!

## 10. Debugging the Current Issue

The Error 31920 indicates Twilio IS correctly configured and trying to connect. The issue is on our server side where the WebSocket handshake isn't completing.

**Current Status:**
- ✅ Twilio is receiving our TwiML correctly
- ✅ Twilio is attempting WebSocket connection
- ❌ Our server isn't completing the WebSocket handshake (HTTP 101 response)

This is what we're debugging - it's not a Twilio configuration issue.