import request from 'supertest';
import { app } from '../../server/app';
import jwt from 'jsonwebtoken';

describe('Complete Interview Flow E2E', () => {
  let authToken: string;
  let mockUser: any;
  let interviewId: string;
  let candidateId: string;
  let agentProfileId: string;

  beforeAll(async () => {
    mockUser = global.testUtils.createMockUser();
    authToken = jwt.sign(
      { id: mockUser.id, email: mockUser.email },
      process.env.JWT_SECRET || 'test-secret'
    );
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Full Interview Automation Flow', () => {
    it('should complete the entire interview process', async () => {
      // Step 1: Create Agent Profile
      console.log('🤖 Step 1: Creating agent profile...');
      const agentProfileResponse = await request(app)
        .post('/api/agent-profiles')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          name: 'E2E Test Agent',
          promptTemplate: 'You are conducting a technical interview for a software engineer position.',
          voiceSettings: {
            voiceId: 'sarah',
            stability: 0.6,
            similarityBoost: 0.8
          },
          rubric: {
            competencies: ['Technical Skills', 'Communication', 'Problem Solving']
          }
        })
        .expect(201);

      expect(agentProfileResponse.body.success).toBe(true);
      agentProfileId = agentProfileResponse.body.agentProfile.id;

      // Step 2: Create Candidate
      console.log('👤 Step 2: Creating candidate...');
      const candidateResponse = await request(app)
        .post('/api/candidates')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          email: '<EMAIL>',
          fullName: 'E2E Test Candidate',
          phone: '+1234567890'
        })
        .expect(201);

      expect(candidateResponse.body.success).toBe(true);
      candidateId = candidateResponse.body.candidate.id;

      // Step 3: Schedule Interview
      console.log('📅 Step 3: Scheduling interview...');
      const interviewResponse = await request(app)
        .post('/api/interviews-v2')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          candidateId,
          role: 'Software Engineer',
          scheduledAt: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
          duration: 60,
          agentProfileId,
          sdkType: 'video_sdk'
        })
        .expect(201);

      expect(interviewResponse.body.success).toBe(true);
      interviewId = interviewResponse.body.interview.id;

      // Step 4: Generate Zoom Tokens
      console.log('🎥 Step 4: Generating Zoom tokens...');
      const zoomTokenResponse = await request(app)
        .get('/api/zoom/token')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          sessionName: `interview-${interviewId}`,
          candidateId,
          role: 'host'
        })
        .expect(200);

      expect(zoomTokenResponse.body.success).toBe(true);
      expect(zoomTokenResponse.body.hostToken).toBeDefined();
      expect(zoomTokenResponse.body.participantToken).toBeDefined();

      // Step 5: Send Interview Invitation
      console.log('📧 Step 5: Sending interview invitation...');
      const invitationResponse = await request(app)
        .post(`/api/interviews-v2/${interviewId}/send-invitation`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(invitationResponse.body.success).toBe(true);
      expect(invitationResponse.body.message).toContain('invitation sent');

      // Step 6: Start Bot Session
      console.log('🤖 Step 6: Starting bot session...');
      const botStartResponse = await request(app)
        .post('/api/bot-runner/start')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ interviewId })
        .expect(200);

      expect(botStartResponse.body.success).toBe(true);
      expect(botStartResponse.body.sessionId).toBeDefined();

      const botSessionId = botStartResponse.body.sessionId;

      // Step 7: Check Bot Status
      console.log('📊 Step 7: Checking bot status...');
      const botStatusResponse = await request(app)
        .get(`/api/bot-runner/status/${botSessionId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(botStatusResponse.body.success).toBe(true);
      expect(botStatusResponse.body.status).toBeDefined();

      // Step 8: Start ElevenLabs Conversation
      console.log('🗣️ Step 8: Starting ElevenLabs conversation...');
      const conversationResponse = await request(app)
        .post('/api/elevenlabs/conversation/start')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          sessionId: botSessionId,
          interviewId
        })
        .expect(200);

      expect(conversationResponse.body.success).toBe(true);
      expect(conversationResponse.body.conversationSessionId).toBeDefined();

      // Step 9: Simulate Interview Progress
      console.log('⏱️ Step 9: Simulating interview progress...');
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second

      // Check conversation status
      const conversationStatusResponse = await request(app)
        .get('/api/elevenlabs/conversation/status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(conversationStatusResponse.body.success).toBe(true);

      // Step 10: End Conversation
      console.log('🏁 Step 10: Ending conversation...');
      const endConversationResponse = await request(app)
        .post('/api/elevenlabs/conversation/end')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ sessionId: botSessionId })
        .expect(200);

      expect(endConversationResponse.body.success).toBe(true);

      // Step 11: Stop Bot Session
      console.log('🛑 Step 11: Stopping bot session...');
      const stopBotResponse = await request(app)
        .post('/api/bot-runner/stop')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ sessionId: botSessionId })
        .expect(200);

      expect(stopBotResponse.body.success).toBe(true);

      // Step 12: Upload Mock Recording for Transcription
      console.log('🎙️ Step 12: Processing interview recording...');
      const mockAudioBuffer = Buffer.from('mock-audio-data');
      
      const transcriptionResponse = await request(app)
        .post('/api/transcription/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('interviewId', interviewId)
        .attach('audio', mockAudioBuffer, 'interview-recording.wav')
        .expect(200);

      expect(transcriptionResponse.body.success).toBe(true);
      expect(transcriptionResponse.body.transcription).toBeDefined();
      expect(transcriptionResponse.body.summary).toBeDefined();

      // Step 13: Retrieve Interview Artifacts
      console.log('📄 Step 13: Retrieving interview artifacts...');
      const artifactsResponse = await request(app)
        .get(`/api/transcription/interview/${interviewId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(artifactsResponse.body.success).toBe(true);
      expect(artifactsResponse.body.artifacts).toBeDefined();
      expect(artifactsResponse.body.artifacts.transcript).toBeDefined();
      expect(artifactsResponse.body.artifacts.summary).toBeDefined();

      // Step 14: Update Interview Status to Completed
      console.log('✅ Step 14: Marking interview as completed...');
      const updateResponse = await request(app)
        .put(`/api/interviews-v2/${interviewId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          status: 'completed',
          completedAt: new Date().toISOString()
        })
        .expect(200);

      expect(updateResponse.body.success).toBe(true);

      // Step 15: Verify Final Interview State
      console.log('🔍 Step 15: Verifying final interview state...');
      const finalInterviewResponse = await request(app)
        .get(`/api/interviews-v2/${interviewId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(finalInterviewResponse.body.success).toBe(true);
      expect(finalInterviewResponse.body.interview.status).toBe('completed');

      console.log('🎉 E2E Test completed successfully!');
    }, 60000); // 60 second timeout for the full flow

    it('should handle interview cancellation flow', async () => {
      // Create a test interview
      const interviewResponse = await request(app)
        .post('/api/interviews-v2')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          candidateId: 'test-candidate-id',
          role: 'Software Engineer',
          scheduledAt: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
          duration: 60,
          agentProfileId: 'test-agent-profile-id'
        })
        .expect(201);

      const testInterviewId = interviewResponse.body.interview.id;

      // Send invitation first
      await request(app)
        .post(`/api/interviews-v2/${testInterviewId}/send-invitation`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Cancel the interview
      const cancelResponse = await request(app)
        .post(`/api/interviews-v2/${testInterviewId}/cancel`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          reason: 'Candidate requested reschedule'
        })
        .expect(200);

      expect(cancelResponse.body.success).toBe(true);

      // Verify interview is cancelled
      const verifyResponse = await request(app)
        .get(`/api/interviews-v2/${testInterviewId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(verifyResponse.body.interview.status).toBe('cancelled');
    });

    it('should handle error scenarios gracefully', async () => {
      // Test with invalid interview ID
      await request(app)
        .post('/api/interviews-v2/invalid-id/send-invitation')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      // Test bot start with non-existent interview
      await request(app)
        .post('/api/bot-runner/start')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ interviewId: 'non-existent-id' })
        .expect(404);

      // Test transcription without audio file
      await request(app)
        .post('/api/transcription/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('interviewId', 'test-id')
        .expect(400);
    });
  });

  afterAll(async () => {
    // Cleanup any resources if needed
    console.log('🧹 E2E test cleanup completed');
  });
});
