
import React, { createContext, useContext, useState, ReactNode } from 'react';

interface ApprovedCandidate {
  id: string;
  name: string;
  email: string;
  position?: string;
  match_score?: number;
  status: string;
}

interface CandidateContextType {
  approvedCandidates: ApprovedCandidate[];
  addApprovedCandidate: (candidate: ApprovedCandidate) => void;
  removeApprovedCandidate: (candidateId: string) => void;
}

const CandidateContext = createContext<CandidateContextType | undefined>(undefined);

export const useCandidateContext = () => {
  const context = useContext(CandidateContext);
  if (context === undefined) {
    throw new Error('useCandidateContext must be used within a CandidateProvider');
  }
  return context;
};

interface CandidateProviderProps {
  children: ReactNode;
}

export const CandidateProvider: React.FC<CandidateProviderProps> = ({ children }) => {
  const [approvedCandidates, setApprovedCandidates] = useState<ApprovedCandidate[]>([]);

  const addApprovedCandidate = (candidate: ApprovedCandidate) => {
    setApprovedCandidates(prev => {
      // Check if candidate already exists to avoid duplicates
      const exists = prev.some(c => c.id === candidate.id);
      if (exists) {
        return prev;
      }
      return [...prev, candidate];
    });
  };

  const removeApprovedCandidate = (candidateId: string) => {
    setApprovedCandidates(prev => prev.filter(c => c.id !== candidateId));
  };

  return (
    <CandidateContext.Provider value={{
      approvedCandidates,
      addApprovedCandidate,
      removeApprovedCandidate
    }}>
      {children}
    </CandidateContext.Provider>
  );
};
