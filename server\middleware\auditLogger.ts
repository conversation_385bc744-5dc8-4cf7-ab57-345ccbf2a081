import { Request, Response, NextFunction } from 'express';
import { db } from '../db';
import { auditLogs } from '@shared/schema';
import { AuthenticatedRequest } from '../auth';

export interface AuditLogData {
  action: string;
  resourceType: string;
  resourceId?: string;
  organizationId?: string;
  userId: string;
  userEmail: string;
  userRole: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
}

/**
 * Log audit events to database
 */
export async function logAuditEvent(data: AuditLogData): Promise<void> {
  try {
    await db.insert(auditLogs).values({
      action: data.action,
      resourceType: data.resourceType,
      resourceId: data.resourceId,
      organizationId: data.organizationId,
      userId: data.userId,
      userEmail: data.userEmail,
      userRole: data.userRole,
      details: data.details,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      success: data.success,
      errorMessage: data.errorMessage,
    });
  } catch (error) {
    console.error('Failed to log audit event:', error);
    // Don't throw error to avoid disrupting main request flow
  }
}

/**
 * Middleware to automatically log API access
 */
export function auditLoggerMiddleware(resourceType: string, actionOverride?: string) {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const startTime = Date.now();
    
    // Extract client info
    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    
    // Determine action based on HTTP method if not overridden
    const action = actionOverride || `${req.method.toLowerCase()}_${resourceType}`;
    
    // Override res.json to capture response
    const originalJson = res.json.bind(res);
    let responseData: any;
    
    res.json = function(body: any) {
      responseData = body;
      return originalJson(body);
    };

    // Continue to next middleware
    next();

    // Log after response is sent
    res.on('finish', async () => {
      if (!req.user) return; // Skip if no authenticated user
      
      const success = res.statusCode < 400;
      const duration = Date.now() - startTime;
      
      const auditData: AuditLogData = {
        action,
        resourceType,
        resourceId: req.params.id,
        organizationId: req.user.organizationId,
        userId: req.user.id,
        userEmail: req.user.email,
        userRole: req.user.role,
        details: {
          method: req.method,
          url: req.originalUrl,
          query: req.query,
          statusCode: res.statusCode,
          duration,
          ...(req.method !== 'GET' && { requestBody: req.body }),
          ...(responseData?.error && { responseError: responseData.error })
        },
        ipAddress,
        userAgent,
        success,
        errorMessage: !success ? responseData?.error || 'Unknown error' : undefined,
      };
      
      await logAuditEvent(auditData);
    });
  };
}

/**
 * Middleware for critical actions that require immediate audit logging
 */
export function criticalActionLogger(action: string, resourceType: string) {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';

    // Log the attempt immediately
    await logAuditEvent({
      action: `${action}_attempt`,
      resourceType,
      resourceId: req.params.id,
      organizationId: req.user.organizationId,
      userId: req.user.id,
      userEmail: req.user.email,
      userRole: req.user.role,
      details: {
        method: req.method,
        url: req.originalUrl,
        requestBody: req.body,
        query: req.query,
      },
      ipAddress,
      userAgent,
      success: true, // Attempt is successful regardless of final outcome
    });

    next();
  };
}

/**
 * Log security violations
 */
export async function logSecurityViolation(
  req: AuthenticatedRequest,
  violationType: string,
  details: Record<string, any>
): Promise<void> {
  const ipAddress = req.ip || req.connection.remoteAddress || 'unknown';
  const userAgent = req.get('User-Agent') || 'unknown';

  await logAuditEvent({
    action: 'security_violation',
    resourceType: 'security',
    organizationId: req.user?.organizationId,
    userId: req.user?.id || 'unknown',
    userEmail: req.user?.email || 'unknown',
    userRole: req.user?.role || 'unknown',
    details: {
      violationType,
      url: req.originalUrl,
      method: req.method,
      ...details
    },
    ipAddress,
    userAgent,
    success: false,
    errorMessage: `Security violation: ${violationType}`,
  });
}