import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { MapPin, Building2, Clock, DollarSign, Users, FileText, Upload, CheckCircle } from 'lucide-react';
// We'll use fetch directly instead of apiRequest for public routes

interface Organization {
  id: string;
  name: string;
  domain: string;
}

interface JobPosting {
  id: string;
  title: string;
  department: string;
  location: string;
  employment_type: string;
  experience_level: string;
  salary_range: string;
  description: string;
  responsibilities: string[];
  preferred_qualifications: string[];
  benefits: string[];
  company_overview: string;
  work_environment: string;
  growth_opportunities: string;
  status: string;
  organization_id: string;
  organization_name: string;
  posted_date: string;
}

interface ApplicationForm {
  candidateName: string;
  email: string;
  phone: string;
  coverLetter: string;
  resume: File | null;
}

export default function PublicJobPortal() {
  const [selectedOrgId, setSelectedOrgId] = useState<string>('');
  const [selectedJob, setSelectedJob] = useState<JobPosting | null>(null);
  const [showApplicationDialog, setShowApplicationDialog] = useState(false);
  const [applicationForm, setApplicationForm] = useState<ApplicationForm>({
    candidateName: '',
    email: '',
    phone: '',
    coverLetter: '',
    resume: null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch all organizations
  const { data: organizations = [], isLoading: orgsLoading } = useQuery({
    queryKey: ['/api/organizations/all'],
    queryFn: () => fetch('/api/organizations/all').then(res => res.json())
  });

  // Fetch job postings for selected organization
  const { data: jobPostings = [], isLoading: jobsLoading, refetch: refetchJobs } = useQuery({
    queryKey: ['/api/job-postings/by-organization', selectedOrgId],
    queryFn: () => 
      selectedOrgId 
        ? fetch(`/api/job-postings/by-organization/${selectedOrgId}`).then(res => res.json())
        : Promise.resolve([]),
    enabled: !!selectedOrgId
  });

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast({
          title: "File too large",
          description: "Please select a file smaller than 10MB",
          variant: "destructive"
        });
        return;
      }
      setApplicationForm(prev => ({ ...prev, resume: file }));
    }
  };

  const submitApplication = async () => {
    if (!selectedJob || !applicationForm.resume || !applicationForm.candidateName || !applicationForm.email) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields and upload your resume",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Submit complete application - all processing happens internally
      const formData = new FormData();
      formData.append('resume', applicationForm.resume);
      formData.append('organizationId', selectedJob.organization_id);
      formData.append('jobId', selectedJob.id);
      formData.append('candidateName', applicationForm.candidateName);
      formData.append('email', applicationForm.email);
      formData.append('phone', applicationForm.phone || '');
      formData.append('coverLetter', applicationForm.coverLetter || '');
      
      const response = await fetch('/api/files/public-submit-application', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit application');
      }
      
      const result = await response.json();

      toast({
        title: "Application submitted successfully!",
        description: `${result.message} Reference ID: ${result.applicationId}`
      });

      // Reset form and close dialog
      setApplicationForm({
        candidateName: '',
        email: '',
        phone: '',
        coverLetter: '',
        resume: null
      });
      setSelectedJob(null);

    } catch (error) {
      console.error('Application submission error:', error);
      toast({
        title: "Submission failed",
        description: "There was an error submitting your application. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Job Portal</h1>
              <p className="text-gray-600 mt-1">Find your next opportunity</p>
            </div>
            <div className="flex items-center space-x-2">
              <Building2 className="h-5 w-5 text-blue-600" />
              <span className="text-sm text-gray-500">Powered by AI-driven matching</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Organization Selection */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Select Organization
            </CardTitle>
            <CardDescription>
              Choose an organization to view their available job opportunities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select value={selectedOrgId} onValueChange={setSelectedOrgId}>
              <SelectTrigger className="w-full max-w-md">
                <SelectValue placeholder="Select an organization..." />
              </SelectTrigger>
              <SelectContent>
                {organizations.map((org: Organization) => (
                  <SelectItem key={org.id} value={org.id}>
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4" />
                      {org.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* Job Listings */}
        {selectedOrgId && (
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Available Positions</h2>
              <Badge variant="secondary" className="text-sm">
                {jobPostings.length} {jobPostings.length === 1 ? 'job' : 'jobs'} available
              </Badge>
            </div>

            {jobsLoading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {[1, 2, 3].map((i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="h-3 bg-gray-200 rounded"></div>
                        <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : jobPostings.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No job openings</h3>
                  <p className="text-gray-500">This organization currently has no active job postings.</p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {jobPostings.map((job: JobPosting) => (
                  <Card key={job.id} className="hover:shadow-lg transition-shadow duration-200 cursor-pointer group">
                    <CardHeader>
                      <CardTitle className="group-hover:text-blue-600 transition-colors">
                        {job.title}
                      </CardTitle>
                      <CardDescription className="flex items-center gap-1">
                        <Building2 className="h-4 w-4" />
                        {job.department}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <MapPin className="h-4 w-4" />
                          {job.location}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Users className="h-4 w-4" />
                          {job.employment_type} • {job.experience_level}
                        </div>
                        {job.salary_range && (
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <DollarSign className="h-4 w-4" />
                            {job.salary_range}
                          </div>
                        )}
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Clock className="h-4 w-4" />
                          Posted {formatDate(job.posted_date)}
                        </div>
                        <p className="text-sm text-gray-600 line-clamp-3">
                          {job.description}
                        </p>
                      </div>
                      <div className="flex gap-2 mt-4">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" className="flex-1">
                              View Details
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle className="text-xl">{job.title}</DialogTitle>
                              <DialogDescription>
                                {job.department} • {job.location}
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-6">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <Label className="font-medium">Employment Type</Label>
                                  <p className="text-sm text-gray-600">{job.employment_type}</p>
                                </div>
                                <div>
                                  <Label className="font-medium">Experience Level</Label>
                                  <p className="text-sm text-gray-600">{job.experience_level}</p>
                                </div>
                                {job.salary_range && (
                                  <div>
                                    <Label className="font-medium">Salary Range</Label>
                                    <p className="text-sm text-gray-600">{job.salary_range}</p>
                                  </div>
                                )}
                              </div>
                              
                              <div>
                                <Label className="font-medium">Job Description</Label>
                                <p className="text-sm text-gray-600 mt-1 whitespace-pre-wrap">{job.description}</p>
                              </div>

                              {job.responsibilities && job.responsibilities.length > 0 && (
                                <div>
                                  <Label className="font-medium">Key Responsibilities</Label>
                                  <ul className="text-sm text-gray-600 mt-1 space-y-1">
                                    {job.responsibilities.map((resp, index) => (
                                      <li key={index} className="flex items-start gap-2">
                                        <span className="text-blue-500 mt-1.5">•</span>
                                        {resp}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}

                              {job.preferred_qualifications && job.preferred_qualifications.length > 0 && (
                                <div>
                                  <Label className="font-medium">Preferred Qualifications</Label>
                                  <ul className="text-sm text-gray-600 mt-1 space-y-1">
                                    {job.preferred_qualifications.map((qual, index) => (
                                      <li key={index} className="flex items-start gap-2">
                                        <span className="text-green-500 mt-1.5">✓</span>
                                        {qual}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}

                              {job.benefits && job.benefits.length > 0 && (
                                <div>
                                  <Label className="font-medium">Benefits</Label>
                                  <ul className="text-sm text-gray-600 mt-1 space-y-1">
                                    {job.benefits.map((benefit, index) => (
                                      <li key={index} className="flex items-start gap-2">
                                        <span className="text-purple-500 mt-1.5">★</span>
                                        {benefit}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}

                              {job.company_overview && (
                                <div>
                                  <Label className="font-medium">About the Company</Label>
                                  <p className="text-sm text-gray-600 mt-1 whitespace-pre-wrap">{job.company_overview}</p>
                                </div>
                              )}
                            </div>
                            <div className="flex gap-3 pt-4 border-t">
                              <Button 
                                onClick={() => {
                                  setSelectedJob(job);
                                  setShowApplicationDialog(true);
                                }}
                                className="flex-1"
                              >
                                Apply Now
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Button 
                          onClick={() => {
                            setSelectedJob(job);
                            setShowApplicationDialog(true);
                          }}
                          className="flex-1"
                        >
                          Apply Now
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Application Dialog */}
        <Dialog open={showApplicationDialog} onOpenChange={setShowApplicationDialog}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Apply for {selectedJob?.title}</DialogTitle>
              <DialogDescription>
                Please fill out the application form below
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="candidateName">Full Name *</Label>
                  <Input
                    id="candidateName"
                    value={applicationForm.candidateName}
                    onChange={(e) => setApplicationForm(prev => ({ ...prev, candidateName: e.target.value }))}
                    placeholder="Your full name"
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={applicationForm.email}
                    onChange={(e) => setApplicationForm(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  value={applicationForm.phone}
                  onChange={(e) => setApplicationForm(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="Your phone number"
                />
              </div>

              <div>
                <Label htmlFor="resume">Resume *</Label>
                <div className="mt-1">
                  <Input
                    id="resume"
                    type="file"
                    accept=".pdf,.doc,.docx"
                    onChange={handleFileUpload}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Accepted formats: PDF, DOC, DOCX (max 10MB)
                  </p>
                  {applicationForm.resume && (
                    <p className="text-sm text-green-600 mt-1 flex items-center gap-1">
                      <CheckCircle className="h-4 w-4" />
                      {applicationForm.resume.name}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="coverLetter">Cover Letter (Optional)</Label>
                <Textarea
                  id="coverLetter"
                  value={applicationForm.coverLetter}
                  onChange={(e) => setApplicationForm(prev => ({ ...prev, coverLetter: e.target.value }))}
                  placeholder="Tell us why you're interested in this position..."
                  rows={4}
                />
              </div>
            </div>
            <div className="flex gap-3 pt-4 border-t">
              <Button 
                variant="outline" 
                onClick={() => setShowApplicationDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button 
                onClick={submitApplication}
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <Upload className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Submit Application
                  </>
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}