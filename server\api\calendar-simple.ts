import { Router } from 'express';
import { calendarService } from '../services/calendarService';
import { db } from '../db';
import { candidates } from '@shared/schema';
import { eq } from 'drizzle-orm';

const router = Router();

// Schedule interview endpoint - simplified to work without database changes
router.post('/schedule-interview', async (req, res) => {
  try {
    const { candidateId, interviewDateTime, slotNotes } = req.body;
    
    if (!candidateId || !interviewDateTime) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Get candidate details
    const [candidate] = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId));

    if (!candidate) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    // Try to create calendar event, but continue even if it fails
    let eventId = null;
    let calendarEventCreated = false;
    
    try {
      if (calendarService.isAuthenticated()) {
        eventId = await calendarService.createInterviewEvent({
          candidateName: candidate.fullName,
          candidateEmail: candidate.email,
          interviewDateTime: interviewDateTime,
          duration: 60,
          interviewerEmail: '<EMAIL>',
          notes: slotNotes || 'Interview scheduled via HR system'
        });
        calendarEventCreated = !!eventId;
      }
    } catch (error) {
      console.log('Calendar event creation failed, continuing without calendar integration:', error);
    }

    // Update candidate status to interview_scheduled
    await db
      .update(candidates)
      .set({ 
        status: 'interview_scheduled',
      })
      .where(eq(candidates.id, candidateId));

    res.json({ 
      success: true, 
      eventId,
      calendarEventCreated,
      message: calendarEventCreated 
        ? 'Interview scheduled successfully with calendar event' 
        : 'Interview scheduled successfully (calendar event creation skipped)',
      candidateName: candidate.fullName,
      scheduledAt: interviewDateTime
    });

  } catch (error) {
    console.error('Error scheduling interview:', error);
    res.status(500).json({ error: 'Failed to schedule interview' });
  }
});

// Get upcoming interviews - simplified version
router.get('/upcoming', async (req, res) => {
  try {
    const upcomingEvents = await calendarService.listUpcomingInterviews(20);
    
    // Get all candidates with interview_scheduled status
    const scheduledCandidates = await db
      .select()
      .from(candidates)
      .where(eq(candidates.status, 'interview_scheduled'));

    res.json({
      calendarEvents: upcomingEvents,
      scheduledCandidates: scheduledCandidates
    });

  } catch (error) {
    console.error('Error fetching upcoming interviews:', error);
    res.status(500).json({ error: 'Failed to fetch upcoming interviews' });
  }
});

export default router;