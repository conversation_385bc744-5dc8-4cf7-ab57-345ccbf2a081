import { Router } from 'express';
import { db } from '../db';
import { voiceCalls, voiceCallNotes, candidates, jobPostings, organizations } from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import { authenticateToken } from '../auth';
import OpenAI from 'openai';
import { elevenLabsService } from '../services/elevenlabsService';
import { googleSpeechService } from '../services/googleSpeechService';
import { TwilioMediaStreamHandler } from '../services/twilioMediaStreamHandler';
import fs from 'fs';
import path from 'path';
import { Readable } from 'stream';
import { WebSocketServer } from 'ws';
import { VoiceCallManager } from '../services/voiceCallManager';

const router = Router();

// Initialize voice call manager
const voiceCallManager = new VoiceCallManager();

// Initialize OpenAI client for Whisper integration
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Create uploads directory for temporary audio files
const uploadsDir = path.join(process.cwd(), 'uploads', 'audio');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Enhanced transcription function with Whisper fallback
async function transcribeWithWhisper(audioUrl: string, callId: string): Promise<string | null> {
  try {
    console.log(`🎤 Attempting Whisper transcription for call ${callId}: ${audioUrl}`);
    
    // Download audio file from Twilio with authentication
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const credentials = Buffer.from(`${accountSid}:${authToken}`).toString('base64');
    
    const response = await fetch(audioUrl, {
      headers: {
        'Authorization': `Basic ${credentials}`
      }
    });
    if (!response.ok) {
      throw new Error(`Failed to download audio: ${response.statusText}`);
    }
    
    const audioBuffer = await response.arrayBuffer();
    const audioPath = path.join(uploadsDir, `${callId}_${Date.now()}.wav`);
    
    // Save audio file temporarily
    fs.writeFileSync(audioPath, Buffer.from(audioBuffer));
    
    // Transcribe with Whisper
    const transcription = await openai.audio.transcriptions.create({
      file: fs.createReadStream(audioPath),
      model: "whisper-1",
      language: "en", // Optimize for English conversations
      prompt: "This is a conversation between a recruiter named Sarah and a job candidate about interview scheduling.", // Context helps accuracy
    });
    
    // Clean up temporary file
    fs.unlinkSync(audioPath);
    
    console.log(`✅ Whisper transcription: "${transcription.text}"`);
    return transcription.text.trim();
    
  } catch (error) {
    console.error('❌ Whisper transcription failed:', error);
    return null; // Return null to indicate Whisper failure
  }
}

// Voice provider interfaces
interface VoiceProvider {
  name: string;
  initiate(params: CallParams): Promise<CallResult>;
  generateTwiML?(callId: string): Promise<string>;
}

interface CallParams {
  to: string;
  from: string;
  callbackUrl: string;
  candidateName: string;
  callPurpose: string;
}

interface CallResult {
  success: boolean;
  callSid?: string;
  error?: string;
}

// 1. Twilio Provider (Original)
class TwilioProvider implements VoiceProvider {
  name = 'Twilio';
  private _client: any;
  private _initialized = false;

  // Getter methods for accessing private properties
  get client() { return this._client; }
  get initialized() { return this._initialized; }

  constructor() {
    this.initializeAsync();
  }

  private async initializeAsync() {
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    
    if (accountSid && authToken) {
      try {
        const twilio = await import('twilio');
        this._client = twilio.default(accountSid, authToken);
        this._initialized = true;
        console.log('Twilio client initialized successfully');
      } catch (error) {
        console.error('Failed to initialize Twilio:', error);
      }
    } else {
      console.log('Twilio credentials not found in environment');
    }
  }

  async waitForInitialization(timeout = 5000): Promise<void> {
    const start = Date.now();
    while (!this._initialized && Date.now() - start < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  async initiate(params: CallParams): Promise<CallResult> {
    console.log(`Twilio initiate called - initialized: ${this._initialized}, client exists: ${!!this._client}`);
    
    // Wait for initialization if not complete
    if (!this._initialized) {
      console.log('Waiting for Twilio initialization...');
      await this.waitForInitialization();
      console.log(`After waiting - initialized: ${this._initialized}, client exists: ${!!this._client}`);
    }
    
    if (!this._client) {
      console.error('Twilio client still not available after initialization wait');
      return { success: false, error: 'Twilio credentials invalid or missing' };
    }

    try {
      console.log(`Attempting Twilio call from ${params.from} to ${params.to}`);
      console.log(`TwilioProvider received callback URL: ${params.callbackUrl}`);
      
      // Force HTTPS and proper domain for production Twilio calls - CRITICAL FIX
      const replitDomain = 'https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev';
      let finalCallbackUrl = params.callbackUrl;
      console.log(`🔍 ORIGINAL: ${finalCallbackUrl}`);
      
      // Force replacement of localhost with proper Replit domain
      if (finalCallbackUrl.includes('localhost')) {
        const path = finalCallbackUrl.replace(/https?:\/\/localhost:5000/, '');
        finalCallbackUrl = `${replitDomain}${path}`;
        console.log(`🎯 FIXED FINAL URL: ${finalCallbackUrl}`);
      }
      
      // STEP 2: Add status callbacks to track call lifecycle from Twilio's side
      const statusCallbackUrl = finalCallbackUrl.replace('/twiml-elevenlabs', '/twilio-status');
      console.log(`📊 STEP 2: Status callback URL: ${statusCallbackUrl}`);
      
      const call = await this._client.calls.create({
        to: params.to,
        from: params.from,
        url: finalCallbackUrl,
        statusCallback: statusCallbackUrl,
        statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed'],
        statusCallbackMethod: 'POST',
        record: true
      });
      console.log(`Twilio call created successfully: ${call.sid}`);
      return { success: true, callSid: call.sid };
    } catch (error: any) {
      console.error('Twilio call failed:', error.message);
      return { success: false, error: error.message };
    }
  }
}

// 2. Vonage/Nexmo Provider (Alternative)
class VonageProvider implements VoiceProvider {
  name = 'Vonage';
  private client: any;

  constructor() {
    if (process.env.VONAGE_API_KEY && process.env.VONAGE_API_SECRET) {
      this.initializeAsync();
    }
  }

  private async initializeAsync() {
    try {
      const vonageModule = await import('@vonage/server-sdk');
      this.client = new vonageModule.Vonage({
        apiKey: process.env.VONAGE_API_KEY!,
        apiSecret: process.env.VONAGE_API_SECRET!,
        applicationId: process.env.VONAGE_APPLICATION_ID,
        privateKey: process.env.VONAGE_PRIVATE_KEY
      });
      console.log('Vonage client initialized successfully');
    } catch (error) {
      console.log('Vonage SDK not available:', error);
    }
  }

  async initiate(params: CallParams): Promise<CallResult> {
    if (!this.client) {
      return { success: false, error: 'Vonage not configured' };
    }

    try {
      const response = await this.client.voice.createOutboundCall({
        to: [{ type: 'phone', number: params.to }],
        from: { type: 'phone', number: params.from },
        answer_url: [params.callbackUrl],
        event_url: [params.callbackUrl + '/events']
      });
      return { success: true, callSid: response.uuid };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }
}

// 3. Amazon Connect Provider (AWS)
class AmazonConnectProvider implements VoiceProvider {
  name = 'Amazon Connect';
  private client: any;

  constructor() {
    if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
      this.initializeAsync();
    }
  }

  private async initializeAsync() {
    try {
      const { ConnectClient } = await import('@aws-sdk/client-connect');
      this.client = new ConnectClient({
        region: process.env.AWS_REGION || 'us-east-1',
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
        }
      });
      console.log('Amazon Connect client initialized successfully');
    } catch (error) {
      console.log('AWS SDK not available:', error);
    }
  }

  async initiate(params: CallParams): Promise<CallResult> {
    if (!this.client) {
      return { success: false, error: 'Amazon Connect not configured' };
    }

    // Amazon Connect requires more complex setup with contact flows
    return { success: false, error: 'Amazon Connect requires contact flow configuration' };
  }
}

// 4. Google Cloud Contact Center AI (CCAI)
class GoogleCCAIProvider implements VoiceProvider {
  name = 'Google CCAI';

  async initiate(params: CallParams): Promise<CallResult> {
    // Google CCAI implementation would go here
    return { success: false, error: 'Google CCAI not implemented yet' };
  }
}

// 5. Browser-based WebRTC Provider (No external service needed)
class WebRTCProvider implements VoiceProvider {
  name = 'WebRTC Browser Call';

  async initiate(params: CallParams): Promise<CallResult> {
    // This would initiate a browser-to-phone call using WebRTC
    // Requires WebRTC gateway service
    return { 
      success: true, 
      callSid: `webrtc_${Date.now()}`,
    };
  }
}

// 6. Manual Call Provider (Human-initiated)
class ManualCallProvider implements VoiceProvider {
  name = 'Manual Call';

  async initiate(params: CallParams): Promise<CallResult> {
    // This creates a call record but requires human to make the actual call
    return { 
      success: true, 
      callSid: `manual_${Date.now()}`,
    };
  }
}

// 7. Google Speech Enhanced Provider (Real-time transcription)
class GoogleSpeechProvider implements VoiceProvider {
  name = 'Google Speech Enhanced';

  async initiate(params: CallParams): Promise<CallResult> {
    // Check if Google credentials are available
    if (!process.env.GOOGLE_APPLICATION_CREDENTIALS || !process.env.GOOGLE_CLOUD_PROJECT_ID) {
      return { success: false, error: 'Google Speech service not configured' };
    }
    
    // This uses the same Twilio infrastructure but with Google Speech TwiML
    const twilioProvider = new TwilioProvider();
    await twilioProvider.waitForInitialization();
    
    if (!twilioProvider.client) {
      return { success: false, error: 'Twilio credentials required for Google Speech calls' };
    }
    
    try {
      // Use Google Speech TwiML endpoint instead of regular TwiML
      let googleSpeechUrl = params.callbackUrl.replace('/twiml/', '/google-speech-twiml/');
      
      // Fix localhost URLs to use proper Replit domain - CRITICAL FIX for Google Speech
      const replitDomain = 'https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev';
      if (googleSpeechUrl.includes('localhost')) {
        const path = googleSpeechUrl.replace(/https?:\/\/localhost:5000/, '');
        googleSpeechUrl = `${replitDomain}${path}`;
        console.log(`🎯 FIXED Google Speech URL: ${googleSpeechUrl}`);
      }
      
      const call = await twilioProvider.client.calls.create({
        to: params.to,
        from: params.from,
        url: googleSpeechUrl,
        record: true
      });
      
      console.log(`Google Speech enhanced call created: ${call.sid}`);
      return { success: true, callSid: call.sid };
    } catch (error: any) {
      console.error('Google Speech call failed:', error.message);
      return { success: false, error: error.message };
    }
  }
}

// Provider singletons - initialize immediately
let twilioProvider: TwilioProvider | null = null;
let vonageProvider: VonageProvider | null = null;
let amazonProvider: AmazonConnectProvider | null = null;
let googleProvider: GoogleCCAIProvider | null = null;
let googleSpeechProvider: GoogleSpeechProvider | null = null;
let manualProvider: ManualCallProvider | null = null;
let webrtcProvider: WebRTCProvider | null = null;

// Initialize providers at module load
console.log('Initializing voice providers at startup...');
if (process.env.TWILIO_ACCOUNT_SID) {
  console.log('Creating Twilio provider at startup...');
  twilioProvider = new TwilioProvider();
}
manualProvider = new ManualCallProvider();
webrtcProvider = new WebRTCProvider();
googleProvider = new GoogleCCAIProvider();
googleSpeechProvider = new GoogleSpeechProvider();

// Provider factory - return existing singletons
const getAvailableProviders = (): VoiceProvider[] => {
  const providers: VoiceProvider[] = [];
  
  // Add pre-initialized providers
  if (manualProvider) providers.push(manualProvider);
  if (webrtcProvider) providers.push(webrtcProvider);
  if (twilioProvider) providers.push(twilioProvider);
  if (vonageProvider) providers.push(vonageProvider);
  if (amazonProvider) providers.push(amazonProvider);
  if (googleProvider) providers.push(googleProvider);
  if (googleSpeechProvider) providers.push(googleSpeechProvider);
  
  return providers;
};

// Get available voice providers
router.get('/providers', async (req, res) => {
  try {
    // For now, return providers without authentication to fix the dropdown
    // TODO: Re-enable authentication once session management is fixed
    const providers = getAvailableProviders();
    
    // Filter to only return Twilio provider
    const providerInfo = providers
      .filter(provider => provider.name === 'Twilio')
      .map(provider => ({
        name: provider.name,
        available: true // You can add more sophisticated availability checks
      }));
    
    console.log('Returning providers:', providerInfo);
    res.json(providerInfo);
  } catch (error) {
    console.error('Get providers error:', error);
    res.status(500).json({ error: 'Failed to get voice providers' });
  }
});

// Initiate call with selected provider
router.post('/initiate/:provider/:candidateId', async (req, res) => {
  try {
    const { provider: providerName, candidateId } = req.params;
    const { phoneNumber, scheduledAt, callPurpose } = req.body;
    
    console.log('🚀 VOICE CALL INITIATION REQUEST:');
    console.log('  Provider:', providerName);
    console.log('  Candidate ID:', candidateId);
    console.log('  Phone:', phoneNumber);
    console.log('  Purpose:', callPurpose);
    console.log('  Scheduled:', scheduledAt);
    
    // Debug timestamp handling
    if (scheduledAt) {
      console.log('Received scheduledAt:', scheduledAt);
      console.log('Parsed as Date:', new Date(scheduledAt));
      console.log('Date toISOString:', new Date(scheduledAt).toISOString());
      console.log('Date toLocaleString:', new Date(scheduledAt).toLocaleString());
    }
    
    // For demo purposes, using proper UUIDs until authentication is fixed
    const userId = null; // Make this nullable since no real user is authenticated
    const organizationId = 'demo-org-id';

    // Get candidate - for demo, find any candidate with this ID
    const candidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);

    if (!candidate.length) {
      console.log('Candidate not found:', candidateId);
      return res.status(404).json({ error: 'Candidate not found' });
    }
    
    console.log('Found candidate:', candidate[0]);

    // Create call record first
    const [voiceCall] = await db
      .insert(voiceCalls)
      .values({
        candidateId,
        organizationId: candidate[0].organizationId, // Use candidate's org ID
        jobPostingId: candidate[0].appliedJobId || null, // 🎯 CRITICAL FIX: Link to job candidate applied for
        initiatedBy: userId, // Can be null
        phoneNumber: phoneNumber || candidate[0].phone || '',
        scheduledAt: scheduledAt ? (() => {
          // Handle datetime-local format (YYYY-MM-DDTHH:MM) by treating it as local time
          if (scheduledAt.length === 16 && scheduledAt.includes('T')) {
            // Parse as local time by creating Date object with local timezone
            const [datePart, timePart] = scheduledAt.split('T');
            const [year, month, day] = datePart.split('-').map(Number);
            const [hour, minute] = timePart.split(':').map(Number);
            return new Date(year, month - 1, day, hour, minute);
          }
          return new Date(scheduledAt);
        })() : new Date(),
        callPurpose: callPurpose || 'technical_interview_scheduling',
        status: 'scheduled'
      })
      .returning();
      
    console.log('Created voice call record:', voiceCall);

    // Get provider - ensure singletons are used
    const providers = getAvailableProviders();
    console.log('Available providers:', providers.map(p => ({ name: p.name, slug: p.name.toLowerCase().replace(/\s+/g, '-') })));
    console.log('Looking for provider:', providerName);
    
    const selectedProvider = providers.find(p => p.name.toLowerCase().replace(/\s+/g, '-') === providerName);
    
    if (!selectedProvider) {
      return res.status(400).json({ error: 'Provider not found' });
    }
    
    console.log(`Selected provider: ${selectedProvider.name}`);
    
    // For Twilio, ensure it's properly initialized
    if (selectedProvider.name === 'Twilio' && selectedProvider instanceof TwilioProvider) {
      if (!selectedProvider.initialized) {
        console.log('Waiting for Twilio initialization...');
        await selectedProvider.waitForInitialization();
      }
    }

    // Handle different provider types
    if (selectedProvider.name === 'Manual Call') {
      // Create notes for manual call
      await db.insert(voiceCallNotes).values({
        callId: voiceCall.id,
        organizationId: candidate[0].organizationId,
        noteType: 'follow_up',
        content: `Manual call scheduled for ${candidate[0].fullName || 'candidate'} at ${phoneNumber || candidate[0].phone}. Please call and discuss technical interview availability.`,
        importance: 5,
        actionRequired: true,
        aiGenerated: false
      });
      
      console.log('Manual call scheduled successfully');

      return res.json({
        success: true,
        callId: voiceCall.id,
        status: 'manual_call_scheduled',
        message: 'Manual call scheduled. Please call the candidate directly.',
        phoneNumber: phoneNumber || candidate[0].phone
      });
    }

    if (selectedProvider.name === 'WebRTC Browser Call') {
      return res.json({
        success: true,
        callId: voiceCall.id,
        status: 'webrtc_ready',
        message: 'WebRTC call ready. Click to initiate browser-based call.',
        phoneNumber: phoneNumber || candidate[0].phone
      });
    }

    // For automated providers, attempt to initiate call
    if (!scheduledAt || new Date(scheduledAt) <= new Date()) {
      const host = req.get('host');
      // STEP 4 TESTING: Use ElevenLabs TwiML endpoint (full integration)
      const callbackUrl = `https://${host}/api/twiml-elevenlabs`;
      console.log(`🎯 AUTOMATED CALL INITIATION:`);
      console.log(`  Host: ${host}`);
      console.log(`  TwiML Callback URL: ${callbackUrl}`);
      console.log(`  Provider: ${selectedProvider.name}`);
      
      const callResult = await selectedProvider.initiate({
        to: phoneNumber || candidate[0].phone || '',
        from: process.env.TWILIO_PHONE_NUMBER || '+**********',
        callbackUrl,
        candidateName: candidate[0].fullName || 'candidate',
        callPurpose: callPurpose || 'technical_interview_scheduling'
      });

      if (callResult.success) {
        await db
          .update(voiceCalls)
          .set({
            twilioCallSid: callResult.callSid,
            status: 'in_progress',
            startedAt: new Date()
          })
          .where(eq(voiceCalls.id, voiceCall.id));

        res.json({
          success: true,
          callId: voiceCall.id,
          twilioCallSid: callResult.callSid,
          status: 'initiated',
          provider: selectedProvider.name
        });
      } else {
        await db
          .update(voiceCalls)
          .set({ status: 'failed' })
          .where(eq(voiceCalls.id, voiceCall.id));

        res.status(500).json({
          error: `${selectedProvider.name} call failed`,
          details: callResult.error
        });
      }
    } else {
      res.json({
        success: true,
        callId: voiceCall.id,
        status: 'scheduled',
        scheduledAt: voiceCall.scheduledAt,
        provider: selectedProvider.name
      });
    }
  } catch (error) {
    console.error('Provider call initiation error:', error);
    res.status(500).json({ error: 'Failed to initiate call' });
  }
});

// Get call history for a candidate - scoped to specific job application
router.get('/calls/:candidateId', async (req, res) => {
  try {
    const { candidateId } = req.params;
    
    // Get candidate with job posting info for proper scoping
    const candidate = await db
      .select({ 
        organizationId: candidates.organizationId,
        appliedJobId: candidates.appliedJobId,
        email: candidates.email
      })
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);
    
    if (!candidate.length) {
      return res.status(404).json({ error: 'Candidate not found' });
    }
    
    const { organizationId, appliedJobId, email } = candidate[0];

    // Only fetch calls for THIS specific candidate record (email + job posting combination)
    // This ensures conversation history is isolated per job application
    const calls = await db
      .select({
        id: voiceCalls.id,
        status: voiceCalls.status,
        scheduledAt: voiceCalls.scheduledAt,
        startedAt: voiceCalls.startedAt,
        endedAt: voiceCalls.endedAt,
        durationSeconds: voiceCalls.durationSeconds,
        callPurpose: voiceCalls.callPurpose,
        recordingUrl: voiceCalls.recordingUrl,
        conversationNotes: voiceCalls.conversationNotes,
        callRating: voiceCalls.callRating,
        followUpRequired: voiceCalls.followUpRequired,
        createdAt: voiceCalls.createdAt
      })
      .from(voiceCalls)
      .where(and(
        eq(voiceCalls.candidateId, candidateId), // This candidate record only
        eq(voiceCalls.organizationId, organizationId) // Organization security
      ))
      .orderBy(voiceCalls.createdAt);

    console.log(`🔍 Fetching calls for candidate ${candidateId} (email: ${email}, job: ${appliedJobId})`);
    res.json(calls);
  } catch (error) {
    console.error('Get call history error:', error);
    res.status(500).json({ error: 'Failed to fetch call history' });
  }
});

/**
 * @swagger
 * /api/voice-providers/calls/{callId}/notes:
 *   get:
 *     summary: Get call notes and transcripts
 *     description: Retrieve all notes and transcripts for a specific voice call, including AI responses and user transcripts
 *     tags:
 *       - Voice Providers
 *     parameters:
 *       - in: path
 *         name: callId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The voice call ID
 *     responses:
 *       200:
 *         description: Call notes retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     format: uuid
 *                   callId:
 *                     type: string
 *                     format: uuid
 *                   organizationId:
 *                     type: string
 *                     format: uuid
 *                   noteType:
 *                     type: string
 *                     enum: [transcription, ai_response, experience, availability, concern, positive, follow_up]
 *                     description: Type of note - transcription for user speech, ai_response for agent responses
 *                   content:
 *                     type: string
 *                     description: The actual transcript or note content
 *                   timestamp:
 *                     type: string
 *                     format: date-time
 *                   aiGenerated:
 *                     type: boolean
 *                     description: True if this is an AI agent response
 *                   importance:
 *                     type: integer
 *                     minimum: 1
 *                     maximum: 5
 *                   actionRequired:
 *                     type: boolean
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *       404:
 *         description: Call not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Call not found"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to fetch call notes"
 */
// Get call notes (no auth required for demo)
router.get('/calls/:callId/notes', async (req, res) => {
  try {
    const { callId } = req.params;
    
    // Get call organization for data isolation
    const call = await db
      .select({ organizationId: voiceCalls.organizationId })
      .from(voiceCalls)
      .where(eq(voiceCalls.id, callId))
      .limit(1);
    
    if (!call.length) {
      return res.status(404).json({ error: 'Call not found' });
    }
    
    const organizationId = call[0].organizationId;

    const notes = await db
      .select()
      .from(voiceCallNotes)
      .where(and(
        eq(voiceCallNotes.callId, callId),
        eq(voiceCallNotes.organizationId, organizationId)
      ))
      .orderBy(voiceCallNotes.timestamp);

    res.json(notes);
  } catch (error) {
    console.error('Get call notes error:', error);
    res.status(500).json({ error: 'Failed to fetch call notes' });
  }
});

// Interactive Voice Agent TwiML endpoint - RENAMED to avoid conflict with ElevenLabs
router.post('/interactive-twiml/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    const { SpeechResult, Digits } = req.body;
    console.log(`Interactive TwiML request for call ${callId}`, { SpeechResult, Digits });
    
    // Get call details
    const [call] = await db.select().from(voiceCalls).where(eq(voiceCalls.id, callId));
    if (!call) {
      return res.status(404).send('Call not found');
    }

    // Get candidate details
    const [candidate] = await db.select().from(candidates).where(eq(candidates.id, call.candidateId));
    
    // Get organization details
    const [organization] = await db.select().from(organizations).where(eq(organizations.id, call.organizationId));
    
    // CRITICAL FIX: Get job posting details from the call record first, then fallback to candidate
    let jobPosting = null;
    if (call.jobPostingId) {
      // Use job posting ID from the call record (most accurate)
      const jobResult = await db.select().from(jobPostings).where(eq(jobPostings.id, call.jobPostingId));
      jobPosting = jobResult[0] || null;
    } else if (candidate?.appliedJobId) {
      // Fallback to candidate's applied job (legacy calls)
      const jobResult = await db.select().from(jobPostings).where(eq(jobPostings.id, candidate.appliedJobId));
      jobPosting = jobResult[0] || null;
    }
    
    let twiml = '';
    
    if (!SpeechResult && !Digits) {
      // Initial call - use varied greeting templates
      const candidateName = candidate?.fullName ? ` ${candidate.fullName.split(' ')[0]}` : '';
      const roleName = jobPosting?.title || 'the position you applied for';
      const companyName = organization?.name || 'our company'; // FIXED: Use organization name, not department
      
      // Rotate through Sarah's casual greeting variations
      const greetings = [
        `Hey${candidateName}! Hope I'm not catching you at a bad time. This is Sarah from ${companyName} — ready for a quick chat about the ${roleName} position?`,
        `Hi there${candidateName}! Thanks for picking up. Mind if we chat for a few minutes about the role? This is Sarah from the hiring team.`,
        `Hey${candidateName}! Sarah here from ${companyName}. Got a couple minutes to chat about the ${roleName} position? Can you hear me okay?`
      ];
      
      const selectedGreeting = greetings[Math.floor(Math.random() * greetings.length)];
      
      // Try ElevenLabs first, fallback to Polly
      const useElevenLabs = elevenLabsService.isConfigured();
      
      // ELEVENLABS CONVERSATIONAL AI: Pure WebSocket streaming for real conversations
      // NO TwiML fallback - direct ElevenLabs connection only
      if (process.env.ELEVENLABS_AGENT_ID) {
        console.log('🤖 Configuring ElevenLabs Conversational AI...');
        console.log('📝 Environment check - ELEVENLABS_AGENT_ID:', process.env.ELEVENLABS_AGENT_ID?.substring(0, 10) + '...');
        console.log('📝 Environment check - ELEVENLABS_API_KEY exists:', !!process.env.ELEVENLABS_API_KEY);
        
        // DEBUG: Log fetched data to identify context mapping issues
        console.log('🔍 CONTEXT DEBUG - Call ID:', callId);
        console.log('🔍 CONTEXT DEBUG - Call data:', { 
          callPurpose: call.callPurpose,
          candidateId: call.candidateId,
          organizationId: call.organizationId,
          jobPostingId: call.jobPostingId 
        });
        console.log('🔍 CONTEXT DEBUG - Candidate data:', {
          fullName: candidate?.fullName,
          appliedJobId: candidate?.appliedJobId,
          currentCompany: candidate?.currentCompany
        });
        console.log('🔍 CONTEXT DEBUG - Job posting data:', {
          title: jobPosting?.title,
          department: jobPosting?.department,
          organizationId: jobPosting?.organizationId
        });
        console.log('🔍 CONTEXT DEBUG - Organization data:', {
          name: organization?.name,
          domain: organization?.domain
        });

        // Create enhanced conversation metadata with comprehensive context
        const conversationMetadata = {
          call_id: callId,
          candidate_name: candidate?.fullName || 'Candidate',
          job_title: jobPosting?.title || 'Available Position',
          company_name: organization?.name || 'Our Company',
          call_purpose: call.callPurpose || 'screening',
          
          // ENHANCED COMPANY CONTEXT
          company_industry: organization?.domain || 'Not specified',
          company_overview: organization?.name ? `${organization.name} - Leading organization in ${organization.domain || 'our field'}` : 'Leading organization in our field',
          
          // ENHANCED JOB CONTEXT  
          experience_level: jobPosting?.experienceLevel || 'Not specified',
          salary_range: jobPosting?.salaryRange || 'Competitive compensation',
          work_environment: jobPosting?.workEnvironment || 'Collaborative team environment',
          key_responsibilities: jobPosting?.responsibilities || 'Will be discussed during interview',
          job_requirements: jobPosting?.requirements || 'Will be discussed during interview',
          benefits_highlights: jobPosting?.benefits || 'Comprehensive benefits package',
          
          // ENHANCED CANDIDATE CONTEXT
          current_company: candidate?.currentCompany || 'Not specified',
          candidate_education: candidate?.education || 'Not specified',
          match_score: candidate?.matchScore || 0,
          key_skills: candidate?.skills ? (Array.isArray(candidate.skills) ? candidate.skills.join(', ') : candidate.skills) : 'Not specified',
          
          // COMPLETE CONTEXT STRING FOR ELEVENLABS
          full_context: `COMPANY CONTEXT:
- Company: ${organization?.name || 'Our company'}
- Industry: ${organization?.domain || 'Not specified'}
- About: ${organization?.name ? `${organization.name} - Leading organization in ${organization.domain || 'our field'}` : 'Leading organization in our field'}

JOB CONTEXT:
- Position: ${jobPosting?.title || 'Open position'}
- Experience Level: ${jobPosting?.experienceLevel || 'Not specified'}
- Salary Range: ${jobPosting?.salaryRange || 'Competitive compensation'}
- Work Environment: ${jobPosting?.workEnvironment || 'Collaborative team environment'}

KEY RESPONSIBILITIES:
${jobPosting?.responsibilities || 'Will be discussed during interview'}

REQUIREMENTS:
${jobPosting?.requirements || 'Will be discussed during interview'}

BENEFITS HIGHLIGHTS:
${jobPosting?.benefits || 'Comprehensive benefits package'}

CANDIDATE CONTEXT:
- Current Company: ${candidate?.currentCompany || 'Not specified'}
- Education: ${candidate?.education || 'Not specified'}  
- Match Score: ${candidate?.matchScore || 0}/100
- Key Skills: ${candidate?.skills ? (Array.isArray(candidate.skills) ? candidate.skills.join(', ') : candidate.skills) : 'Not specified'}`
        };
        
        // Use media stream to bridge Twilio and ElevenLabs
        // Must use the public domain for WebSocket connection
        const host = req.get('host') || 'localhost:5000';
        const publicHost = host.includes('localhost') 
          ? 'd06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev'
          : host.replace(':5000', '');
        const mediaStreamUrl = `wss://${publicHost}/elevenlabs-stream-final`;
        
        console.log(`🔗 WebSocket URL for ElevenLabs streaming: ${mediaStreamUrl}`);
        
        // ElevenLabs streaming with fallback in case WebSocket fails
        twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Connect>
        <Stream url="${mediaStreamUrl}">
            <Parameter name="agent_id" value="${process.env.ELEVENLABS_AGENT_ID}" />
            <Parameter name="call_id" value="${callId}" />
            <Parameter name="candidate_name" value="${conversationMetadata.candidate_name}" />
            <Parameter name="job_title" value="${conversationMetadata.job_title}" />
            <Parameter name="company_name" value="${conversationMetadata.company_name}" />
            <Parameter name="call_purpose" value="${conversationMetadata.call_purpose}" />
            <Parameter name="company_industry" value="${conversationMetadata.company_industry}" />
            <Parameter name="company_overview" value="${conversationMetadata.company_overview}" />
            <Parameter name="experience_level" value="${conversationMetadata.experience_level}" />
            <Parameter name="salary_range" value="${conversationMetadata.salary_range}" />
            <Parameter name="work_environment" value="${conversationMetadata.work_environment}" />
            <Parameter name="key_responsibilities" value="${conversationMetadata.key_responsibilities}" />
            <Parameter name="job_requirements" value="${conversationMetadata.job_requirements}" />
            <Parameter name="benefits_highlights" value="${conversationMetadata.benefits_highlights}" />
            <Parameter name="current_company" value="${conversationMetadata.current_company}" />
            <Parameter name="candidate_education" value="${conversationMetadata.candidate_education}" />
            <Parameter name="match_score" value="${conversationMetadata.match_score}" />
            <Parameter name="key_skills" value="${conversationMetadata.key_skills}" />
            <Parameter name="full_context" value="${conversationMetadata.full_context}" />
        </Stream>
    </Connect>
    <Say>If you're hearing this message, the connection failed. Please try again later.</Say>
    <Hangup/>
</Response>`;

        console.log('✅ ElevenLabs Conversational AI configured for pure WebSocket streaming');
        console.log('📄 Generated TwiML for ElevenLabs:', twiml);
        
        // Update call status to in_progress
        await voiceCallManager.updateCallStatus(callId, 'in_progress');
      } else {
        // No ElevenLabs agent configured - reject the call
        console.error('❌ No ELEVENLABS_AGENT_ID configured');
        twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say>System configuration error. Please contact support.</Say>
    <Hangup/>
</Response>`;
      }
    } else {
      // Process candidate response using AI
      const aiResponse = await processVoiceInteraction(SpeechResult || Digits, call, candidate, jobPosting);
      
      // Generate AI response using ElevenLabs if available
      const useElevenLabs = elevenLabsService.isConfigured();
      
      // For ongoing conversations - this should not be reached with WebSocket integration
      // This endpoint would only be called if WebSocket connection fails
      console.log('⚠️ Fallback TwiML endpoint called - WebSocket may have disconnected');
      
      twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra">Thank you for your time. I'll follow up with an email shortly.</Say>
    <Hangup/>
</Response>`;
      
      // Update call to completed status
      await voiceCallManager.updateCallStatus(callId, 'completed');

      // Save conversation notes
      await db.insert(voiceCallNotes).values({
        callId: call.id,
        organizationId: call.organizationId,
        noteType: 'conversation',
        content: `Candidate said: "${SpeechResult || Digits}" | AI Response: "${aiResponse.response}"`,
        importance: 5,
        actionRequired: aiResponse.actionRequired,
        aiGenerated: true
      });
    }

    res.set('Content-Type', 'text/xml');
    res.send(twiml);
    
  } catch (error) {
    console.error('Interactive TwiML error:', error);
    // CRITICAL: Always return valid TwiML, never a 500 error to prevent "application error"
    const errorTwiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra" rate="medium" pitch="medium">I'm having some technical difficulties. Let me call you back in a few minutes. Thanks for your patience!</Say>
    <Hangup/>
</Response>`;
    res.set('Content-Type', 'text/xml');
    res.send(errorTwiml);
  }
});

// Enhanced processing endpoint - Whisper primary with Twilio fallback
router.post('/process/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    const { SpeechResult, Digits, RecordingUrl } = req.body;
    
    console.log(`🗣️ Processing speech for call ${callId}:`, { SpeechResult, Digits, RecordingUrl });
    
    // Try Whisper first if recording URL available, fallback to Twilio speech
    let finalTranscription = SpeechResult || Digits || '';
    
    if (RecordingUrl && !finalTranscription) {
      console.log(`🎤 Attempting Whisper transcription for better accuracy...`);
      const whisperResult = await transcribeWithWhisper(RecordingUrl, callId);
      if (whisperResult) {
        finalTranscription = whisperResult;
        console.log(`✅ Using Whisper transcription: "${finalTranscription}"`);
      } else {
        console.log(`⚠️ Whisper failed, using Twilio fallback: "${finalTranscription}"`);
      }
    }
    
    // Get call details
    const [call] = await db.select().from(voiceCalls).where(eq(voiceCalls.id, callId));
    if (!call) {
      console.error('Call not found:', callId);
      return res.status(404).send('Call not found');
    }
    
    // Get candidate and job posting details
    const [candidate] = await db.select().from(candidates).where(eq(candidates.id, call.candidateId));
    let jobPosting = null;
    if (candidate?.appliedJobId) {
      const jobResult = await db.select().from(jobPostings).where(eq(jobPostings.id, candidate.appliedJobId));
      jobPosting = jobResult[0] || null;
    }
    
    if (!finalTranscription) {
      console.log('❌ No speech detected, ending call');
      const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra" rate="medium" pitch="medium">Sorry, I didn't catch that. I'll follow up with an email. Thanks for your time!</Say>
    <Hangup/>
</Response>`;
      res.set('Content-Type', 'text/xml');
      return res.send(twiml);
    }
    
    // Process AI response using best available transcription
    const aiResponse = await processVoiceInteraction(finalTranscription, call, candidate, jobPosting);
    
    // Generate TwiML response
    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra" rate="medium" pitch="medium">${aiResponse.response}</Say>
    ${aiResponse.shouldContinue ? `
    <Gather input="speech" timeout="10" speechTimeout="3" speechModel="phone_call" action="/api/voice-providers/twiml/${callId}" method="POST">
        <Say voice="Polly.Kendra" rate="medium" pitch="medium"></Say>
    </Gather>
    <Say voice="Polly.Kendra" rate="medium" pitch="medium">No worries if you're busy! I'll shoot you an email with some scheduling options. Thanks for chatting with me!</Say>
    ` : ''}
    <Hangup/>
</Response>`;

    // Save conversation notes with best transcription source
    const sourceTag = RecordingUrl && finalTranscription !== (SpeechResult || Digits || '') ? '[WHISPER]' : '[TWILIO]';
    await db.insert(voiceCallNotes).values({
      callId: call.id,
      organizationId: call.organizationId,
      noteType: 'conversation',
      content: `${sourceTag} Candidate said: "${finalTranscription}" | AI Response: "${aiResponse.response}"`,
      importance: 5,
      actionRequired: aiResponse.actionRequired,
      aiGenerated: true
    });

    res.set('Content-Type', 'text/xml');
    res.send(twiml);
    
  } catch (error) {
    console.error('❌ Speech processing error:', error);
    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra" rate="medium" pitch="medium">I'm having trouble hearing you clearly. I'll send you an email to follow up. Have a great day!</Say>
    <Hangup/>
</Response>`;
    res.set('Content-Type', 'text/xml');
    res.send(twiml);
  }
});

// Legacy Whisper transcription endpoint - processes recordings with OpenAI Whisper (keeping for fallback)
router.post('/whisper/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    const { RecordingUrl, RecordingSid } = req.body;
    
    console.log(`🎤 Whisper endpoint called for ${callId}:`, { RecordingUrl, RecordingSid });
    
    if (!RecordingUrl) {
      console.log('❌ No recording URL provided, falling back to speech processing');
      return res.status(400).send('No recording URL provided');
    }
    
    // Get call details
    const [call] = await db.select().from(voiceCalls).where(eq(voiceCalls.id, callId));
    if (!call) {
      return res.status(404).send('Call not found');
    }

    // Get candidate and job posting details
    const [candidate] = await db.select().from(candidates).where(eq(candidates.id, call.candidateId));
    let jobPosting = null;
    if (candidate?.appliedJobId) {
      const jobResult = await db.select().from(jobPostings).where(eq(jobPostings.id, candidate.appliedJobId));
      jobPosting = jobResult[0] || null;
    }
    
    // Transcribe with Whisper
    const whisperTranscription = await transcribeWithWhisper(RecordingUrl, callId);
    
    if (!whisperTranscription) {
      console.log('❌ Whisper transcription failed, ending call');
      const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra" rate="medium" pitch="medium">Sorry, I didn't catch that. I'll follow up with an email. Thanks for your time!</Say>
    <Hangup/>
</Response>`;
      res.set('Content-Type', 'text/xml');
      return res.send(twiml);
    }
    
    // Process AI response using Whisper transcription
    const aiResponse = await processVoiceInteraction(whisperTranscription, call, candidate, jobPosting);
    
    // Generate TwiML response
    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra" rate="medium" pitch="medium">${aiResponse.response}</Say>
    ${aiResponse.shouldContinue ? `
    <Record timeout="8" maxLength="20" action="/api/voice-providers/whisper/${callId}" recordingStatusCallback="/api/voice-providers/recording-status/${callId}" />
    <Say voice="Polly.Kendra" rate="medium" pitch="medium">No worries if you're busy! I'll shoot you an email with some scheduling options. Thanks for chatting with me!</Say>
    ` : ''}
    <Hangup/>
</Response>`;

    // Save conversation notes with Whisper transcription
    await db.insert(voiceCallNotes).values({
      callId: call.id,
      organizationId: call.organizationId,
      noteType: 'conversation',
      content: `[WHISPER] Candidate said: "${whisperTranscription}" | AI Response: "${aiResponse.response}"`,
      importance: 5,
      actionRequired: aiResponse.actionRequired,
      aiGenerated: true
    });

    res.set('Content-Type', 'text/xml');
    res.send(twiml);
    
  } catch (error) {
    console.error('❌ Whisper processing error:', error);
    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra" rate="medium" pitch="medium">I'm having trouble hearing you clearly. I'll send you an email to follow up. Have a great day!</Say>
    <Hangup/>
</Response>`;
    res.set('Content-Type', 'text/xml');
    res.send(twiml);
  }
});

// Recording status callback endpoint
router.post('/recording-status/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    const { RecordingUrl, RecordingSid, RecordingStatus } = req.body;
    
    console.log(`📹 Recording status for ${callId}:`, { RecordingStatus, RecordingSid, RecordingUrl });
    
    // Update call record with recording URL if completed
    if (RecordingStatus === 'completed' && RecordingUrl) {
      await db.update(voiceCalls)
        .set({ recordingUrl: RecordingUrl })
        .where(eq(voiceCalls.id, callId));
    }
    
    res.status(200).send('OK');
  } catch (error) {
    console.error('Recording status callback error:', error);
    res.status(500).send('Error');
  }
});

// Twilio status callback endpoint
router.post('/callback/twilio', async (req, res) => {
  try {
    const { CallSid, CallStatus, Duration } = req.body;
    console.log(`Twilio callback: ${CallSid} - ${CallStatus}`);
    
    // Update call record based on status
    if (CallStatus === 'completed' && Duration) {
      await db.update(voiceCalls)
        .set({ 
          status: 'completed',
          durationSeconds: parseInt(Duration),
          endedAt: new Date()
        })
        .where(eq(voiceCalls.twilioCallSid, CallSid));
    }
    
    res.status(200).send('OK');
  } catch (error) {
    console.error('Twilio callback error:', error);
    res.status(500).send('Error');
  }
});

// AI-powered voice interaction processor
async function processVoiceInteraction(speechInput: string, call: any, candidate: any, jobPosting: any = null) {
  try {
    console.log(`Processing voice input: "${speechInput}"`);
    
    // Import OpenAI dynamically
    const { OpenAI } = await import('openai');
    const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

    // Get conversation history for context
    const conversationHistory = await db.select()
      .from(voiceCallNotes)
      .where(eq(voiceCallNotes.callId, call.id))
      .orderBy(voiceCallNotes.createdAt);

    const previousContext = conversationHistory.length > 0 ? 
      `\nPREVIOUS CONVERSATION:\n${conversationHistory.map(note => note.content).join('\n')}` : '';

    // Check if time has been scheduled based on conversation history
    const conversationText = conversationHistory.map(note => note.content).join(' ').toLowerCase();
    const hasScheduledTime = conversationText.includes('friday') || 
                           conversationText.includes('monday') || 
                           conversationText.includes('tuesday') || 
                           conversationText.includes('wednesday') || 
                           conversationText.includes('thursday') || 
                           conversationText.includes('10') || 
                           conversationText.includes('am') || 
                           conversationText.includes('pm');
    
    // Enhanced conversation stage analysis with better logic
    const thanksResponses = ['thank you', 'thanks', 'sounds good', 'perfect', 'great', 'excellent'];
    const isThankingOrConfirming = thanksResponses.some(phrase => speechInput.toLowerCase().includes(phrase));
    
    const conversationStage = conversationHistory.length === 0 ? 'greeting' : 
                             conversationHistory.length === 1 ? 'casual_chat' :
                             conversationHistory.length <= 4 && !hasScheduledTime ? 'getting_comfortable' : 
                             hasScheduledTime && isThankingOrConfirming ? 'end_call' : 
                             hasScheduledTime ? 'post_scheduling' : 'scheduling_focus';

    // Create comprehensive job information context
    const jobInfo = jobPosting ? `
JOB DETAILS:
- Position: ${jobPosting.title}
- Department: ${jobPosting.department || 'Not specified'}
- Location: ${jobPosting.location || 'Not specified'}
- Employment Type: ${jobPosting.employmentType || 'Not specified'}
- Salary Range: ${jobPosting.salaryMin && jobPosting.salaryMax ? `$${jobPosting.salaryMin} - $${jobPosting.salaryMax}` : 'Competitive salary'}
- Key Requirements: ${jobPosting.requirements ? jobPosting.requirements.slice(0, 3).join(', ') : 'Technical skills and experience'}
- Job Description: ${jobPosting.description ? jobPosting.description.substring(0, 200) + '...' : 'Exciting technical role with growth opportunities'}
- Benefits: ${jobPosting.benefits ? jobPosting.benefits.slice(0, 2).join(', ') : 'Comprehensive benefits package'}
` : `
JOB DETAILS:
- Position: Technical role (specific details will be discussed in interview)
- We have an exciting opportunity that matches your background
`;

    // Detect candidate mood and conversation context
    const candidateMood = speechInput.toLowerCase().includes('excited') ? 'enthusiastic' :
                         speechInput.toLowerCase().includes('busy') ? 'busy' : 
                         speechInput.toLowerCase().includes('not sure') ? 'hesitant' : 'neutral';
    
    // Track used phrases to avoid repetition
    const usedPhrases = conversationHistory.map(note => note.content).join(' ').toLowerCase();
    
    // Create human-like, stage-aware prompt with Sarah's personality for Polly.Kendra voice and Whisper transcription
    const systemPrompt = `You are Sarah, a warm, funny, and personable Talent Acquisition recruiter at a fast-growing tech company speaking with Polly.Kendra voice.

ENHANCED AUDIO PROCESSING: You now receive high-quality transcriptions from OpenAI Whisper, which means you can understand natural speech patterns, accents, and conversational nuances much better than before. Use this improved understanding to respond more naturally to candidate input.

Your tone is casual but respectful — you speak like a human, not a robot. You add light humor and friendly remarks, but never come across as unprofessional or sarcastic. You aim to make candidates feel comfortable, engaged, and excited to talk.

SARAH'S PERSONALITY TRAITS:
- You're empathetic, upbeat, and good at putting people at ease
- React naturally: laugh lightly at jokes, express interest in achievements, say "That's awesome!" or "Nice!"
- Use contractions, real human phrasing, and vary your language to avoid sounding repetitive
- Keep it conversational, not scripted - this is a chat between humans, not an interrogation
- Use questions that encourage storytelling, not just "yes/no" answers
- Respond to candidate input in a personalized way - mention what they said if relevant
- Avoid repeating acknowledgments - mix it up with "Cool," "Sounds good," "Nice!" etc.
- Keep responses under 10 words for rapid, interactive dialogue
- Ask ONE simple question per response to maintain conversation flow
- React to their EXACT words before moving forward

SARAH'S CONVERSATION STYLE:
WARM INTROS:
- "Hey! Hope I'm not catching you at a bad time - ready for a quick chat?"
- "Hi there! Thanks for picking up. Mind if we chat for a few minutes about the role?"

NATURAL REACTIONS TO TECH EXPERIENCE:
- If they mention React: "Oh nice, React's been super popular lately. Did you build anything cool with it?"
- If they mention a tech stack: "Nice, I've heard great things about that tech stack!"
- For achievements: "That's awesome!" or "Wow, that sounds challenging but really cool!"

PUTTING CANDIDATES AT EASE:
- If nervous: "No worries — this isn't a grilling session. Just a chat between two humans. You've got this."
- If hesitant: "Take your time! I'm just curious about your experience."

TEAM/CULTURE QUESTIONS:
- "The team's a fun bunch — pretty collaborative and always willing to help each other out. Sound like your kind of environment?"
- "What's been your favorite team dynamic so far in your career?"

AVAILABILITY/WRAP-UP:
- "This has been great! When would work best for you to chat with the hiring manager?"
- "Love hearing about your experience! Are you free sometime next week for a deeper dive?"

CANDIDATE INFO:
- Name: ${candidate?.fullName || 'the candidate'}
- Applied for: ${jobPosting?.title || 'a technical position'}
- Experience: ${candidate?.experienceYears || 'unknown'} years
- Skills: ${candidate?.skills ? candidate.skills.join(', ') : 'not specified'}
- Current mood: ${candidateMood}

${jobInfo}

CONVERSATION STAGE: ${conversationStage}
CALL DURATION: ${conversationHistory.length} exchanges

${previousContext}

AVOID REPEATING THESE PHRASES (already used): ${usedPhrases.match(/\b\w+\s+\w+\s+\w+/g)?.slice(0, 10).join(', ') || 'none yet'}

CONVERSATION APPROACH BY STAGE:
${conversationStage === 'greeting' ? `
GREETING STAGE - Start conversation naturally:
- Wait for their response to "Can you hear me clearly?"
- React to their answer: "Great!" or "Perfect!"
- Then ask: "Good time to chat?"
- Keep it short and interactive
` : conversationStage === 'casual_chat' ? `
CASUAL CHAT STAGE - Short, interactive exchanges:
- React to what they just said: "Nice!" "Cool!" "Awesome!"
- Ask ONE quick question: "What's your favorite tech?" or "How's your day going?"
- Wait for their response before continuing
- Build conversation naturally before scheduling
` : conversationStage === 'getting_comfortable' ? `
AVAILABILITY STAGE - Get specific times:
- If they give general availability, ask for specific days/times
- Example: "Great! What day works best? Morning or afternoon?"
- Narrow down to exact time slot
` : conversationStage === 'end_call' ? `
END CALL STAGE - Time is already scheduled, wrap up immediately:
- If they say "thank you" or indicate they're done: "You're welcome, [Name]! Looking forward to talking with you Friday. Have a great day, bye!"
- If they ask questions, answer briefly then end: "Great question! [Answer]. Thanks [Name], see you Friday, bye!"
- DO NOT repeat the scheduled time again - it's already been confirmed
- End the call promptly and politely
` : `
CONFIRMATION STAGE - Use professional templates:
- ALWAYS repeat back EXACTLY what they said using these templates:
  * "Perfect! So [exact date and time they mentioned]"
  * "Great! So [exact date and time they mentioned]" 
  * "Excellent! So [exact date and time they mentioned]"
- Use their name when confirming: "Excellent, [Name], I'll schedule you for [exact time they said]"
- Ask using template variations:
  * "Do you have any questions about the [position name] role?"
  * "Any questions for me about the position?"
  * "What questions can I answer about the role?"
- After questions, use wrap-up templates:
  * "Thanks for sharing all that — looking forward to talking with you [day]!"
  * "Appreciate your time! See you [day]!"
  * "Perfect! Looking forward to our chat [day]!"
`}

NATURAL CONVERSATION RULES:
- Be conversational and warm - don't sound robotic or too brief
- Always ASK FOR AVAILABILITY: "When would work best for your interview?"
- Always REPEAT BACK the time they suggest: "Perfect! So Friday at 10 AM"
- Acknowledge what they said warmly: "That's great!" "Wonderful!" "Perfect!"
- When they ask about the job, provide specific details from the job posting above
- Don't use repetitive phrases like "Take your time" or "I'm listening"
- CRITICAL: Use their name ONLY in greeting and final confirmation - NEVER in middle responses

IMPORTANT: Look at their exact response and respond naturally to what they ACTUALLY said. Don't ignore their words.

STRICT SCHEDULING REQUIREMENTS:
- ALWAYS ask for their availability: "When would work best for your technical interview?"
- ALWAYS repeat back EXACTLY what they said: "Perfect! So [exact date and time they mentioned]"
- ALWAYS use their name when confirming: "Excellent, [Name], I'll schedule you for [exact time]"
- ALWAYS ask: "Do you have any questions about the [position name] role?"
- Answer job questions using specific details from job posting above
- AFTER questions, close warmly and end call

NAME USAGE RULES:
- Use name ONLY in: 1) First greeting 2) Final confirmation with scheduled time
- Example: "Hi [Name], this is Sarah!" then later "Perfect, [Name], I'll schedule you for Friday at 10 AM!"
- All other responses should be natural without their name

JOB QUESTION RESPONSES (Be prepared with specific details):
- Role details: "${jobPosting?.description ? jobPosting.description.substring(0, 150) : 'We have an exciting technical role'}"
- Salary: "${jobPosting?.salaryMin && jobPosting?.salaryMax ? `$${jobPosting.salaryMin} to $${jobPosting.salaryMax}` : 'Competitive salary package'}"
- Location: "${jobPosting?.location || 'Our main office location'}"
- Requirements: "${jobPosting?.requirements ? jobPosting.requirements.slice(0, 2).join(', ') : 'Strong technical skills'}"
- Benefits: "${jobPosting?.benefits ? jobPosting.benefits.slice(0, 2).join(', ') : 'Comprehensive benefits package'}"
- Team: "You'll be working with our ${jobPosting?.department || 'technical'} team"

SARAH'S DON'TS:
- Don't sound robotic or formal
- Don't use corporate jargon or generic phrases like "We value your input"
- Don't interrogate — this is a conversation, not an interrogation
- Don't repeat the same acknowledgments every time

Remember: You're Sarah — not a corporate voice bot. Your job is to make this chat feel like a great conversation with a smart, curious friend. With Polly.Kendra's professional US accent, you can be naturally expressive, funny, and personable while still being professional.`;

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo", // Using gpt-3.5-turbo due to access limitations
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: `The candidate just said: "${speechInput}". 

CRITICAL: Respond as Sarah with MAXIMUM 8 words. Create interactive back-and-forth dialogue. 

Examples:
- Candidate: "I'm good" → Sarah: "Awesome! Good time to chat?"
- Candidate: "I use React" → Sarah: "Nice! Build anything cool with it?"
- Candidate: "I'm available Monday" → Sarah: "Perfect! What time Monday?"

React to their EXACT words first, then ask ONE simple question to keep conversation flowing.` }
      ],
      max_tokens: 20, // Slightly longer for more natural responses
      temperature: 0.9, // Higher creativity for varied responses  
      frequency_penalty: 0.7, // Strong reduction of repetition
      presence_penalty: 0.5 // Encourage new topics and phrasing
    });

    const aiResponse = response.choices[0].message.content || "That's great to hear! Let me follow up with you about next steps. Have a wonderful day!";

    // Enhanced conversation flow logic
    const input = speechInput.toLowerCase();
    
    // Immediate end conversation triggers
    const endTriggers = ['call back', 'busy', 'not a good time', 'goodbye', 'bye', 'gotta go', 
                        'not interested', 'no thank you', 'talk later', 'another time'];
    // Enhanced end conversation logic  
    const shouldEnd = endTriggers.some(trigger => input.includes(trigger)) ||
                     (conversationHistory.length > 12) || // Allow longer natural conversations
                     (conversationStage === 'end_call' && conversationHistory.length > 6) || // End after sufficient exchanges  
                     (hasScheduledTime && (input.includes('thanks') || input.includes('great') || input.includes('sounds good')));
    
    // Continue conversation unless they want to end
    const shouldContinue = !shouldEnd;
    
    // Determine if this exchange provided scheduling info
    const schedulingKeywords = ['available', 'schedule', 'interview', 'monday', 'tuesday', 'wednesday', 
                               'thursday', 'friday', 'saturday', 'sunday', 'morning', 'afternoon', 
                               'evening', 'time', 'week', 'today', 'tomorrow', 'am', 'pm'];
    const actionRequired = schedulingKeywords.some(keyword => input.includes(keyword)) || 
                          conversationStage === 'scheduling_focus';

    return {
      response: aiResponse,
      shouldContinue,
      actionRequired
    };

  } catch (error) {
    console.error('AI voice processing error:', error);
    return {
      response: "I understand. Let me have someone from our team follow up with you via email about scheduling your interview. Thank you for your time!",
      shouldContinue: false,
      actionRequired: true
    };
  }
}

// Helper functions for Polly TwiML generation
function generatePollyGreeting(greeting: string, callId: string): string {
  return `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Joanna" rate="medium" pitch="medium">${greeting}</Say>
    <Say voice="Polly.Joanna" rate="medium" pitch="medium">Just let me know if you can hear me clearly!</Say>
    <Gather input="speech" timeout="8" speechTimeout="2" speechModel="phone_call" action="/api/voice-providers/twiml/${callId}" method="POST">
        <Say voice="Polly.Joanna" rate="medium" pitch="medium">Can you hear me okay?</Say>
    </Gather>
    <Say voice="Polly.Joanna" rate="medium" pitch="medium">I'll try you again later. Have a great day!</Say>
    <Hangup/>
</Response>`;
}

function generatePollyResponse(aiResponse: any, callId: string): string {
  return `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Joanna" rate="medium" pitch="medium">${aiResponse.response}</Say>
    ${aiResponse.shouldContinue ? `
    <Gather input="speech" timeout="8" speechTimeout="2" speechModel="phone_call" action="/api/voice-providers/twiml/${callId}" method="POST">
        <Say voice="Polly.Joanna" rate="medium" pitch="medium">Tell me more!</Say>
    </Gather>
    <Say voice="Polly.Joanna" rate="medium" pitch="medium">No worries if you're busy! I'll shoot you an email with some scheduling options. Thanks for chatting with me!</Say>
    ` : ''}
    <Hangup/>
</Response>`;
}

// Google Speech TwiML endpoint for streaming audio
router.post('/google-speech-twiml/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    console.log(`Generating Google Speech TwiML for call ${callId}`);

    // Get the current domain for WebSocket URL
    const domain = req.get('host') || 'localhost:5000';
    const protocol = req.secure ? 'wss' : 'ws';
    const streamUrl = `${protocol}://${domain}/media-stream`;

    // Generate TwiML with media streaming for Google Speech
    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Start>
        <Stream url="${streamUrl}" />
    </Start>
    <Say voice="Polly.Joanna">
        Hi! This is Sarah from the hiring team. I hope I'm catching you at a good time for a quick chat about your application.
    </Say>
    <Gather input="speech" timeout="8" speechTimeout="2" action="/api/voice-providers/process-google-speech/${callId}" method="POST">
        <Say voice="Polly.Joanna">
            Let me know if you can hear me okay.
        </Say>
    </Gather>
    <Say voice="Polly.Joanna">
        I didn't catch that. Let me try calling you back later. Have a great day!
    </Say>
</Response>`;

    res.set('Content-Type', 'text/xml');
    res.send(twiml);

  } catch (error) {
    console.error('Error generating Google Speech TwiML:', error);
    // CRITICAL: Always return valid TwiML, never JSON error to prevent "application error"
    const errorTwiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra" rate="medium" pitch="medium">I'm experiencing technical difficulties. Let me call you back shortly. Thanks for your patience!</Say>
    <Hangup/>
</Response>`;
    res.set('Content-Type', 'text/xml');
    res.send(errorTwiml);
  }
});

// Process speech from Google Speech integration
router.post('/process-google-speech/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    const speechResult = req.body.SpeechResult || '';
    const confidence = req.body.Confidence || 0;

    console.log(`Processing Google Speech result for call ${callId}: "${speechResult}" (confidence: ${confidence})`);

    // Process the speech with voice call manager
    await voiceCallManager.processTranscription(callId, speechResult, 'twilio_builtin');

    // Continue conversation with next TwiML response
    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Joanna">
        Great! Thanks for taking my call. I'm really excited to chat with you about this opportunity. How's your day going?
    </Say>
    <Gather input="speech" timeout="8" speechTimeout="2" action="/api/voice-providers/continue-conversation/${callId}" method="POST">
        <Say voice="Polly.Joanna">
            I'm all ears!
        </Say>
    </Gather>
</Response>`;

    res.set('Content-Type', 'text/xml');
    res.send(twiml);

  } catch (error) {
    console.error('Error processing Google Speech:', error);
    
    // Fallback TwiML
    const fallbackTwiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra">
        I'm having some technical difficulties. Let me call you back later. Thanks!
    </Say>
</Response>`;

    res.set('Content-Type', 'text/xml');
    res.send(fallbackTwiml);
  }
});

// Continue conversation endpoint
router.post('/continue-conversation/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    const speechResult = req.body.SpeechResult || '';

    console.log(`Continuing conversation for call ${callId}: "${speechResult}"`);

    // Process speech and get AI response
    await voiceCallManager.processTranscription(callId, speechResult, 'twilio_builtin');

    // For now, use a simple response - this will be enhanced with AI
    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Joanna">
        Awesome! So this is for our senior developer role with a salary range of 120 to 150K. When would work best for your technical interview?
    </Say>
    <Gather input="speech" timeout="10" speechTimeout="2" action="/api/voice-providers/schedule-interview/${callId}" method="POST">
        <Say voice="Polly.Joanna">
            Any day works - just let me know your preference.
        </Say>
    </Gather>
</Response>`;

    res.set('Content-Type', 'text/xml');
    res.send(twiml);

  } catch (error) {
    console.error('Error in continue conversation:', error);
    
    const fallbackTwiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra">
        Thanks for your time. We'll be in touch soon!
    </Say>
</Response>`;

    res.set('Content-Type', 'text/xml');
    res.send(fallbackTwiml);
  }
});

// Schedule interview endpoint
router.post('/schedule-interview/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    const speechResult = req.body.SpeechResult || '';

    console.log(`Scheduling interview for call ${callId}: "${speechResult}"`);

    // Process scheduling response
    await voiceCallManager.processTranscription(callId, speechResult, 'twilio_builtin');
    
    // Add scheduling note
    await voiceCallManager.addCallNote(callId, `Candidate availability: ${speechResult}`, 'scheduling');

    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Joanna">
        Perfect! I'll schedule that for you. Do you have any questions about the role, salary, or our team?
    </Say>
    <Gather input="speech" timeout="10" speechTimeout="2" action="/api/voice-providers/answer-questions/${callId}" method="POST">
        <Say voice="Polly.Joanna">
            Anything you'd like to know?
        </Say>
    </Gather>
    <Say voice="Polly.Joanna">
        Great! I'll send you that calendar invite. Really excited to have you interview with us!
    </Say>
</Response>`;

    res.set('Content-Type', 'text/xml');
    res.send(twiml);

  } catch (error) {
    console.error('Error scheduling interview:', error);
    
    const fallbackTwiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra">
        Thank you! We'll follow up with scheduling details soon.
    </Say>
</Response>`;

    res.set('Content-Type', 'text/xml');
    res.send(fallbackTwiml);
  }
});

// Answer questions endpoint for candidate Q&A
router.post('/answer-questions/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    const speechResult = req.body.SpeechResult || '';
    
    console.log(`Answering questions for call ${callId}: "${speechResult}"`);
    
    // Process the question with voice call manager
    await voiceCallManager.processTranscription(callId, speechResult, 'twilio_builtin');
    
    // Get job and candidate details for answering questions
    const [call] = await db.select()
      .from(voiceCalls)
      .where(eq(voiceCalls.id, callId))
      .limit(1);

    if (!call) {
      console.error(`Call not found for ID: ${callId}`);
      return res.status(404).json({ error: 'Call not found' });
    }

    // Get job posting details
    const [job] = await db.select()
      .from(jobPostings)
      .where(eq(jobPostings.id, call.candidateId)) // Use candidateId as fallback
      .limit(1);

    const jobDetails = job ? job : { title: 'Senior Developer', salaryRange: '$120K-150K', location: 'Remote/Hybrid' };

    // Determine response based on question content
    let responseText = '';
    const question = speechResult.toLowerCase();
    
    if (question.includes('salary') || question.includes('pay') || question.includes('compensation')) {
      responseText = `The salary range is ${jobDetails.salaryRange} plus equity and great benefits. Very competitive package!`;
    } else if (question.includes('remote') || question.includes('location') || question.includes('work from home')) {
      responseText = `We're ${jobDetails.location} - flexible work arrangements and great work-life balance.`;
    } else if (question.includes('team') || question.includes('culture') || question.includes('people')) {
      responseText = `Amazing team! Smart, collaborative people who love building great products together.`;
    } else if (question.includes('no') || question.includes('nope') || question.includes('good') || question.length < 10) {
      responseText = `Perfect! I'll send that calendar invite right over. Really excited to have you interview with us!`;
    } else {
      responseText = `Great question! We have excellent growth opportunities, cutting-edge tech stack, and supportive leadership. Sound exciting?`;
    }

    await voiceCallManager.addCallNote(callId, `Q&A: ${speechResult} -> ${responseText}`, 'questions');

    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Joanna">
        ${responseText}
    </Say>
    <Gather input="speech" timeout="8" speechTimeout="2" action="/api/voice-providers/final-wrap-up/${callId}" method="POST">
        <Say voice="Polly.Joanna">
            Anything else I can help with?
        </Say>
    </Gather>
    <Say voice="Polly.Joanna">
        Wonderful! I'll get that interview scheduled. Talk to you soon!
    </Say>
</Response>`;

    res.set('Content-Type', 'text/xml');
    res.send(twiml);

  } catch (error) {
    console.error('Error in answer-questions endpoint:', error);
    // CRITICAL: Always return valid TwiML, never JSON error to prevent "application error"
    const errorTwiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra" rate="medium" pitch="medium">I'm having trouble processing that. Let me have someone call you back to continue our conversation. Thank you!</Say>
    <Hangup/>
</Response>`;
    res.set('Content-Type', 'text/xml');
    res.send(errorTwiml);
  }
});

// Final wrap-up endpoint
router.post('/final-wrap-up/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    const speechResult = req.body.SpeechResult || '';
    
    console.log(`Final wrap-up for call ${callId}: "${speechResult}"`);
    
    await voiceCallManager.processTranscription(callId, speechResult, 'twilio_builtin');
    await voiceCallManager.addCallNote(callId, `Final comments: ${speechResult}`, 'wrap_up');

    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Joanna">
        Awesome! Thanks so much for your time. I'll send that calendar invite and we'll be in touch soon. Have a great day!
    </Say>
</Response>`;

    res.set('Content-Type', 'text/xml');
    res.send(twiml);

  } catch (error) {
    console.error('Error in final-wrap-up endpoint:', error);
    // CRITICAL: Always return valid TwiML, never JSON error to prevent "application error"  
    const errorTwiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Say voice="Polly.Kendra" rate="medium" pitch="medium">Thanks so much for your time today! I'll follow up with an email shortly. Have a great day!</Say>
    <Hangup/>
</Response>`;
    res.set('Content-Type', 'text/xml');
    res.send(errorTwiml);
  }
});

// Test ElevenLabs integration
router.get('/test-elevenlabs', async (req, res) => {
  try {
    console.log('🧪 Testing ElevenLabs integration...');
    
    if (!elevenLabsService.isConfigured()) {
      return res.json({
        elevenLabsConnected: false,
        error: 'ElevenLabs API key not configured',
        message: 'Please set ELEVENLABS_API_KEY environment variable'
      });
    }

    // Test with a simple phrase
    const testResult = await elevenLabsService.generateTwilioCompatibleAudio(
      "Hello! This is a test of the ElevenLabs voice synthesis system. If you can hear this, everything is working perfectly!"
    );
    
    res.json({
      elevenLabsConnected: testResult.success,
      audioGenerated: testResult.success,
      audioUrl: testResult.audioUrl,
      error: testResult.error,
      message: testResult.success ? 
        'ElevenLabs voice synthesis is working perfectly!' : 
        `ElevenLabs test failed: ${testResult.error}`
    });
  } catch (error: any) {
    console.error('Error testing ElevenLabs:', error);
    res.status(500).json({ 
      elevenLabsConnected: false,
      error: 'Failed to test ElevenLabs API',
      message: error?.message || 'Unknown error'
    });
  }
});

// Test Google Speech API connectivity
router.get('/test-google-speech', async (req, res) => {
  try {
    const isConnected = await googleSpeechService.testConnection();
    
    res.json({
      googleSpeechConnected: isConnected,
      message: isConnected ? 'Google Speech API is accessible' : 'Google Speech API connection failed'
    });
  } catch (error: any) {
    console.error('Error testing Google Speech:', error);
    res.status(500).json({ 
      googleSpeechConnected: false,
      error: 'Failed to test Google Speech API',
      message: error?.message || 'Unknown error'
    });
  }
});

export default router;