import request from 'supertest';
import { app } from '../../../server/app';
import jwt from 'jsonwebtoken';

// Mock database
jest.mock('../../../server/db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    leftJoin: jest.fn().mockReturnThis(),
    limit: jest.fn().mockResolvedValue([]),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    onConflictDoUpdate: jest.fn().mockResolvedValue({ insertId: 'test-id' }),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockResolvedValue({ affectedRows: 1 }),
    delete: jest.fn().mockReturnThis()
  }
}));

describe('Interviews V2 API', () => {
  let authToken: string;
  let mockUser: any;

  beforeAll(() => {
    mockUser = global.testUtils.createMockUser();
    authToken = jwt.sign(
      { id: mockUser.id, email: mockUser.email },
      process.env.JWT_SECRET || 'test-secret'
    );
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/interviews-v2', () => {
    it('should create a new interview', async () => {
      const interviewData = {
        candidateId: 'test-candidate-id',
        role: 'Software Engineer',
        scheduledAt: new Date().toISOString(),
        duration: 60,
        agentProfileId: 'test-agent-profile-id'
      };

      const response = await request(app)
        .post('/api/interviews-v2')
        .set('Authorization', `Bearer ${authToken}`)
        .send(interviewData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.interview).toBeDefined();
    });

    it('should require authentication', async () => {
      const interviewData = {
        candidateId: 'test-candidate-id',
        role: 'Software Engineer'
      };

      await request(app)
        .post('/api/interviews-v2')
        .send(interviewData)
        .expect(401);
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/interviews-v2')
        .set('Authorization', `Bearer ${authToken}`)
        .send({})
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('required');
    });
  });

  describe('GET /api/interviews-v2', () => {
    it('should list interviews for authenticated user', async () => {
      const response = await request(app)
        .get('/api/interviews-v2')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.interviews).toBeInstanceOf(Array);
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/interviews-v2?page=1&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.pagination).toBeDefined();
    });

    it('should support filtering by status', async () => {
      const response = await request(app)
        .get('/api/interviews-v2?status=scheduled')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('GET /api/interviews-v2/:id', () => {
    it('should get interview by ID', async () => {
      const interviewId = 'test-interview-id';

      const response = await request(app)
        .get(`/api/interviews-v2/${interviewId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.interview).toBeDefined();
    });

    it('should return 404 for non-existent interview', async () => {
      const response = await request(app)
        .get('/api/interviews-v2/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /api/interviews-v2/:id', () => {
    it('should update interview', async () => {
      const interviewId = 'test-interview-id';
      const updateData = {
        role: 'Senior Software Engineer',
        duration: 90
      };

      const response = await request(app)
        .put(`/api/interviews-v2/${interviewId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should validate update data', async () => {
      const interviewId = 'test-interview-id';
      const invalidData = {
        duration: 'invalid-duration'
      };

      const response = await request(app)
        .put(`/api/interviews-v2/${interviewId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/interviews-v2/:id/send-invitation', () => {
    it('should send interview invitation', async () => {
      const interviewId = 'test-interview-id';

      const response = await request(app)
        .post(`/api/interviews-v2/${interviewId}/send-invitation`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('invitation sent');
    });

    it('should require scheduled interview', async () => {
      const interviewId = 'unscheduled-interview-id';

      const response = await request(app)
        .post(`/api/interviews-v2/${interviewId}/send-invitation`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('scheduled');
    });
  });

  describe('POST /api/interviews-v2/:id/cancel', () => {
    it('should cancel interview', async () => {
      const interviewId = 'test-interview-id';
      const cancelData = {
        reason: 'Candidate unavailable'
      };

      const response = await request(app)
        .post(`/api/interviews-v2/${interviewId}/cancel`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(cancelData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('cancelled');
    });

    it('should send cancellation notification', async () => {
      const interviewId = 'test-interview-id';

      await request(app)
        .post(`/api/interviews-v2/${interviewId}/cancel`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ reason: 'Test cancellation' })
        .expect(200);

      // Verify that the invitation service was called
      const { interviewInvitationService } = require('../../../server/services/interviewInvitationService');
      expect(interviewInvitationService.sendCancellationNotification).toHaveBeenCalled();
    });
  });

  describe('DELETE /api/interviews-v2/:id', () => {
    it('should delete interview', async () => {
      const interviewId = 'test-interview-id';

      const response = await request(app)
        .delete(`/api/interviews-v2/${interviewId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should prevent deletion of active interviews', async () => {
      const interviewId = 'active-interview-id';

      const response = await request(app)
        .delete(`/api/interviews-v2/${interviewId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('active');
    });
  });

  describe('Error handling', () => {
    it('should handle database errors gracefully', async () => {
      // Mock database error
      const { db } = require('../../../server/db');
      db.select.mockRejectedValueOnce(new Error('Database connection failed'));

      const response = await request(app)
        .get('/api/interviews-v2')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });

    it('should handle invalid JWT tokens', async () => {
      const invalidToken = 'invalid.jwt.token';

      await request(app)
        .get('/api/interviews-v2')
        .set('Authorization', `Bearer ${invalidToken}`)
        .expect(401);
    });

    it('should handle malformed request bodies', async () => {
      const response = await request(app)
        .post('/api/interviews-v2')
        .set('Authorization', `Bearer ${authToken}`)
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });
});
