import { Router } from 'express';
import { authenticateToken } from '../auth';
import { botRunnerService } from '../services/botRunnerService';
import { db } from '../db';
import { interviewsV2, authUsers } from '@shared/schema';
import { eq, and } from 'drizzle-orm';

const router = Router();

/**
 * @swagger
 * /bot-runner/start:
 *   post:
 *     summary: Start bot session for interview
 *     description: Manually start a bot session for an interview
 *     tags: [Bot Runner]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - interviewId
 *             properties:
 *               interviewId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Bot session started successfully
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Interview not found
 *       500:
 *         description: Failed to start bot session
 */
router.post('/start', authenticateToken, async (req, res) => {
  try {
    const { interviewId } = req.body;
    const userId = req.user?.id;

    if (!interviewId) {
      return res.status(400).json({
        success: false,
        error: 'Interview ID is required'
      });
    }

    // Verify interview exists and user has access
    const [interview] = await db
      .select()
      .from(interviewsV2)
      .where(eq(interviewsV2.id, interviewId))
      .limit(1);

    if (!interview) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    // Verify user belongs to same organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || user.organizationId !== interview.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Start bot session
    const sessionId = await botRunnerService.startBotSession(interviewId);

    res.json({
      success: true,
      sessionId,
      message: 'Bot session started successfully'
    });

  } catch (error) {
    console.error('Error starting bot session:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to start bot session'
    });
  }
});

/**
 * @swagger
 * /bot-runner/start/{interviewId}:
 *   post:
 *     summary: Start bot session for interview (URL param)
 *     description: Manually start a bot session for an interview using URL parameter
 *     tags: [Bot Runner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: interviewId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Bot session started successfully
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Interview not found
 *       500:
 *         description: Failed to start bot session
 */
router.post('/start/:interviewId', authenticateToken, async (req, res) => {
  try {
    const { interviewId } = req.params;
    const userId = req.user?.id;

    console.log('🤖 Manual bot start requested for interview:', interviewId);

    if (!interviewId) {
      return res.status(400).json({
        success: false,
        error: 'Interview ID is required'
      });
    }

    // Verify interview exists and user has access
    const [interview] = await db
      .select()
      .from(interviewsV2)
      .where(eq(interviewsV2.id, interviewId))
      .limit(1);

    if (!interview) {
      console.log('❌ Interview not found:', interviewId);
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    // Verify user belongs to same organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || user.organizationId !== interview.organizationId) {
      console.log('❌ Access denied for user:', userId, 'interview org:', interview.organizationId);
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    console.log('✅ Starting bot session for interview:', interviewId);
    // Start bot session
    const sessionId = await botRunnerService.startBotSession(interviewId);

    console.log('✅ Bot session started successfully:', sessionId);
    res.json({
      success: true,
      sessionId,
      message: 'AI Agent has joined the interview session'
    });

  } catch (error) {
    console.error('❌ Error starting bot session:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to start AI Agent: ' + error.message
    });
  }
});

/**
 * @swagger
 * /bot-runner/stop:
 *   post:
 *     summary: Stop bot session
 *     description: Manually stop a running bot session
 *     tags: [Bot Runner]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *             properties:
 *               sessionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Bot session stopped successfully
 *       404:
 *         description: Session not found
 *       500:
 *         description: Failed to stop bot session
 */
router.post('/stop', authenticateToken, async (req, res) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Session ID is required'
      });
    }

    const session = botRunnerService.getBotSession(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Bot session not found'
      });
    }

    // Verify user has access to this interview
    const userId = req.user?.id;
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    const [interview] = await db
      .select()
      .from(interviewsV2)
      .where(eq(interviewsV2.id, session.interviewId))
      .limit(1);

    if (!user || !interview || user.organizationId !== interview.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Stop bot session
    await botRunnerService.endBotSession(sessionId);

    res.json({
      success: true,
      message: 'Bot session stopped successfully'
    });

  } catch (error) {
    console.error('Error stopping bot session:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to stop bot session'
    });
  }
});

/**
 * @swagger
 * /bot-runner/sessions:
 *   get:
 *     summary: Get active bot sessions
 *     description: List all active bot sessions
 *     tags: [Bot Runner]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Active sessions retrieved successfully
 */
router.get('/sessions', authenticateToken, async (req, res) => {
  try {
    const sessions = botRunnerService.getActiveSessions();
    
    // Filter sessions by user's organization
    const userId = req.user?.id;
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Get interviews for this organization to filter sessions
    const interviews = await db
      .select()
      .from(interviewsV2)
      .where(eq(interviewsV2.organizationId, user.organizationId));

    const interviewIds = new Set(interviews.map(i => i.id));
    
    const filteredSessions = sessions
      .filter(session => interviewIds.has(session.interviewId))
      .map(session => ({
        sessionId: session.sessionId,
        interviewId: session.interviewId,
        status: session.status,
        startedAt: session.startedAt,
        zoomSessionName: session.zoomSessionName
      }));

    res.json({
      success: true,
      sessions: filteredSessions
    });

  } catch (error) {
    console.error('Error fetching bot sessions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch bot sessions'
    });
  }
});

/**
 * @swagger
 * /bot-runner/schedule:
 *   post:
 *     summary: Schedule bot auto-join
 *     description: Schedule bot to automatically join interview at specified time
 *     tags: [Bot Runner]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - interviewId
 *               - scheduledAt
 *             properties:
 *               interviewId:
 *                 type: string
 *                 format: uuid
 *               scheduledAt:
 *                 type: string
 *                 format: date-time
 *     responses:
 *       200:
 *         description: Bot auto-join scheduled successfully
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Interview not found
 */
router.post('/schedule', authenticateToken, async (req, res) => {
  try {
    const { interviewId, scheduledAt } = req.body;
    const userId = req.user?.id;

    if (!interviewId || !scheduledAt) {
      return res.status(400).json({
        success: false,
        error: 'Interview ID and scheduled time are required'
      });
    }

    // Verify interview exists and user has access
    const [interview] = await db
      .select()
      .from(interviewsV2)
      .where(eq(interviewsV2.id, interviewId))
      .limit(1);

    if (!interview) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    // Verify user belongs to same organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || user.organizationId !== interview.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Schedule auto-join
    const scheduledTime = new Date(scheduledAt);
    await botRunnerService.scheduleAutoJoin(interviewId, scheduledTime);

    res.json({
      success: true,
      message: 'Bot auto-join scheduled successfully',
      scheduledAt: scheduledTime.toISOString()
    });

  } catch (error) {
    console.error('Error scheduling bot auto-join:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to schedule bot auto-join'
    });
  }
});

/**
 * @swagger
 * /bot-runner/status/{sessionId}:
 *   get:
 *     summary: Get bot session status
 *     description: Get detailed status of a specific bot session
 *     tags: [Bot Runner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Session status retrieved successfully
 *       404:
 *         description: Session not found
 */
router.get('/status/:sessionId', authenticateToken, async (req, res) => {
  try {
    const { sessionId } = req.params;
    
    const session = botRunnerService.getBotSession(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Bot session not found'
      });
    }

    // Verify user has access
    const userId = req.user?.id;
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    const [interview] = await db
      .select()
      .from(interviewsV2)
      .where(eq(interviewsV2.id, session.interviewId))
      .limit(1);

    if (!user || !interview || user.organizationId !== interview.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    res.json({
      success: true,
      session: {
        sessionId: session.sessionId,
        interviewId: session.interviewId,
        status: session.status,
        startedAt: session.startedAt,
        zoomSessionName: session.zoomSessionName,
        elevenlabsConversationId: session.elevenlabsConversationId
      }
    });

  } catch (error) {
    console.error('Error getting bot session status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get session status'
    });
  }
});

export default router;
