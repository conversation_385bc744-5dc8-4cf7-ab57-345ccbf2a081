
import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { FileText, Calendar, Users, UserPlus, Bot, Zap, Brain, Clock, LogOut, User } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import EnhancedResumeScreeningAgent from "@/components/EnhancedResumeScreeningAgent";
import CandidateInteractionAgent from "@/components/CandidateInteractionAgent";
import JobPostingAgent from "@/components/JobPostingAgent";
import OnboardingAgent from "@/components/OnboardingAgent";
import { CandidateProvider, useCandidateContext } from "@/contexts/CandidateContext";

const IndexContent = () => {
  const { user, logout, loading } = useAuth();
  const navigate = useNavigate();
  const { approvedCandidates } = useCandidateContext();

  React.useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <div className="flex items-center space-x-2">
          <Clock className="w-6 h-6 animate-spin text-blue-600" />
          <span className="text-lg">Loading...</span>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to auth
  }

  const handleSignOut = async () => {
    logout();
    navigate('/auth');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Bot className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">HR Workflow AI</h1>
                <p className="text-sm text-gray-500">Intelligent HR Automation Platform</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  <Zap className="w-3 h-3 mr-1" />
                  AI Powered
                </Badge>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  <Brain className="w-3 h-3 mr-1" />
                  OpenAI Integrated
                </Badge>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <User className="w-4 h-4" />
                <span>{user.email}</span>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleSignOut}
                className="flex items-center space-x-2"
              >
                <LogOut className="w-4 h-4" />
                <span>Sign Out</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Resume Screening
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-blue-100 text-sm">AI-powered resume analysis with job matching</p>
              <div className="mt-2 text-2xl font-bold">47</div>
              <p className="text-xs text-blue-200">Resumes processed today</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Scheduling
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-green-100 text-sm">Automated interview scheduling and coordination</p>
              <div className="mt-2 text-2xl font-bold">{approvedCandidates.length}</div>
              <p className="text-xs text-green-200">Candidates awaiting scheduling</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Job Sourcing
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-purple-100 text-sm">Smart job posting and candidate sourcing</p>
              <div className="mt-2 text-2xl font-bold">12</div>
              <p className="text-xs text-purple-200">Active job postings</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <UserPlus className="w-5 h-5 mr-2" />
                Onboarding
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-orange-100 text-sm">Automated onboarding with Workday integration</p>
              <div className="mt-2 text-2xl font-bold">8</div>
              <p className="text-xs text-orange-200">New hires this month</p>
            </CardContent>
          </Card>
        </div>

        {/* AI Agents Tabs */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center text-2xl">
              <Bot className="w-6 h-6 mr-3 text-blue-600" />
              AI Workflow Agents
            </CardTitle>
            <CardDescription>
              Intelligent automation agents powered by OpenAI to streamline your HR processes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="resume-screening" className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-6">
                <TabsTrigger value="resume-screening" className="flex items-center space-x-2">
                  <FileText className="w-4 h-4" />
                  <span>Resume Screening</span>
                </TabsTrigger>
                <TabsTrigger value="candidate-interaction" className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>Scheduling</span>
                  {approvedCandidates.length > 0 && (
                    <Badge variant="secondary" className="ml-1 bg-red-500 text-white text-xs">
                      {approvedCandidates.length}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="job-posting" className="flex items-center space-x-2">
                  <Users className="w-4 h-4" />
                  <span>Job Sourcing</span>
                </TabsTrigger>
                <TabsTrigger value="onboarding" className="flex items-center space-x-2">
                  <UserPlus className="w-4 h-4" />
                  <span>Onboarding</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="resume-screening">
                <EnhancedResumeScreeningAgent />
              </TabsContent>

              <TabsContent value="candidate-interaction">
                <CandidateInteractionAgent approvedCandidates={approvedCandidates} />
              </TabsContent>

              <TabsContent value="job-posting">
                <JobPostingAgent />
              </TabsContent>

              <TabsContent value="onboarding">
                <OnboardingAgent />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

const Index = () => {
  return (
    <CandidateProvider>
      <IndexContent />
    </CandidateProvider>
  );
};

export default Index;
