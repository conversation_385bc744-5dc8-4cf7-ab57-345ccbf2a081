import sgMail from '@sendgrid/mail';

// Initialize SendGrid
if (process.env.SENDGRID_API_KEY) {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
}

interface BusinessValidationEmailData {
  candidateName: string;
  candidateEmail: string;
  errorCode: string;
  errorMessage: string;
  data?: any;
  jobTitle?: string;
  organizationName?: string;
}

const getEmailTemplate = (validationData: BusinessValidationEmailData): { subject: string; html: string; text: string } => {
  const { candidateName, errorCode, errorMessage, data, jobTitle, organizationName } = validationData;

  switch (errorCode) {
    case 'ACTIVE_APPLICATION_EXISTS':
      return {
        subject: `Application Status Update - ${jobTitle || 'Position'}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;">
              <h2 style="color: #856404; margin-top: 0;">Application Update</h2>
              <p>Dear ${candidateName || 'Candidate'},</p>
              
              <p>Thank you for your interest in the <strong>${jobTitle || 'position'}</strong> at ${organizationName || 'our organization'}.</p>
              
              <p>We've received your application, however, our system shows you currently have an active application for another position with us that is being processed.</p>
              
              <div style="background-color: #fff3cd; padding: 15px; border-radius: 4px; margin: 20px 0;">
                <h3 style="color: #856404; margin-top: 0;">Current Application Status:</h3>
                <ul style="margin: 10px 0;">
                  <li><strong>Status:</strong> ${data?.status?.replace('_', ' ').toUpperCase() || 'Under Review'}</li>
                  <li><strong>Applied:</strong> ${data?.appliedAt ? new Date(data.appliedAt).toLocaleDateString() : 'Recently'}</li>
                </ul>
              </div>
              
              <p><strong>What happens next?</strong></p>
              <p>Please wait for your current application to be fully processed before applying to new positions. We'll notify you once a decision has been made on your existing application.</p>
              
              <p>We appreciate your patience and continued interest in joining our team.</p>
              
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              <p style="font-size: 12px; color: #666;">This is an automated message from our application tracking system. If you have questions, please contact our HR department.</p>
            </div>
          </div>
        `,
        text: `
Dear ${candidateName || 'Candidate'},

Thank you for your interest in the ${jobTitle || 'position'} at ${organizationName || 'our organization'}.

We've received your application, however, our system shows you currently have an active application for another position with us that is being processed.

Current Application Status:
- Status: ${data?.status?.replace('_', ' ').toUpperCase() || 'Under Review'}
- Applied: ${data?.appliedAt ? new Date(data.appliedAt).toLocaleDateString() : 'Recently'}

What happens next?
Please wait for your current application to be fully processed before applying to new positions. We'll notify you once a decision has been made on your existing application.

We appreciate your patience and continued interest in joining our team.

This is an automated message from our application tracking system. If you have questions, please contact our HR department.
        `
      };

    case 'SAME_JOB_COOLDOWN_ACTIVE':
      const daysRemaining = data?.canReapplyAt ? Math.ceil((new Date(data.canReapplyAt).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 'several';
      return {
        subject: `Application Resubmission Policy - ${jobTitle || 'Position'}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #dc3545;">
              <h2 style="color: #721c24; margin-top: 0;">Application Resubmission Notice</h2>
              <p>Dear ${candidateName || 'Candidate'},</p>
              
              <p>Thank you for your continued interest in the <strong>${jobTitle || 'position'}</strong> at ${organizationName || 'our organization'}.</p>
              
              <p>Our records show you previously applied for this same position. To ensure fair consideration for all candidates, we have a waiting period policy for reapplications.</p>
              
              <div style="background-color: #f8d7da; padding: 15px; border-radius: 4px; margin: 20px 0;">
                <h3 style="color: #721c24; margin-top: 0;">Reapplication Policy:</h3>
                <ul style="margin: 10px 0;">
                  <li><strong>Previous Application:</strong> ${data?.previousApplicationDate ? new Date(data.previousApplicationDate).toLocaleDateString() : 'Recently submitted'}</li>
                  <li><strong>Earliest Reapplication Date:</strong> ${data?.canReapplyAt ? new Date(data.canReapplyAt).toLocaleDateString() : `${daysRemaining} days from now`}</li>
                  <li><strong>Days Remaining:</strong> ${daysRemaining} days</li>
                </ul>
              </div>
              
              <p><strong>Why do we have this policy?</strong></p>
              <p>This waiting period allows us to thoroughly review all applications and ensures that each candidate receives proper consideration. It also gives you time to gain additional experience and strengthen your application.</p>
              
              <p>We encourage you to reapply after the waiting period. In the meantime, we invite you to explore other positions that might be a great fit for your skills.</p>
              
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              <p style="font-size: 12px; color: #666;">This is an automated message from our application tracking system. If you have questions, please contact our HR department.</p>
            </div>
          </div>
        `,
        text: `
Dear ${candidateName || 'Candidate'},

Thank you for your continued interest in the ${jobTitle || 'position'} at ${organizationName || 'our organization'}.

Our records show you previously applied for this same position. To ensure fair consideration for all candidates, we have a waiting period policy for reapplications.

Reapplication Policy:
- Previous Application: ${data?.previousApplicationDate ? new Date(data.previousApplicationDate).toLocaleDateString() : 'Recently submitted'}
- Earliest Reapplication Date: ${data?.canReapplyAt ? new Date(data.canReapplyAt).toLocaleDateString() : `${daysRemaining} days from now`}
- Days Remaining: ${daysRemaining} days

Why do we have this policy?
This waiting period allows us to thoroughly review all applications and ensures that each candidate receives proper consideration. It also gives you time to gain additional experience and strengthen your application.

We encourage you to reapply after the waiting period. In the meantime, we invite you to explore other positions that might be a great fit for your skills.

This is an automated message from our application tracking system. If you have questions, please contact our HR department.
        `
      };

    case 'CROSS_JOB_COOLDOWN_ACTIVE':
      const crossJobDaysRemaining = data?.canApplyAt ? Math.ceil((new Date(data.canApplyAt).getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 'several';
      return {
        subject: `Application Waiting Period - ${jobTitle || 'Position'}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #fd7e14;">
              <h2 style="color: #95390c; margin-top: 0;">Application Waiting Period</h2>
              <p>Dear ${candidateName || 'Candidate'},</p>
              
              <p>Thank you for your interest in the <strong>${jobTitle || 'position'}</strong> at ${organizationName || 'our organization'}.</p>
              
              <p>Our records show you recently applied for another position with us. To maintain fairness in our hiring process, there is a brief waiting period before applying to additional positions.</p>
              
              <div style="background-color: #fff3cd; padding: 15px; border-radius: 4px; margin: 20px 0;">
                <h3 style="color: #95390c; margin-top: 0;">Application Timeline:</h3>
                <ul style="margin: 10px 0;">
                  <li><strong>Recent Application:</strong> ${data?.previousRejectionDate ? new Date(data.previousRejectionDate).toLocaleDateString() : 'Recently submitted'}</li>
                  <li><strong>Next Application Date:</strong> ${data?.canApplyAt ? new Date(data.canApplyAt).toLocaleDateString() : `${crossJobDaysRemaining} days from now`}</li>
                  <li><strong>Days Remaining:</strong> ${crossJobDaysRemaining} days</li>
                </ul>
              </div>
              
              <p><strong>What this means:</strong></p>
              <p>This brief waiting period ensures that all candidates receive fair consideration and prevents overwhelming our hiring teams. It also gives you time to tailor your application materials for different roles.</p>
              
              <p>We value your interest in our organization and encourage you to apply again after the waiting period.</p>
              
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              <p style="font-size: 12px; color: #666;">This is an automated message from our application tracking system. If you have questions, please contact our HR department.</p>
            </div>
          </div>
        `,
        text: `
Dear ${candidateName || 'Candidate'},

Thank you for your interest in the ${jobTitle || 'position'} at ${organizationName || 'our organization'}.

Our records show you recently applied for another position with us. To maintain fairness in our hiring process, there is a brief waiting period before applying to additional positions.

Application Timeline:
- Recent Application: ${data?.previousRejectionDate ? new Date(data.previousRejectionDate).toLocaleDateString() : 'Recently submitted'}
- Next Application Date: ${data?.canApplyAt ? new Date(data.canApplyAt).toLocaleDateString() : `${crossJobDaysRemaining} days from now`}
- Days Remaining: ${crossJobDaysRemaining} days

What this means:
This brief waiting period ensures that all candidates receive fair consideration and prevents overwhelming our hiring teams. It also gives you time to tailor your application materials for different roles.

We value your interest in our organization and encourage you to apply again after the waiting period.

This is an automated message from our application tracking system. If you have questions, please contact our HR department.
        `
      };

    default:
      return {
        subject: `Application Update - ${jobTitle || 'Position'}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #6c757d;">
              <h2 style="color: #495057; margin-top: 0;">Application Update</h2>
              <p>Dear ${candidateName || 'Candidate'},</p>
              
              <p>Thank you for your interest in the <strong>${jobTitle || 'position'}</strong> at ${organizationName || 'our organization'}.</p>
              
              <p>We've received your application, however, there was an issue with processing it:</p>
              
              <div style="background-color: #e2e3e5; padding: 15px; border-radius: 4px; margin: 20px 0;">
                <p style="margin: 0;"><strong>Issue:</strong> ${errorMessage}</p>
              </div>
              
              <p>Please review your application and try again, or contact our HR department if you need assistance.</p>
              
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              <p style="font-size: 12px; color: #666;">This is an automated message from our application tracking system. If you have questions, please contact our HR department.</p>
            </div>
          </div>
        `,
        text: `
Dear ${candidateName || 'Candidate'},

Thank you for your interest in the ${jobTitle || 'position'} at ${organizationName || 'our organization'}.

We've received your application, however, there was an issue with processing it:

Issue: ${errorMessage}

Please review your application and try again, or contact our HR department if you need assistance.

This is an automated message from our application tracking system. If you have questions, please contact our HR department.
        `
      };
  }
};

export async function sendCandidateValidationFailureEmail(
  validationData: BusinessValidationEmailData
): Promise<boolean> {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      console.log('📧 SendGrid API key not configured, skipping email notification');
      return false;
    }

    const { subject, html, text } = getEmailTemplate(validationData);

    const msg = {
      to: validationData.candidateEmail,
      from: process.env.FROM_EMAIL || '<EMAIL>', // Configure this in your environment
      subject,
      text,
      html,
    };

    await sgMail.send(msg);
    console.log(`📧 Validation failure email sent to ${validationData.candidateEmail} for ${validationData.errorCode}`);
    return true;
  } catch (error) {
    console.error('📧 Failed to send validation failure email:', error);
    return false;
  }
}

export async function sendCandidateApplicationSuccessEmail(
  candidateName: string,
  candidateEmail: string,
  jobTitle: string,
  organizationName?: string
): Promise<boolean> {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      console.log('📧 SendGrid API key not configured, skipping email notification');
      return false;
    }

    const subject = `Application Received - ${jobTitle}`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #333;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
          <h2 style="color: #155724; margin-top: 0;">Application Received Successfully</h2>
          <p>Dear ${candidateName || 'Candidate'},</p>
          
          <p>Thank you for applying to the <strong>${jobTitle}</strong> position at ${organizationName || 'our organization'}.</p>
          
          <div style="background-color: #d4edda; padding: 15px; border-radius: 4px; margin: 20px 0;">
            <h3 style="color: #155724; margin-top: 0;">✓ Application Status: Received</h3>
            <p style="margin: 0;">Your application has been successfully submitted and is now being reviewed by our hiring team.</p>
          </div>
          
          <p><strong>What happens next?</strong></p>
          <ul>
            <li>Our team will review your application and qualifications</li>
            <li>If you're a good fit, we'll contact you within 1-2 weeks</li>
            <li>You'll receive updates on your application status via email</li>
          </ul>
          
          <p>We appreciate your interest in joining our team and look forward to potentially working with you.</p>
          
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          <p style="font-size: 12px; color: #666;">This is an automated message from our application tracking system. If you have questions, please contact our HR department.</p>
        </div>
      </div>
    `;

    const text = `
Dear ${candidateName || 'Candidate'},

Thank you for applying to the ${jobTitle} position at ${organizationName || 'our organization'}.

✓ Application Status: Received

Your application has been successfully submitted and is now being reviewed by our hiring team.

What happens next?
- Our team will review your application and qualifications
- If you're a good fit, we'll contact you within 1-2 weeks
- You'll receive updates on your application status via email

We appreciate your interest in joining our team and look forward to potentially working with you.

This is an automated message from our application tracking system. If you have questions, please contact our HR department.
    `;

    const msg = {
      to: candidateEmail,
      from: process.env.FROM_EMAIL || '<EMAIL>',
      subject,
      text,
      html,
    };

    await sgMail.send(msg);
    console.log(`📧 Application success email sent to ${candidateEmail}`);
    return true;
  } catch (error) {
    console.error('📧 Failed to send application success email:', error);
    return false;
  }
}