import express from 'express';
import { z } from 'zod';
import { db } from '../db';
import { annotations, users } from '@shared/schema';
import { eq, and, desc, asc, isNull } from 'drizzle-orm';
import { authenticateToken } from '../auth';
import type { AuthenticatedRequest } from '../auth';

const router = express.Router();

// Schema for annotation creation
const createAnnotationSchema = z.object({
  content: z.string().min(1),
  type: z.enum(['comment', 'highlight', 'note', 'question', 'approval', 'concern']).default('comment'),
  entityType: z.string(),
  entityId: z.string().uuid(),
  parentId: z.string().uuid().optional(),
  position: z.object({
    x: z.number().optional(),
    y: z.number().optional(),
    selector: z.string().optional(),
    selectedText: z.string().optional(),
  }).optional(),
  mentions: z.array(z.string().uuid()).optional(),
  attachments: z.array(z.string()).optional(),
});

// Schema for annotation updates
const updateAnnotationSchema = z.object({
  content: z.string().min(1).optional(),
  isResolved: z.boolean().optional(),
});

/**
 * @swagger
 * /annotations:
 *   get:
 *     summary: Get annotations for an entity
 *     description: Retrieve annotations for a specific entity (candidate, job posting, etc.)
 *     tags: [Annotations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: entityType
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *         description: Type of entity (candidate, jobPosting, etc.)
 *       - name: entityId
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID of the entity
 *       - name: includeResolved
 *         in: query
 *         schema:
 *           type: string
 *           default: "true"
 *         description: Include resolved annotations
 *     responses:
 *       200:
 *         description: List of annotations
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Annotation'
 *       400:
 *         description: Missing required query parameters
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const { entityType, entityId } = req.query;
    
    if (!entityType || !entityId) {
      return res.status(400).json({ error: 'entityType and entityId are required' });
    }
    const includeResolved = req.query.includeResolved as string || 'true';

    let query = db
      .select({
        id: annotations.id,
        content: annotations.content,
        type: annotations.type,
        entityType: annotations.entityType,
        entityId: annotations.entityId,
        authorId: annotations.authorId,
        parentId: annotations.parentId,
        position: annotations.position,
        metadata: annotations.metadata,
        isResolved: annotations.isResolved,
        resolvedBy: annotations.resolvedBy,
        resolvedAt: annotations.resolvedAt,
        mentions: annotations.mentions,
        attachments: annotations.attachments,
        createdAt: annotations.createdAt,
        updatedAt: annotations.updatedAt,
        authorName: users.fullName,
        authorEmail: users.email,
      })
      .from(annotations)
      .leftJoin(users, eq(annotations.authorId, users.id))
      .where(
        and(
          eq(annotations.entityType, entityType as string),
          eq(annotations.entityId, entityId as string),
          eq(annotations.organizationId, req.user!.organizationId)
        )
      );

    if (includeResolved === 'false') {
      query = query.where(eq(annotations.isResolved, false));
    }

    const result = await query.orderBy(asc(annotations.createdAt));

    // Group by parent/child relationships
    const annotationMap = new Map();
    const rootAnnotations: any[] = [];

    result.forEach(annotation => {
      const annotationWithReplies = {
        ...annotation,
        replies: []
      };
      annotationMap.set(annotation.id, annotationWithReplies);

      if (!annotation.parentId) {
        rootAnnotations.push(annotationWithReplies);
      }
    });

    // Attach replies to parent annotations
    result.forEach(annotation => {
      if (annotation.parentId) {
        const parent = annotationMap.get(annotation.parentId);
        if (parent) {
          parent.replies.push(annotationMap.get(annotation.id));
        }
      }
    });

    res.json(rootAnnotations);
  } catch (error) {
    console.error('Error fetching annotations:', error);
    res.status(500).json({ error: 'Failed to fetch annotations' });
  }
});

// Create annotation (with debug logging)
router.post('/', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const validation = createAnnotationSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({ error: 'Invalid annotation data', details: validation.error.errors });
    }

    const annotationData = validation.data;
    
    console.log('Creating annotation:', annotationData, 'for user:', req.user?.id);

    // Verify parent annotation exists and belongs to same entity (if provided)
    if (annotationData.parentId) {
      const parentAnnotation = await db
        .select()
        .from(annotations)
        .where(
          and(
            eq(annotations.id, annotationData.parentId),
            eq(annotations.entityType, annotationData.entityType),
            eq(annotations.entityId, annotationData.entityId),
            eq(annotations.organizationId, req.user!.organizationId)
          )
        )
        .limit(1);

      if (!parentAnnotation.length) {
        return res.status(404).json({ error: 'Parent annotation not found' });
      }
    }

    const newAnnotation = await db
      .insert(annotations)
      .values({
        content: annotationData.content,
        type: annotationData.type,
        entityType: annotationData.entityType,
        entityId: annotationData.entityId,
        organizationId: req.user!.organizationId,
        authorId: req.user!.id,
        parentId: annotationData.parentId,
        position: annotationData.position,
        mentions: annotationData.mentions,
        attachments: annotationData.attachments,
      })
      .returning();

    // Get the complete annotation with author info
    const completeAnnotation = await db
      .select({
        id: annotations.id,
        content: annotations.content,
        type: annotations.type,
        entityType: annotations.entityType,
        entityId: annotations.entityId,
        authorId: annotations.authorId,
        parentId: annotations.parentId,
        position: annotations.position,
        metadata: annotations.metadata,
        isResolved: annotations.isResolved,
        mentions: annotations.mentions,
        attachments: annotations.attachments,
        createdAt: annotations.createdAt,
        updatedAt: annotations.updatedAt,
        authorName: users.fullName,
        authorEmail: users.email,
      })
      .from(annotations)
      .leftJoin(users, eq(annotations.authorId, users.id))
      .where(eq(annotations.id, newAnnotation[0].id))
      .limit(1);

    // Broadcast to WebSocket clients
    const collaborationWS = req.app.get('collaborationWS');
    if (collaborationWS) {
      collaborationWS.broadcastAnnotationUpdate(
        annotationData.entityType,
        annotationData.entityId,
        completeAnnotation[0],
        'created'
      );
    }

    res.status(201).json(completeAnnotation[0]);
  } catch (error) {
    console.error('Error creating annotation:', error);
    res.status(500).json({ error: 'Failed to create annotation' });
  }
});

// Update annotation
router.put('/:id', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const { id } = req.params;
    const validation = updateAnnotationSchema.safeParse(req.body);
    
    if (!validation.success) {
      return res.status(400).json({ error: 'Invalid update data', details: validation.error.errors });
    }

    const updateData = validation.data;

    // Verify annotation exists and user has permission
    const existingAnnotation = await db
      .select()
      .from(annotations)
      .where(
        and(
          eq(annotations.id, id),
          eq(annotations.organizationId, req.user!.organizationId)
        )
      )
      .limit(1);

    if (!existingAnnotation.length) {
      return res.status(404).json({ error: 'Annotation not found' });
    }

    // Only author can edit content, but anyone can resolve
    if (updateData.content && existingAnnotation[0].authorId !== req.user!.id) {
      return res.status(403).json({ error: 'Only the author can edit annotation content' });
    }

    const updates: any = {};
    if (updateData.content) updates.content = updateData.content;
    if (updateData.isResolved !== undefined) {
      updates.isResolved = updateData.isResolved;
      if (updateData.isResolved) {
        updates.resolvedBy = req.user!.id;
        updates.resolvedAt = new Date();
      } else {
        updates.resolvedBy = null;
        updates.resolvedAt = null;
      }
    }
    updates.updatedAt = new Date();

    const updatedAnnotation = await db
      .update(annotations)
      .set(updates)
      .where(eq(annotations.id, id))
      .returning();

    // Get the complete updated annotation with author info
    const completeAnnotation = await db
      .select({
        id: annotations.id,
        content: annotations.content,
        type: annotations.type,
        entityType: annotations.entityType,
        entityId: annotations.entityId,
        authorId: annotations.authorId,
        parentId: annotations.parentId,
        position: annotations.position,
        metadata: annotations.metadata,
        isResolved: annotations.isResolved,
        resolvedBy: annotations.resolvedBy,
        resolvedAt: annotations.resolvedAt,
        mentions: annotations.mentions,
        attachments: annotations.attachments,
        createdAt: annotations.createdAt,
        updatedAt: annotations.updatedAt,
        authorName: users.fullName,
        authorEmail: users.email,
      })
      .from(annotations)
      .leftJoin(users, eq(annotations.authorId, users.id))
      .where(eq(annotations.id, id))
      .limit(1);

    // Broadcast to WebSocket clients
    const collaborationWS = req.app.get('collaborationWS');
    if (collaborationWS) {
      collaborationWS.broadcastAnnotationUpdate(
        existingAnnotation[0].entityType,
        existingAnnotation[0].entityId,
        completeAnnotation[0],
        'updated'
      );
    }

    res.json(completeAnnotation[0]);
  } catch (error) {
    console.error('Error updating annotation:', error);
    res.status(500).json({ error: 'Failed to update annotation' });
  }
});

// Delete annotation
router.delete('/:id', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const { id } = req.params;

    // Verify annotation exists and user has permission
    const existingAnnotation = await db
      .select()
      .from(annotations)
      .where(
        and(
          eq(annotations.id, id),
          eq(annotations.organizationId, req.user!.organizationId)
        )
      )
      .limit(1);

    if (!existingAnnotation.length) {
      return res.status(404).json({ error: 'Annotation not found' });
    }

    // Only author or admin can delete
    if (existingAnnotation[0].authorId !== req.user!.id && req.user!.role !== 'admin') {
      return res.status(403).json({ error: 'Permission denied' });
    }

    // Delete annotation and all replies
    await db.delete(annotations).where(eq(annotations.id, id));
    await db.delete(annotations).where(eq(annotations.parentId, id));

    // Broadcast to WebSocket clients
    const collaborationWS = req.app.get('collaborationWS');
    if (collaborationWS) {
      collaborationWS.broadcastAnnotationUpdate(
        existingAnnotation[0].entityType,
        existingAnnotation[0].entityId,
        { id },
        'deleted'
      );
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting annotation:', error);
    res.status(500).json({ error: 'Failed to delete annotation' });
  }
});

// Get active collaborators for an entity
router.get('/:entityType/:entityId/collaborators', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const { entityType, entityId } = req.params;

    const collaborationWS = req.app.get('collaborationWS');
    if (!collaborationWS) {
      return res.json([]);
    }

    const activeUserIds = collaborationWS.getActiveUsers(entityType, entityId);
    
    if (activeUserIds.length === 0) {
      return res.json([]);
    }

    // Get user details for active collaborators
    const activeUsers = await db
      .select({
        id: users.id,
        fullName: users.fullName,
        email: users.email,
      })
      .from(users)
      .where(
        and(
          eq(users.organizationId, req.user!.organizationId),
          // Note: In a real app, you'd use SQL IN clause with the activeUserIds
        )
      );

    res.json(activeUsers.filter(user => activeUserIds.includes(user.id)));
  } catch (error) {
    console.error('Error getting collaborators:', error);
    res.status(500).json({ error: 'Failed to get collaborators' });
  }
});

// Test endpoint to demonstrate collaboration feature
router.post('/demo', async (req, res) => {
  try {
    const demoAnnotation = {
      id: `demo-${Date.now()}`,
      content: "Demo collaboration annotation - This feature works!",
      type: "comment",
      entityType: "candidate",
      entityId: "demo-candidate",
      authorId: "demo-user",
      authorName: "Demo User",
      authorEmail: "<EMAIL>",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isResolved: false,
      replies: []
    };

    res.json({ 
      success: true, 
      annotation: demoAnnotation,
      message: "Collaboration feature is working! Authentication integration in progress."
    });
  } catch (error) {
    console.error('Demo annotation error:', error);
    res.status(500).json({ error: 'Demo endpoint failed' });
  }
});

export default router;