import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';
import { Router } from 'express';

const router = Router();

let testWss: WebSocketServer | null = null;

export function setupTestWebSocket(server: Server) {
  console.log('🧪 Setting up test WebSocket server for Twilio connectivity test...');
  
  testWss = new WebSocketServer({ 
    server, 
    path: '/test-websocket',
    perMessageDeflate: false,
    verifyClient: (info) => {
      console.log('🧪 TEST: WebSocket connection attempt from:', info.origin);
      console.log('🧪 TEST: WebSocket path:', info.req.url);
      console.log('🧪 TEST: Allowing connection');
      return true;
    }
  });

  console.log('🧪 Test WebSocket server initialized at /test-websocket');
  
  testWss.on('connection', (ws: WebSocket, req) => {
    console.log('🎉🧪 TEST WEBSOCKET CONNECTION ESTABLISHED! 🧪🎉');
    console.log('🧪 Connection from:', req.url);
    
    // Send a test message
    ws.send(JSON.stringify({ 
      event: 'connected', 
      message: 'Test WebSocket connected successfully' 
    }));
    
    ws.on('message', (message) => {
      console.log('🧪 Received test message:', message.toString());
      // Echo back the message
      ws.send(JSON.stringify({ 
        event: 'echo', 
        data: message.toString() 
      }));
    });
    
    ws.on('close', () => {
      console.log('🧪 Test WebSocket connection closed');
    });
  });
  
  testWss.on('error', (error) => {
    console.error('🧪 Test WebSocket Server Error:', error);
  });
  
  return testWss;
}

// Route to generate TwiML for WebSocket test
router.post('/test-twilio-websocket/:callId', (req, res) => {
  const { callId } = req.params;
  const host = req.get('host');
  const publicHost = host?.includes('localhost') 
    ? 'd06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev'
    : host?.replace(':5000', '') || host;
  const testWebSocketUrl = `wss://${publicHost}/test-websocket`;
  
  console.log('🧪 Generating test TwiML with WebSocket URL:', testWebSocketUrl);
  
  const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
    <Connect>
        <Stream url="${testWebSocketUrl}">
            <Parameter name="test" value="true" />
            <Parameter name="call_id" value="${callId}" />
        </Stream>
    </Connect>
    <Say>This is a WebSocket connectivity test. The call will end shortly.</Say>
    <Hangup/>
</Response>`;

  console.log('🧪 Generated test TwiML:', twiml);
  res.set('Content-Type', 'text/xml');
  res.send(twiml);
});

export default router;