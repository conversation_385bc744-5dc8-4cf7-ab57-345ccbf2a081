// Test datetime format conversion
console.log('🕐 Testing datetime format conversion...');

// Simulate the frontend datetime-local format
const datetimeLocal = '2025-09-21T08:11'; // This is what comes from the HTML input
console.log('📝 Input (datetime-local):', datetimeLocal);

// Convert to Date object
const date = new Date(datetimeLocal);
console.log('📅 Date object:', date);
console.log('📅 Date valid?', !isNaN(date.getTime()));

// Convert to ISO string (what the API expects)
const isoString = date.toISOString();
console.log('📤 Output (ISO string):', isoString);

// Test with current time + 3 minutes (Quick Test scenario)
const quickTestTime = new Date(Date.now() + 3 * 60 * 1000);
const quickTestLocal = quickTestTime.toISOString().slice(0, 16);
const quickTestISO = new Date(quickTestLocal).toISOString();

console.log('');
console.log('⚡ Quick Test scenario:');
console.log('📝 Quick test (datetime-local):', quickTestLocal);
console.log('📤 Quick test (ISO string):', quickTestISO);

// Test the validation schema format
const { z } = require('zod');

const testSchema = z.object({
  scheduledAt: z.string().datetime('Invalid scheduled time')
});

console.log('');
console.log('🔍 Testing validation schema:');

try {
  const result1 = testSchema.parse({ scheduledAt: datetimeLocal });
  console.log('❌ Should fail - datetime-local format:', result1);
} catch (error) {
  console.log('✅ Correctly rejected datetime-local format:', error.errors[0].message);
}

try {
  const result2 = testSchema.parse({ scheduledAt: isoString });
  console.log('✅ Correctly accepted ISO format:', result2);
} catch (error) {
  console.log('❌ Should pass - ISO format:', error.errors[0].message);
}

console.log('');
console.log('🎯 Summary:');
console.log('- Frontend sends datetime-local format (missing seconds/timezone)');
console.log('- Backend expects full ISO datetime string');
console.log('- Solution: Convert datetime-local to ISO before sending to API');
console.log('✅ Fix implemented in formatDateTimeForAPI() function');
