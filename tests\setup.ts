import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/hrms_test';

// Mock external services for testing
jest.mock('../server/services/elevenlabsService', () => ({
  elevenLabsService: {
    generateSpeech: jest.fn().mockResolvedValue({
      success: true,
      audioBuffer: Buffer.from('mock-audio'),
      contentType: 'audio/mpeg'
    }),
    startConversation: jest.fn().mockResolvedValue('mock-session-id'),
    endConversation: jest.fn().mockResolvedValue(undefined),
    sendAudioToConversation: jest.fn().mockReturnValue(true),
    getConversationStatus: jest.fn().mockReturnValue({
      isActive: true,
      startedAt: new Date(),
      conversationId: 'mock-conversation-id'
    })
  }
}));

jest.mock('../server/services/zoomVideoSDKService', () => ({
  zoomVideoSDKService: {
    generateToken: jest.fn().mockResolvedValue({
      hostToken: 'mock-host-token',
      participantToken: 'mock-participant-token',
      sessionName: 'mock-session'
    }),
    createSession: jest.fn().mockResolvedValue({
      sessionId: 'mock-session-id',
      sessionName: 'mock-session',
      hostToken: 'mock-host-token'
    })
  }
}));

jest.mock('../server/services/botRunnerService', () => ({
  botRunnerService: {
    startBotSession: jest.fn().mockResolvedValue('mock-bot-session-id'),
    endBotSession: jest.fn().mockResolvedValue(undefined),
    getBotSessionStatus: jest.fn().mockReturnValue({
      status: 'connected',
      startedAt: new Date()
    })
  }
}));

jest.mock('../server/services/transcriptionService', () => ({
  transcriptionService: {
    transcribeAudio: jest.fn().mockResolvedValue({
      text: 'Mock transcription text',
      segments: [],
      language: 'en',
      duration: 120
    }),
    generateInterviewSummary: jest.fn().mockResolvedValue({
      summary: 'Mock interview summary',
      keyPoints: ['Point 1', 'Point 2'],
      competencyScores: {
        'Communication': {
          score: 8,
          evidence: ['Good communication'],
          feedback: 'Excellent communication skills'
        }
      },
      overallRating: 8,
      recommendations: ['Recommendation 1']
    })
  }
}));

jest.mock('../server/services/interviewInvitationService', () => ({
  interviewInvitationService: {
    sendInterviewInvitation: jest.fn().mockResolvedValue(true),
    sendCancellationNotification: jest.fn().mockResolvedValue(true),
    sendRescheduleNotification: jest.fn().mockResolvedValue(true)
  }
}));

// Global test utilities
global.testUtils = {
  createMockUser: () => ({
    id: 'test-user-id',
    email: '<EMAIL>',
    fullName: 'Test User',
    organizationId: 'test-org-id',
    organizationName: 'Test Organization'
  }),
  
  createMockInterview: () => ({
    id: 'test-interview-id',
    organizationId: 'test-org-id',
    candidateId: 'test-candidate-id',
    role: 'Software Engineer',
    status: 'scheduled',
    scheduledAt: new Date(),
    duration: 60,
    agentProfileId: 'test-agent-profile-id',
    zoomJoinUrl: 'https://zoom.us/j/123456789',
    createdAt: new Date(),
    updatedAt: new Date()
  }),
  
  createMockCandidate: () => ({
    id: 'test-candidate-id',
    organizationId: 'test-org-id',
    email: '<EMAIL>',
    fullName: 'Test Candidate',
    name: 'Test Candidate',
    phone: '+1234567890',
    createdAt: new Date(),
    updatedAt: new Date()
  }),
  
  createMockAgentProfile: () => ({
    id: 'test-agent-profile-id',
    organizationId: 'test-org-id',
    name: 'Test Agent Profile',
    promptTemplate: 'Test prompt template',
    voiceSettings: {
      voiceId: 'sarah',
      stability: 0.6,
      similarityBoost: 0.8
    },
    rubric: {
      competencies: ['Communication', 'Technical Skills']
    },
    safetySettings: {
      filterLevel: 'medium'
    },
    createdAt: new Date(),
    updatedAt: new Date()
  })
};

// Extend global types
declare global {
  var testUtils: {
    createMockUser: () => any;
    createMockInterview: () => any;
    createMockCandidate: () => any;
    createMockAgentProfile: () => any;
  };
}
