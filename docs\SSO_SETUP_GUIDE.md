# Single Sign-On (SSO) Setup Guide

This guide walks you through setting up Single Sign-On (SSO) for your organization in the multi-tenant ATS system.

## Prerequisites

1. **Admin Access**: You need admin-level access to your organization
2. **SSO Provider Account**: Access to configure your chosen SSO provider (Google Workspace, Azure AD, etc.)
3. **Domain Verification**: Your organization domain should be configured in the system

## Step-by-Step SSO Setup

### Step 1: Access SSO Configuration

1. Log into your ATS system as an admin
2. Navigate to **SSO Setup** in the sidebar (Shield icon)
3. You'll see the SSO configuration dashboard

### Step 2: Enable SSO for Your Organization

1. In the **SSO Status** section, toggle **Single Sign-On** to **Enabled**
2. Click **Save Configuration** to apply the changes

### Step 3: Configure SSO Settings

Go to the **Settings** tab and configure:

1. **Auto-provision users**: Enable to automatically create accounts for new SSO users
2. **Default Role**: Choose the default role for new users (Viewer, Member, or Admin)
3. **Allowed Domains**: Add your organization's domains (e.g., `yourcompany.com`)

### Step 4: Add an SSO Provider

#### For Google Workspace (OIDC):

1. Go to the **Providers** tab
2. Click **Add Provider**
3. Fill in the form:
   - **Provider Name**: "Google Workspace"
   - **Provider Type**: "OpenID Connect"
   - **Client ID**: Your Google OAuth client ID
   - **Client Secret**: Your Google OAuth client secret
   - **Issuer URL**: `https://accounts.google.com`
   - **Discovery URL**: `https://accounts.google.com/.well-known/openid_configuration`
   - **Scopes**: `openid email profile`

#### For Azure Active Directory (SAML):

1. Go to the **Providers** tab
2. Click **Add Provider**
3. Fill in the form:
   - **Provider Name**: "Azure Active Directory"
   - **Provider Type**: "SAML 2.0"
   - **Client ID**: Your Azure app registration ID
   - **Client Secret**: Your Azure app client secret
   - **Issuer URL**: `https://login.microsoftonline.com/[tenant-id]`

### Step 5: Configure Your SSO Provider

#### Google Workspace Setup:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API
4. Go to **Credentials** → **Create Credentials** → **OAuth 2.0 Client ID**
5. Set Application Type to **Web application**
6. Add the **Redirect URI** from your ATS system:
   ```
   https://your-domain.replit.app/api/sso/callback/google-workspace
   ```
7. Copy the **Client ID** and **Client Secret** to your ATS SSO configuration

#### Azure Active Directory Setup:

1. Go to [Azure Portal](https://portal.azure.com/)
2. Navigate to **Azure Active Directory** → **App registrations**
3. Click **New registration**
4. Set the **Redirect URI** to:
   ```
   https://your-domain.replit.app/api/sso/callback/azure-ad
   ```
5. In **Certificates & secrets**, create a new client secret
6. Copy the **Application ID** and **Client Secret** to your ATS SSO configuration

### Step 6: Test Your SSO Configuration

1. In the ATS SSO setup, find your configured provider
2. Click the **Test** button (external link icon)
3. You should see a success message if everything is configured correctly

### Step 7: Enable the Provider

1. Toggle the switch next to your SSO provider to **Enabled**
2. Click **Save Configuration**

## Using SSO

### For End Users:

1. Go to the ATS login page
2. Click **Sign in with [Your SSO Provider]**
3. You'll be redirected to your SSO provider
4. After successful authentication, you'll be redirected back to the ATS

### For Admins:

1. Monitor SSO users in the **SSO Users** tab
2. Manage user roles and permissions as needed
3. Review SSO provider status and user counts

## Troubleshooting

### Common Issues:

1. **"Invalid Redirect URI"**:
   - Ensure the redirect URI in your SSO provider matches exactly: `https://your-domain.replit.app/api/sso/callback/[provider-id]`

2. **"Client Secret Invalid"**:
   - Regenerate the client secret in your SSO provider
   - Update the secret in your ATS SSO configuration

3. **"Domain Not Allowed"**:
   - Add your organization's domain to the **Allowed Domains** list in SSO settings

4. **"User Not Auto-Provisioned"**:
   - Ensure **Auto-provision users** is enabled in SSO settings
   - Check that the user's email domain is in the allowed domains list

### Testing SSO:

1. Use the built-in **Test** functionality in the Providers tab
2. Check the provider status indicators (Active/Inactive/Error)
3. Review user counts to ensure users are being created properly

## Security Best Practices

1. **Regular Review**: Periodically review SSO providers and user access
2. **Least Privilege**: Set appropriate default roles for new users
3. **Domain Restrictions**: Only allow domains you control
4. **Monitor Usage**: Keep track of SSO user activity
5. **Backup Authentication**: Maintain local admin accounts as backup

## Provider-Specific Configuration

### Google Workspace Advanced Settings:

- **Attribute Mapping**:
  - Email: `email`
  - First Name: `given_name`
  - Last Name: `family_name`
  - Groups: `groups`

### Azure AD Advanced Settings:

- **Attribute Mapping**:
  - Email: `http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress`
  - First Name: `http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname`
  - Last Name: `http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname`
  - Groups: `http://schemas.microsoft.com/ws/2008/06/identity/claims/groups`

## Support

If you encounter issues:

1. Check the provider status in the SSO dashboard
2. Use the Test functionality to diagnose connection issues
3. Review your SSO provider's configuration
4. Contact your system administrator for additional help

## Next Steps

Once SSO is configured:

1. **Communicate to Users**: Inform your team about the new SSO login option
2. **Gradual Rollout**: Consider enabling SSO for a small group first
3. **Training**: Provide training on the new login process
4. **Monitoring**: Monitor SSO usage and user feedback

This completes your SSO setup. Users can now authenticate using your organization's SSO provider while maintaining the security and multi-tenant isolation of the ATS system.