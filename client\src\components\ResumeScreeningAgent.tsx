
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Upload, FileText, Brain, CheckCircle, XCircle, Clock, User, Briefcase, GraduationCap } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const ResumeScreeningAgent = () => {
  const [file, setFile] = useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [apiKey, setApiKey] = useState('');
  const { toast } = useToast();

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type === 'application/pdf' || selectedFile.type.includes('document')) {
        setFile(selectedFile);
        console.log("File uploaded:", selectedFile.name);
      } else {
        toast({
          title: "Invalid file type",
          description: "Please upload a PDF or Word document",
          variant: "destructive",
        });
      }
    }
  };

  const analyzeResume = async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please upload a resume first",
        variant: "destructive",
      });
      return;
    }

    if (!apiKey) {
      toast({
        title: "API Key Required",
        description: "Please enter your OpenAI API key to analyze the resume",
        variant: "destructive",
      });
      return;
    }

    setIsAnalyzing(true);
    console.log("Starting resume analysis for:", file.name);

    try {
      // Simulate file text extraction (in real implementation, you'd use a PDF parser)
      const mockResumeText = `John Doe
Software Engineer with 5 years of experience
Skills: React, TypeScript, Node.js, Python, AWS
Previous Companies: TechCorp (Senior Developer), StartupXYZ (Full Stack Developer)
Education: Bachelor's in Computer Science from MIT
Experience: Led team of 4 developers, built scalable web applications, implemented CI/CD pipelines`;

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are an expert HR assistant. Analyze resumes and provide structured summaries in JSON format with fields: name, experience_years, key_skills (array), recent_roles (array with title and company), education, overall_score (1-10), strengths (array), concerns (array).'
            },
            {
              role: 'user',
              content: `Analyze this resume text and provide a structured summary:\n\n${mockResumeText}`
            }
          ],
          temperature: 0.3,
          max_tokens: 800,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      const analysis = JSON.parse(data.choices[0].message.content);
      
      console.log("Analysis result:", analysis);
      setAnalysisResult(analysis);
      
      toast({
        title: "Resume analyzed successfully",
        description: `${analysis.name} - Score: ${analysis.overall_score}/10`,
      });
    } catch (error) {
      console.error("Error analyzing resume:", error);
      toast({
        title: "Analysis failed",
        description: error instanceof Error ? error.message : "Failed to analyze resume",
        variant: "destructive",
      });
      
      // Show mock result for demo purposes
      const mockAnalysis = {
        name: "John Doe",
        experience_years: 5,
        key_skills: ["React", "TypeScript", "Node.js", "Python", "AWS"],
        recent_roles: [
          { title: "Senior Developer", company: "TechCorp" },
          { title: "Full Stack Developer", company: "StartupXYZ" }
        ],
        education: "Bachelor's in Computer Science from MIT",
        overall_score: 8,
        strengths: ["Strong technical skills", "Leadership experience", "Full-stack expertise"],
        concerns: ["May be overqualified for junior positions"]
      };
      setAnalysisResult(mockAnalysis);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleDecision = (decision: 'approve' | 'reject') => {
    console.log(`Resume ${decision}d for ${analysisResult?.name}`);
    toast({
      title: `Candidate ${decision}d`,
      description: `${analysisResult?.name} has been ${decision}d and ${decision === 'approve' ? 'moved to scheduling' : 'archived'}`,
      variant: decision === 'approve' ? 'default' : 'destructive',
    });
  };

  return (
    <div className="space-y-6">
      {/* File Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Upload className="w-5 h-5 mr-2" />
            Resume Upload & Analysis
          </CardTitle>
          <CardDescription>
            Upload resumes (PDF/DOCX) for AI-powered screening and analysis
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">OpenAI API Key</label>
            <Input
              type="password"
              placeholder="sk-..."
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              className="mb-4"
            />
            <p className="text-xs text-gray-500 mb-4">Enter your OpenAI API key to enable resume analysis</p>
          </div>
          
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
            <input
              type="file"
              accept=".pdf,.doc,.docx"
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload"
            />
            <label htmlFor="file-upload" className="cursor-pointer">
              <FileText className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <p className="text-lg font-medium text-gray-700">
                {file ? file.name : "Drop your resume here or click to browse"}
              </p>
              <p className="text-sm text-gray-500 mt-2">Supports PDF and Word documents</p>
            </label>
          </div>

          <Button 
            onClick={analyzeResume} 
            disabled={!file || isAnalyzing || !apiKey}
            className="w-full"
          >
            {isAnalyzing ? (
              <>
                <Clock className="w-4 h-4 mr-2 animate-spin" />
                Analyzing Resume...
              </>
            ) : (
              <>
                <Brain className="w-4 h-4 mr-2" />
                Analyze with AI
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Analysis Results */}
      {analysisResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <User className="w-5 h-5 mr-2" />
                Analysis Results
              </span>
              <Badge variant={analysisResult.overall_score >= 7 ? "default" : "secondary"}>
                Score: {analysisResult.overall_score}/10
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Candidate Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="p-4">
                <div className="flex items-center mb-2">
                  <User className="w-4 h-4 mr-2 text-blue-600" />
                  <span className="font-medium">Candidate</span>
                </div>
                <p className="text-lg font-bold">{analysisResult.name}</p>
                <p className="text-sm text-gray-600">{analysisResult.experience_years} years experience</p>
              </Card>

              <Card className="p-4">
                <div className="flex items-center mb-2">
                  <Briefcase className="w-4 h-4 mr-2 text-green-600" />
                  <span className="font-medium">Recent Role</span>
                </div>
                <p className="text-lg font-bold">{analysisResult.recent_roles[0]?.title}</p>
                <p className="text-sm text-gray-600">{analysisResult.recent_roles[0]?.company}</p>
              </Card>

              <Card className="p-4">
                <div className="flex items-center mb-2">
                  <GraduationCap className="w-4 h-4 mr-2 text-purple-600" />
                  <span className="font-medium">Education</span>
                </div>
                <p className="text-sm">{analysisResult.education}</p>
              </Card>
            </div>

            {/* Skills */}
            <div>
              <h4 className="font-medium mb-2">Key Skills</h4>
              <div className="flex flex-wrap gap-2">
                {analysisResult.key_skills.map((skill: string, index: number) => (
                  <Badge key={index} variant="outline">{skill}</Badge>
                ))}
              </div>
            </div>

            {/* Strengths & Concerns */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2 text-green-700">Strengths</h4>
                <ul className="space-y-1">
                  {analysisResult.strengths.map((strength: string, index: number) => (
                    <li key={index} className="flex items-center text-sm">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                      {strength}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium mb-2 text-orange-700">Concerns</h4>
                <ul className="space-y-1">
                  {analysisResult.concerns.map((concern: string, index: number) => (
                    <li key={index} className="flex items-center text-sm">
                      <XCircle className="w-4 h-4 mr-2 text-orange-600" />
                      {concern}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Decision Buttons */}
            <div className="flex space-x-4 pt-4 border-t">
              <Button 
                onClick={() => handleDecision('approve')} 
                className="flex-1 bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Approve & Schedule Interview
              </Button>
              <Button 
                onClick={() => handleDecision('reject')} 
                variant="outline"
                className="flex-1 text-red-600 border-red-600 hover:bg-red-50"
              >
                <XCircle className="w-4 h-4 mr-2" />
                Reject Application
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ResumeScreeningAgent;
