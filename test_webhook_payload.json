{"data": {"conversation_id": "test-conv-123", "agent_id": "test-agent-456", "transcript": [{"role": "agent", "message": "Hello, this is <PERSON> from Acme Corp. I'm calling about the Senior Developer position you applied for. Do you have a few minutes to chat?"}, {"role": "user", "message": "Hi <PERSON>, yes I have time. I'm very interested in this opportunity."}, {"role": "agent", "message": "Great! Can you tell me about your experience with React and Node.js?"}, {"role": "user", "message": "I have 5 years of experience with React and 3 years with Node.js. I've built several full-stack applications."}, {"role": "agent", "message": "Perfect! Would you be available for an interview next Tuesday at 2 PM?"}, {"role": "user", "message": "Yes, Tuesday at 2 PM works great for me."}, {"role": "agent", "message": "Excellent! I'll send you a calendar invite. Thank you for your time and I look forward to speaking with you next week."}], "metadata": {"call_duration_secs": 180, "termination_reason": "agent_ended", "main_language": "en", "cost": 0.05}, "analysis": {"transcript_summary": "<PERSON> from Acme Corp spoke with a candidate about a Senior Developer position. The candidate has 5 years React and 3 years Node.js experience. They scheduled an interview for Tuesday at 2 PM."}}}