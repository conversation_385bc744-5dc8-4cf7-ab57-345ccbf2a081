
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, AlertTriangle } from "lucide-react";
import { EnhancedAnalysisResult } from "@/types/resumeAnalysis";

interface SkillAnalysisProps {
  skillAnalysis: EnhancedAnalysisResult['skill_analysis'];
}

const SkillAnalysis: React.FC<SkillAnalysisProps> = ({ skillAnalysis }) => {
  // Provide safe defaults if skillAnalysis is undefined or missing properties
  const safeSkillAnalysis = {
    matched_skills: skillAnalysis?.matched_skills || [],
    missing_skills: skillAnalysis?.missing_skills || [],
    transferable_skills: skillAnalysis?.transferable_skills || []
  };

  return (
    <div className="space-y-4">
      <h4 className="font-medium text-lg">Skill Analysis</h4>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <h5 className="font-medium mb-2 text-green-700 flex items-center">
            <CheckCircle className="w-4 h-4 mr-1" />
            Matched Skills ({safeSkillAnalysis.matched_skills.length})
          </h5>
          <div className="flex flex-wrap gap-1">
            {safeSkillAnalysis.matched_skills.map((skill, index) => (
              <Badge key={index} className="bg-green-100 text-green-800">{skill}</Badge>
            ))}
          </div>
        </div>
        
        <div>
          <h5 className="font-medium mb-2 text-red-700 flex items-center">
            <XCircle className="w-4 h-4 mr-1" />
            Missing Skills ({safeSkillAnalysis.missing_skills.length})
          </h5>
          <div className="flex flex-wrap gap-1">
            {safeSkillAnalysis.missing_skills.map((skill, index) => (
              <Badge key={index} variant="outline" className="border-red-200 text-red-700">{skill}</Badge>
            ))}
          </div>
        </div>

        <div>
          <h5 className="font-medium mb-2 text-blue-700 flex items-center">
            <AlertTriangle className="w-4 h-4 mr-1" />
            Transferable Skills ({safeSkillAnalysis.transferable_skills.length})
          </h5>
          <div className="flex flex-wrap gap-1">
            {safeSkillAnalysis.transferable_skills.map((skill, index) => (
              <Badge key={index} variant="outline" className="border-blue-200 text-blue-700">{skill}</Badge>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkillAnalysis;
