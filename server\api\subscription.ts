import { Router, Request, Response } from 'express';
import { z } from 'zod';

const router = Router();

// Mock subscription data for demonstration
const mockSubscriptions = new Map([
  ['user-1', {
    id: 'sub-1',
    planId: 'professional',
    status: 'active',
    currentPeriodStart: new Date().toISOString(),
    currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    cancelAtPeriodEnd: false
  }],
  ['user-2', {
    id: 'sub-2',
    planId: 'starter',
    status: 'active',
    currentPeriodStart: new Date().toISOString(),
    currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    cancelAtPeriodEnd: false
  }]
]);

const plans = [
  {
    id: 'starter',
    name: 'Starter',
    price: 29,
    billingCycle: 'monthly',
    features: [
      'Up to 50 candidates per month',
      'Basic resume screening',
      'Email notifications',
      'Standard support',
      '1 user account'
    ]
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 99,
    billingCycle: 'monthly',
    features: [
      'Up to 500 candidates per month',
      'AI-powered resume analysis',
      'Advanced candidate matching',
      'Email & SMS notifications',
      'Calendar integration',
      'Priority support',
      'Up to 5 user accounts',
      'Custom workflows'
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 299,
    billingCycle: 'monthly',
    features: [
      'Unlimited candidates',
      'Advanced AI analytics',
      'Custom integrations',
      'Dedicated account manager',
      'White-label options',
      'SAML/SSO authentication',
      'Unlimited user accounts',
      'Custom reporting',
      'API access'
    ]
  }
];

// Middleware to authenticate user (simplified for demo)
const authenticateUser = (req: Request, res: Response, next: any) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  
  // Mock user authentication - in real app, validate JWT token
  req.user = { id: 'user-1', email: '<EMAIL>' };
  next();
};

/**
 * @swagger
 * /subscription/current:
 *   get:
 *     summary: Get current subscription
 *     description: Retrieve the current user's subscription details
 *     tags: [Subscription]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current subscription details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 subscription:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     planId:
 *                       type: string
 *                     status:
 *                       type: string
 *                       enum: [active, inactive, cancelled]
 *                     currentPeriodStart:
 *                       type: string
 *                       format: date-time
 *                     currentPeriodEnd:
 *                       type: string
 *                       format: date-time
 *                     cancelAtPeriodEnd:
 *                       type: boolean
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/current', authenticateUser, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const subscription = mockSubscriptions.get(userId);
    
    if (!subscription) {
      return res.json({ subscription: null });
    }
    
    res.json({ subscription });
  } catch (error) {
    console.error('Error fetching subscription:', error);
    res.status(500).json({ error: 'Failed to fetch subscription' });
  }
});

/**
 * @swagger
 * /subscription/plans:
 *   get:
 *     summary: Get subscription plans
 *     description: Retrieve all available subscription plans
 *     tags: [Subscription]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Available subscription plans
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 plans:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       price:
 *                         type: number
 *                       billingCycle:
 *                         type: string
 *                       features:
 *                         type: array
 *                         items:
 *                           type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/plans', authenticateUser, async (req: Request, res: Response) => {
  try {
    res.json({ plans });
  } catch (error) {
    console.error('Error fetching plans:', error);
    res.status(500).json({ error: 'Failed to fetch plans' });
  }
});

// Upgrade subscription
const upgradeSchema = z.object({
  planId: z.string()
});

router.post('/upgrade', authenticateUser, async (req: Request, res: Response) => {
  try {
    const { planId } = upgradeSchema.parse(req.body);
    const userId = req.user.id;
    
    const plan = plans.find(p => p.id === planId);
    if (!plan) {
      return res.status(400).json({ error: 'Invalid plan ID' });
    }
    
    // In a real app, this would integrate with Stripe or another payment processor
    // For demo purposes, we'll simulate success
    const newSubscription = {
      id: `sub-${Date.now()}`,
      planId,
      status: 'active' as const,
      currentPeriodStart: new Date().toISOString(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      cancelAtPeriodEnd: false
    };
    
    mockSubscriptions.set(userId, newSubscription);
    
    // Simulate Stripe checkout URL for higher-tier plans
    if (plan.price > 99) {
      return res.json({ 
        checkoutUrl: `https://checkout.stripe.com/demo/${planId}`,
        subscription: newSubscription
      });
    }
    
    res.json({ 
      success: true, 
      subscription: newSubscription,
      message: `Successfully upgraded to ${plan.name} plan`
    });
  } catch (error) {
    console.error('Error upgrading subscription:', error);
    res.status(500).json({ error: 'Failed to upgrade subscription' });
  }
});

// Cancel subscription
router.post('/cancel', authenticateUser, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const subscription = mockSubscriptions.get(userId);
    
    if (!subscription) {
      return res.status(404).json({ error: 'No subscription found' });
    }
    
    // Mark subscription for cancellation at period end
    const updatedSubscription = {
      ...subscription,
      cancelAtPeriodEnd: true
    };
    
    mockSubscriptions.set(userId, updatedSubscription);
    
    res.json({ 
      success: true, 
      subscription: updatedSubscription,
      message: 'Subscription will be cancelled at the end of the current billing period'
    });
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    res.status(500).json({ error: 'Failed to cancel subscription' });
  }
});

// Reactivate subscription
router.post('/reactivate', authenticateUser, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const subscription = mockSubscriptions.get(userId);
    
    if (!subscription) {
      return res.status(404).json({ error: 'No subscription found' });
    }
    
    // Reactivate subscription
    const updatedSubscription = {
      ...subscription,
      cancelAtPeriodEnd: false,
      status: 'active' as const
    };
    
    mockSubscriptions.set(userId, updatedSubscription);
    
    res.json({ 
      success: true, 
      subscription: updatedSubscription,
      message: 'Subscription has been reactivated'
    });
  } catch (error) {
    console.error('Error reactivating subscription:', error);
    res.status(500).json({ error: 'Failed to reactivate subscription' });
  }
});

// Get billing history
router.get('/billing-history', authenticateUser, async (req: Request, res: Response) => {
  try {
    const userId = req.user.id;
    const subscription = mockSubscriptions.get(userId);
    
    if (!subscription) {
      return res.json({ history: [] });
    }
    
    // Mock billing history
    const history = [
      {
        id: 'inv-1',
        date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        amount: 99,
        status: 'paid',
        description: 'Professional Plan - Monthly'
      },
      {
        id: 'inv-2',
        date: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
        amount: 99,
        status: 'paid',
        description: 'Professional Plan - Monthly'
      }
    ];
    
    res.json({ history });
  } catch (error) {
    console.error('Error fetching billing history:', error);
    res.status(500).json({ error: 'Failed to fetch billing history' });
  }
});

export default router;