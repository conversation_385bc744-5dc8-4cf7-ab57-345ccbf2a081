import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Upload, 
  FileText, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Linkedin, 
  Star,
  CheckCircle,
  XCircle,
  AlertCircle,
  MessageSquare,
  UserCheck,
  UserX,
  Calendar
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ContactInfo {
  name?: string;
  email?: string;
  phone?: string;
  location?: string;
  linkedin?: string;
}

interface ResumeAnalysis {
  overall_score: number;
  match_score: number;
  experience_years: number;
  key_skills: string[];
  matched_skills: string[];
  missing_skills: string[];
  strengths: string[];
  concerns: string[];
  recommendation: 'HIRE' | 'INTERVIEW' | 'REJECT';
  detailed_feedback: string;
  interview_questions: string[];
}

interface AnalysisResult {
  contactInfo: ContactInfo;
  analysis?: ResumeAnalysis;
  extractedText: string;
  method: string;
  hasJobAnalysis: boolean;
}

export default function ResumeAnalysisApp() {
  const [file, setFile] = useState<File | null>(null);
  const [jobDescription, setJobDescription] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [candidateId, setCandidateId] = useState<string | null>(null);
  const [candidateStatus, setCandidateStatus] = useState<'pending_review' | 'approved_for_interview' | 'rejected'>('pending_review');
  const { toast } = useToast();

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFile = event.target.files?.[0];
    if (uploadedFile) {
      const allowedTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword',
        'text/plain'
      ];
      
      if (!allowedTypes.includes(uploadedFile.type)) {
        toast({
          title: "Invalid file type",
          description: "Please upload a PDF, Word document, or text file.",
          variant: "destructive"
        });
        return;
      }
      
      setFile(uploadedFile);
      setAnalysisResult(null);
      setCandidateId(null);
      setCandidateStatus('pending_review');
    }
  }, [toast]);

  const analyzeResume = useCallback(async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please upload a resume file first.",
        variant: "destructive"
      });
      return;
    }

    setIsAnalyzing(true);
    
    try {
      const fileContent = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const result = e.target?.result as string;
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.readAsDataURL(file);
      });

      const response = await fetch('/api/resume/parse', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileContent,
          fileName: file.name,
          fileType: file.type,
          extractContactOnly: false,
          jobDescription: jobDescription.trim() || undefined
        }),
      });

      if (!response.ok) {
        throw new Error('Analysis failed');
      }

      const result: AnalysisResult = await response.json();
      setAnalysisResult(result);
      
      // Save candidate if analysis was successful and has job analysis
      if (result.hasJobAnalysis && result.analysis && result.contactInfo.email) {
        try {
          const saveResponse = await fetch('/api/candidates', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              contactInfo: result.contactInfo,
              analysis: result.analysis,
              resumeText: result.extractedText,
              jobPostingId: null
            }),
          });

          if (saveResponse.ok) {
            const savedCandidate = await saveResponse.json();
            setCandidateId(savedCandidate.id);
            setCandidateStatus('pending_review');
            console.log('Candidate saved:', savedCandidate.id);
          } else {
            console.error('Failed to save candidate:', await saveResponse.text());
          }
        } catch (saveError) {
          console.error('Failed to save candidate:', saveError);
        }
      }
      
      toast({
        title: "Analysis complete",
        description: `Resume analyzed using ${result.method}`,
      });

    } catch (error) {
      console.error('Analysis error:', error);
      toast({
        title: "Analysis failed",
        description: "Please try again or check your file format.",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  }, [file, jobDescription, toast]);

  const handleApprove = useCallback(async () => {
    if (!candidateId) return;

    try {
      const response = await fetch(`/api/candidates/${candidateId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to approve candidate');
      }

      setCandidateStatus('approved_for_interview');
      
      toast({
        title: "Candidate approved!",
        description: "Availability email has been sent to the candidate.",
      });

    } catch (error) {
      console.error('Approval error:', error);
      toast({
        title: "Approval failed",
        description: "Please try again.",
        variant: "destructive"
      });
    }
  }, [candidateId, toast]);

  const handleReject = useCallback(async () => {
    if (!candidateId) return;

    try {
      const response = await fetch(`/api/candidates/${candidateId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: 'Does not meet requirements'
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to reject candidate');
      }

      setCandidateStatus('rejected');
      
      toast({
        title: "Candidate rejected",
        description: "Status has been updated.",
      });

    } catch (error) {
      console.error('Rejection error:', error);
      toast({
        title: "Rejection failed",
        description: "Please try again.",
        variant: "destructive"
      });
    }
  }, [candidateId, toast]);

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'HIRE': return 'bg-green-500';
      case 'INTERVIEW': return 'bg-yellow-500';
      case 'REJECT': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getRecommendationIcon = (recommendation: string) => {
    switch (recommendation) {
      case 'HIRE': return <CheckCircle className="w-4 h-4" />;
      case 'INTERVIEW': return <AlertCircle className="w-4 h-4" />;
      case 'REJECT': return <XCircle className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
              AI Resume Analysis
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Upload a resume and job description for intelligent matching and analysis
            </p>
          </div>

          {/* Upload Section */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="w-5 h-5" />
                Upload Resume & Job Description
              </CardTitle>
              <CardDescription>
                Upload a resume file and provide a job description for comprehensive analysis
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                {/* File Upload */}
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Resume File
                  </label>
                  <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                    <input
                      type="file"
                      accept=".pdf,.doc,.docx,.txt"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="resume-upload"
                    />
                    <label htmlFor="resume-upload" className="cursor-pointer">
                      <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {file ? file.name : 'Click to upload or drag and drop'}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        PDF, DOC, DOCX, or TXT
                      </p>
                    </label>
                  </div>
                </div>

                {/* Job Description */}
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Job Description (Optional)
                  </label>
                  <Textarea
                    value={jobDescription}
                    onChange={(e) => setJobDescription(e.target.value)}
                    placeholder="Paste the job description here for skill matching and analysis..."
                    className="min-h-[200px] resize-none"
                  />
                </div>
              </div>

              <Button 
                onClick={analyzeResume}
                disabled={!file || isAnalyzing}
                className="w-full"
                size="lg"
              >
                {isAnalyzing ? 'Analyzing...' : 'Analyze Resume'}
              </Button>
            </CardContent>
          </Card>

          {/* Results */}
          {analysisResult && (
            <div className="grid lg:grid-cols-3 gap-6">
              {/* Contact Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="w-5 h-5" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {analysisResult.contactInfo.name && (
                    <div className="flex items-center gap-3">
                      <User className="w-4 h-4 text-gray-500" />
                      <span className="font-medium">{analysisResult.contactInfo.name}</span>
                    </div>
                  )}
                  {analysisResult.contactInfo.email && (
                    <div className="flex items-center gap-3">
                      <Mail className="w-4 h-4 text-gray-500" />
                      <span className="text-sm">{analysisResult.contactInfo.email}</span>
                    </div>
                  )}
                  {analysisResult.contactInfo.phone && (
                    <div className="flex items-center gap-3">
                      <Phone className="w-4 h-4 text-gray-500" />
                      <span className="text-sm">{analysisResult.contactInfo.phone}</span>
                    </div>
                  )}
                  {analysisResult.contactInfo.location && (
                    <div className="flex items-center gap-3">
                      <MapPin className="w-4 h-4 text-gray-500" />
                      <span className="text-sm">{analysisResult.contactInfo.location}</span>
                    </div>
                  )}
                  {analysisResult.contactInfo.linkedin && (
                    <div className="flex items-center gap-3">
                      <Linkedin className="w-4 h-4 text-gray-500" />
                      <span className="text-sm">{analysisResult.contactInfo.linkedin}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Analysis Results */}
              <div className="lg:col-span-2">
                {analysisResult.hasJobAnalysis && analysisResult.analysis ? (
                  <Tabs defaultValue="overview" className="space-y-4">
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="overview">Overview</TabsTrigger>
                      <TabsTrigger value="skills">Skills</TabsTrigger>
                      <TabsTrigger value="feedback">Feedback</TabsTrigger>
                      <TabsTrigger value="questions">Questions</TabsTrigger>
                    </TabsList>

                    <TabsContent value="overview">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center justify-between">
                            <span>Analysis Overview</span>
                            <div className="flex items-center gap-2">
                              <Badge className={`${getRecommendationColor(analysisResult.analysis.recommendation)} text-white`}>
                                {getRecommendationIcon(analysisResult.analysis.recommendation)}
                                <span className="ml-1">{analysisResult.analysis.recommendation}</span>
                              </Badge>
                              {candidateStatus === 'approved_for_interview' && (
                                <Badge className="bg-green-100 text-green-800">
                                  <Calendar className="w-3 h-3 mr-1" />
                                  Interview Scheduled
                                </Badge>
                              )}
                              {candidateStatus === 'rejected' && (
                                <Badge className="bg-red-100 text-red-800">
                                  <UserX className="w-3 h-3 mr-1" />
                                  Rejected
                                </Badge>
                              )}
                            </div>
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-600 dark:text-gray-400">Overall Score</p>
                              <div className="flex items-center gap-2 mt-1">
                                <Progress value={analysisResult.analysis.overall_score} className="flex-1" />
                                <span className="font-semibold">{analysisResult.analysis.overall_score}%</span>
                              </div>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600 dark:text-gray-400">Job Match</p>
                              <div className="flex items-center gap-2 mt-1">
                                <Progress value={analysisResult.analysis.match_score} className="flex-1" />
                                <span className="font-semibold">{analysisResult.analysis.match_score}%</span>
                              </div>
                            </div>
                          </div>

                          <div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Experience</p>
                            <p className="font-medium">{analysisResult.analysis.experience_years} years</p>
                          </div>

                          <div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Strengths</p>
                            <div className="space-y-1">
                              {analysisResult.analysis.strengths.map((strength, index) => (
                                <div key={index} className="flex items-start gap-2">
                                  <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                                  <span className="text-sm">{strength}</span>
                                </div>
                              ))}
                            </div>
                          </div>

                          {analysisResult.analysis.concerns.length > 0 && (
                            <div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Concerns</p>
                              <div className="space-y-1">
                                {analysisResult.analysis.concerns.map((concern, index) => (
                                  <div key={index} className="flex items-start gap-2">
                                    <AlertCircle className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                                    <span className="text-sm">{concern}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Always show approve/reject buttons for job analysis results */}
                          <div className="pt-6 border-t">
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                              Decision Required
                            </p>
                            <div className="flex gap-3">
                              <Button 
                                onClick={handleApprove}
                                className="flex-1 bg-green-600 hover:bg-green-700"
                                disabled={candidateStatus === 'approved_for_interview'}
                              >
                                <UserCheck className="w-4 h-4 mr-2" />
                                {candidateStatus === 'approved_for_interview' ? 'Approved' : 'Approve for Interview'}
                              </Button>
                              <Button 
                                onClick={handleReject}
                                variant="destructive"
                                className="flex-1"
                                disabled={candidateStatus === 'rejected'}
                              >
                                <UserX className="w-4 h-4 mr-2" />
                                {candidateStatus === 'rejected' ? 'Rejected' : 'Reject'}
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="skills">
                      <Card>
                        <CardHeader>
                          <CardTitle>Skills Analysis</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                          <div>
                            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Key Skills</p>
                            <div className="flex flex-wrap gap-2">
                              {analysisResult.analysis.key_skills.map((skill, index) => (
                                <Badge key={index} variant="secondary">{skill}</Badge>
                              ))}
                            </div>
                          </div>

                          <Separator />

                          <div>
                            <p className="text-sm font-medium text-green-700 dark:text-green-300 mb-3">Matched Skills</p>
                            <div className="flex flex-wrap gap-2">
                              {analysisResult.analysis.matched_skills.map((skill, index) => (
                                <Badge key={index} className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                                  <CheckCircle className="w-3 h-3 mr-1" />
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          {analysisResult.analysis.missing_skills.length > 0 && (
                            <div>
                              <p className="text-sm font-medium text-red-700 dark:text-red-300 mb-3">Missing Skills</p>
                              <div className="flex flex-wrap gap-2">
                                {analysisResult.analysis.missing_skills.map((skill, index) => (
                                  <Badge key={index} className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100">
                                    <XCircle className="w-3 h-3 mr-1" />
                                    {skill}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="feedback">
                      <Card>
                        <CardHeader>
                          <CardTitle>Detailed Feedback</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ScrollArea className="h-64">
                            <p className="text-sm leading-relaxed whitespace-pre-wrap">
                              {analysisResult.analysis.detailed_feedback}
                            </p>
                          </ScrollArea>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="questions">
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <MessageSquare className="w-5 h-5" />
                            Interview Questions
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ScrollArea className="h-64">
                            <div className="space-y-4">
                              {analysisResult.analysis.interview_questions.map((question, index) => (
                                <div key={index} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                    {index + 1}. {question}
                                  </p>
                                </div>
                              ))}
                            </div>
                          </ScrollArea>
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </Tabs>
                ) : (
                  <Card>
                    <CardHeader>
                      <CardTitle>Basic Analysis</CardTitle>
                      <CardDescription>
                        Add a job description for comprehensive skill matching and detailed analysis
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Contact information extracted successfully. Upload with a job description for detailed analysis.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}