import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Eye, MapPin, DollarSign, Clock, Users, BookOpen, TrendingUp } from 'lucide-react';

interface JobDescriptionModalProps {
  job: {
    id: string;
    title: string;
    department?: string;
    location?: string;
    salaryRange?: string;
    employmentType?: string;
    description: string;
    detailedDescription?: string;
    responsibilities?: string;
    requirements?: string;
    preferredQualifications?: string;
    benefits?: string;
    companyOverview?: string;
    workEnvironment?: string;
    growthOpportunities?: string;
    skillsRequired?: string[];
    experienceLevel?: string;
    educationRequirement?: string;
  };
  trigger?: React.ReactNode;
}

export function JobDescriptionModal({ job, trigger }: JobDescriptionModalProps) {
  const [isOpen, setIsOpen] = useState(false);

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="gap-2">
      <Eye className="h-4 w-4" />
      View JD
    </Button>
  );

  const formatText = (text?: string) => {
    if (!text) return null;
    return text.split('\n').map((line, index) => (
      <p key={index} className="mb-2 last:mb-0">
        {line}
      </p>
    ));
  };

  const Section = ({ title, content, icon: Icon }: { title: string; content?: string; icon?: any }) => {
    if (!content) return null;
    
    return (
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          {Icon && <Icon className="h-5 w-5 text-blue-600" />}
          <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100">{title}</h3>
        </div>
        <div className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {formatText(content)}
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] bg-white dark:bg-gray-900">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {job.title}
          </DialogTitle>
        </DialogHeader>
        
        <ScrollArea className="pr-4 max-h-[calc(90vh-120px)]">
          <div className="space-y-6">
            {/* Job Overview */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {job.department && (
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <span className="text-sm">
                      <span className="font-medium">Department:</span> {job.department}
                    </span>
                  </div>
                )}
                {job.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-blue-600" />
                    <span className="text-sm">
                      <span className="font-medium">Location:</span> {job.location}
                    </span>
                  </div>
                )}
                {job.salaryRange && (
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-blue-600" />
                    <span className="text-sm">
                      <span className="font-medium">Salary:</span> {job.salaryRange}
                    </span>
                  </div>
                )}
                {job.employmentType && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-blue-600" />
                    <span className="text-sm">
                      <span className="font-medium">Type:</span> {job.employmentType}
                    </span>
                  </div>
                )}
              </div>
              
              {(job.experienceLevel || job.educationRequirement) && (
                <div className="mt-3 pt-3 border-t border-blue-200 dark:border-blue-700">
                  <div className="flex flex-wrap gap-2">
                    {job.experienceLevel && (
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {job.experienceLevel} level
                      </Badge>
                    )}
                    {job.educationRequirement && (
                      <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        {job.educationRequirement}
                      </Badge>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Skills Required */}
            {job.skillsRequired && job.skillsRequired.length > 0 && (
              <div className="space-y-3">
                <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100 flex items-center gap-2">
                  <BookOpen className="h-5 w-5 text-blue-600" />
                  Skills Required
                </h3>
                <div className="flex flex-wrap gap-2">
                  {job.skillsRequired.map((skill, index) => (
                    <Badge key={index} variant="outline" className="bg-gray-50 dark:bg-gray-800">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <Separator />

            {/* Company Overview */}
            <Section 
              title="Company Overview" 
              content={job.companyOverview}
              icon={Users}
            />

            {/* Role Description */}
            <Section 
              title="Role Overview" 
              content={job.detailedDescription || job.description}
            />

            {/* Key Responsibilities */}
            <Section 
              title="Key Responsibilities" 
              content={job.responsibilities}
            />

            {/* Requirements */}
            <Section 
              title="Required Qualifications" 
              content={job.requirements}
            />

            {/* Preferred Qualifications */}
            <Section 
              title="Preferred Qualifications" 
              content={job.preferredQualifications}
            />

            {/* Work Environment */}
            <Section 
              title="Work Environment" 
              content={job.workEnvironment}
              icon={MapPin}
            />

            {/* Growth Opportunities */}
            <Section 
              title="Growth & Development" 
              content={job.growthOpportunities}
              icon={TrendingUp}
            />

            {/* Benefits */}
            <Section 
              title="Benefits & Compensation" 
              content={job.benefits}
              icon={DollarSign}
            />
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}