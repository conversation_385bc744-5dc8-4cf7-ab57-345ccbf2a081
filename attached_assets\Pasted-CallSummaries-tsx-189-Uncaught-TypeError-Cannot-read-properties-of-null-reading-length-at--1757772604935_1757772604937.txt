CallSummaries.tsx:189 Uncaught TypeError: Cannot read properties of null (reading 'length')
    at CallSummaries.tsx:189:42
    at Array.map (<anonymous>)
    at CallSummaries (CallSummaries.tsx:142:24)
hook.js:608 The above error occurred in the <CallSummaries> component:

    at CallSummaries (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/src/components/CallSummaries.tsx:36:41)
    at div
    at VoiceAgent (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/src/components/VoiceAgent.tsx:33:38)
    at div
    at https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…unner/workspace/node_modules/.vite/deps/chunk-GCLFTVUM.js?v=8398ed10:42:13
    at Presence (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…unner/workspace/node_modules/.vite/deps/chunk-327G6LSZ.js?v=8398ed10:24:11)
    at https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…orkspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=8398ed10:388:13
    at _c5 (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/src/components/ui/tabs.tsx:72:12)
    at div
    at https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…unner/workspace/node_modules/.vite/deps/chunk-GCLFTVUM.js?v=8398ed10:42:13
    at Provider (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…unner/workspace/node_modules/.vite/deps/chunk-VJRUVGTI.js?v=8398ed10:38:15)
    at https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…workspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=8398ed10:266:7
    at div
    at div
    at div
    at CandidateDetailsModal (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/src/components/CandidateDetailsModal.tsx:40:3)
    at div
    at JobPostingMatches (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/src/components/JobPostingMatches.tsx:60:45)
    at EnhancedJobPostings (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/src/components/EnhancedJobPostings.tsx:115:41)
    at RenderedRoute (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…r/workspace/node_modules/.vite/deps/react-router-dom.js?v=8398ed10:5455:26)
    at Routes (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…er/workspace/node_modules/.vite/deps/react-router-dom.js?v=8398ed10:6188:3)
    at main
    at div
    at div
    at AppContent (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/src/App.tsx?v=gvGYYB1ThuHihxRBRdwzQ:48:55)
    at AccessibilityProvider (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/src/components/AccessibilityProvider.tsx:78:41)
    at CandidateProvider (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/src/contexts/CandidateContext.tsx:29:37)
    at AuthProvider (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/src/contexts/AuthContext.tsx:21:32)
    at Router (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…r/workspace/node_modules/.vite/deps/react-router-dom.js?v=8398ed10:6131:13)
    at BrowserRouter (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…er/workspace/node_modules/.vite/deps/react-router-dom.js?v=8398ed10:9149:3)
    at Provider (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…unner/workspace/node_modules/.vite/deps/chunk-VJRUVGTI.js?v=8398ed10:38:15)
    at TooltipProvider (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…rkspace/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=8398ed10:64:5)
    at QueryClientProvider (https://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.…rkspace/node_modules/.vite/deps/@tanstack_react-query.js?v=8398ed10:2805:3)
    at App

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
chunk-WERSD76P.js?v=8398ed10:9129 Uncaught TypeError: Cannot read properties of null (reading 'length')
    at CallSummaries.tsx:189:42
    at Array.map (<anonymous>)
    at CallSummaries (CallSummaries.tsx:142:24)
