import { Router } from 'express';
import { z } from 'zod';
import { db } from '../db';
import { users, organizations } from '@shared/schema';
import { eq, and, desc } from 'drizzle-orm';
import { authenticateToken, requireRole, AuthenticatedRequest } from '../auth';

const router = Router();

/**
 * @swagger
 * /organizations:
 *   get:
 *     summary: Get all organizations
 *     description: Retrieve a list of all organizations in the system
 *     tags: [Organizations]
 *     security: []
 *     responses:
 *       200:
 *         description: List of organizations
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Organization'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', async (req, res) => {
  try {
    const allOrganizations = await db
      .select({
        id: organizations.id,
        name: organizations.name,
        domain: organizations.domain,
        createdAt: organizations.createdAt,
      })
      .from(organizations)
      .orderBy(desc(organizations.createdAt));

    res.json(allOrganizations);
  } catch (error) {
    console.error('Error fetching organizations:', error);
    res.status(500).json({ error: 'Failed to fetch organizations' });
  }
});

/**
 * @swagger
 * /organizations/all:
 *   get:
 *     summary: Get all organizations (public endpoint)
 *     description: Public endpoint to retrieve all active organizations for job portal
 *     tags: [Organizations]
 *     security: []
 *     responses:
 *       200:
 *         description: List of organizations
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   domain:
 *                     type: string
 */
router.get('/all', async (req, res) => {
  try {
    const allOrganizations = await db
      .select({
        id: organizations.id,
        name: organizations.name,
        domain: organizations.domain,
      })
      .from(organizations)
      .where(eq(organizations.isActive, true))
      .orderBy(organizations.name);

    res.json(allOrganizations);
  } catch (error) {
    console.error('Error fetching all organizations:', error);
    res.status(500).json({ error: 'Failed to fetch organizations' });
  }
});

/**
 * @swagger
 * /organizations/users:
 *   get:
 *     summary: Get organization users
 *     description: Retrieve all users within the current organization (admin only)
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Organization users and info
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 users:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *                 organization:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     name:
 *                       type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin role required
 *       500:
 *         description: Internal server error
 */
router.get('/users', authenticateToken, requireRole(['admin']), async (req: AuthenticatedRequest, res) => {
  try {
    const organizationUsers = await db
      .select({
        id: users.id,
        email: users.email,
        fullName: users.fullName,
        role: users.role,
        isActive: users.isActive,
        isApproved: users.isApproved,
        createdAt: users.createdAt,
      })
      .from(users)
      .where(eq(users.organizationId, req.user!.organizationId))
      .orderBy(desc(users.createdAt));

    res.json({
      users: organizationUsers,
      organization: {
        id: req.user!.organizationId,
        name: req.user!.organizationName,
      },
    });
  } catch (error) {
    console.error('Error fetching organization users:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get organization details for sharing (admin only)
router.get('/share-info', authenticateToken, requireRole(['admin']), async (req: AuthenticatedRequest, res) => {
  try {
    const orgInfo = await db
      .select({
        id: organizations.id,
        name: organizations.name,
        domain: organizations.domain,
        createdAt: organizations.createdAt,
      })
      .from(organizations)
      .where(eq(organizations.id, req.user!.organizationId))
      .limit(1);

    if (!orgInfo.length) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    const org = orgInfo[0];
    
    // Generate shareable information
    const shareInfo = {
      organizationId: org.id,
      organizationName: org.name,
      domain: org.domain,
      registrationUrl: `${req.protocol}://${req.get('host')}/auth/register?orgId=${org.id}`,
      instructions: `To join ${org.name}, use this Organization ID when registering: ${org.id}`,
      createdAt: org.createdAt,
    };

    res.json(shareInfo);
  } catch (error) {
    console.error('Error generating share info:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get organization statistics (admin only)
router.get('/stats', authenticateToken, requireRole(['admin']), async (req: AuthenticatedRequest, res) => {
  try {
    const allUsers = await db
      .select({
        isActive: users.isActive,
        isApproved: users.isApproved,
        role: users.role,
      })
      .from(users)
      .where(eq(users.organizationId, req.user!.organizationId));

    const stats = {
      totalUsers: allUsers.length,
      activeUsers: allUsers.filter(u => u.isActive && u.isApproved).length,
      pendingApproval: allUsers.filter(u => !u.isApproved).length,
      adminUsers: allUsers.filter(u => u.role === 'admin').length,
      hrManagers: allUsers.filter(u => u.role === 'hr_manager').length,
      recruiters: allUsers.filter(u => u.role === 'recruiter').length,
      hiringManagers: allUsers.filter(u => u.role === 'hiring_manager').length,
    };

    res.json(stats);
  } catch (error) {
    console.error('Error fetching organization stats:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update user role (admin only)
router.patch('/users/:userId/role', authenticateToken, requireRole(['admin']), async (req: AuthenticatedRequest, res) => {
  try {
    const { userId } = req.params;
    const { role } = z.object({ role: z.enum(['admin', 'hr_manager', 'recruiter', 'hiring_manager']) }).parse(req.body);

    // Update user role (only within same organization)
    const result = await db
      .update(users)
      .set({ role })
      .where(and(
        eq(users.id, userId),
        eq(users.organizationId, req.user!.organizationId)
      ))
      .returning();

    if (!result.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ 
      message: 'User role updated successfully',
      user: {
        id: result[0].id,
        email: result[0].email,
        role: result[0].role,
      }
    });
  } catch (error) {
    console.error('Error updating user role:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Deactivate user (admin only)
router.patch('/users/:userId/deactivate', authenticateToken, requireRole(['admin']), async (req: AuthenticatedRequest, res) => {
  try {
    const { userId } = req.params;

    // Deactivate user (only within same organization)
    const result = await db
      .update(users)
      .set({ isActive: false })
      .where(and(
        eq(users.id, userId),
        eq(users.organizationId, req.user!.organizationId)
      ))
      .returning();

    if (!result.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ message: 'User deactivated successfully' });
  } catch (error) {
    console.error('Error deactivating user:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;