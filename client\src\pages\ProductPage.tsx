import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Calendar, 
  Users, 
  UserCheck,
  Building2,
  Search,
  Bot,
  Zap,
  Brain,
  CheckCircle,
  ArrowRight,
  Star,
  Clock,
  Target,
  Shield,
  TrendingUp
} from 'lucide-react';
import { useAuth } from "../contexts/AuthContext";
import { useNavigate } from 'react-router-dom';

export default function ProductPage() {
  const { user } = useAuth();
  const navigate = useNavigate();

  const features = [
    {
      icon: FileText,
      title: "AI-Powered Resume Screening",
      description: "Automatically analyze resumes with advanced AI to identify top candidates based on job requirements.",
      color: "bg-blue-50 text-blue-600",
      benefits: ["90% faster screening", "Bias-free evaluation", "Detailed analysis reports"]
    },
    {
      icon: Calendar,
      title: "Smart Interview Scheduling",
      description: "Automated interview coordination with Gmail integration and calendar synchronization.",
      color: "bg-green-50 text-green-600",
      benefits: ["Automated email sending", "Calendar integration", "Availability tracking"]
    },
    {
      icon: Users,
      title: "Candidate Management",
      description: "Comprehensive candidate tracking with status management and workflow automation.",
      color: "bg-purple-50 text-purple-600",
      benefits: ["Multi-stage workflow", "Real-time status updates", "Collaborative reviewing"]
    },
    {
      icon: Building2,
      title: "Job Posting Management",
      description: "Create, manage, and track job postings with intelligent candidate matching.",
      color: "bg-orange-50 text-orange-600",
      benefits: ["Smart matching", "Multi-platform posting", "Performance analytics"]
    },
    {
      icon: Search,
      title: "Career Site Builder",
      description: "Professional career site with tile-based job search interface for candidates.",
      color: "bg-indigo-50 text-indigo-600",
      benefits: ["Mobile-responsive", "Custom branding", "SEO optimized"]
    },
    {
      icon: Bot,
      title: "Workflow Automation",
      description: "Automated recruitment workflows with real-time progress tracking and notifications.",
      color: "bg-emerald-50 text-emerald-600",
      benefits: ["Process automation", "Progress visualization", "Custom triggers"]
    }
  ];

  const stats = [
    { label: "Time Saved", value: "75%", icon: Clock },
    { label: "Accuracy Improved", value: "90%", icon: Target },
    { label: "Cost Reduced", value: "60%", icon: TrendingUp },
    { label: "Security Level", value: "100%", icon: Shield }
  ];

  const pricingPlans = [
    {
      name: "Starter",
      price: "$49",
      period: "per month",
      description: "Perfect for small teams getting started",
      features: [
        "Up to 50 candidates per month",
        "Basic resume screening",
        "Email integration",
        "Standard support"
      ],
      popular: false
    },
    {
      name: "Professional",
      price: "$149",
      period: "per month",
      description: "Best for growing companies",
      features: [
        "Up to 200 candidates per month",
        "Advanced AI screening",
        "Calendar integration",
        "Workflow automation",
        "Priority support"
      ],
      popular: true
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "pricing",
      description: "For large organizations",
      features: [
        "Unlimited candidates",
        "Custom integrations",
        "Advanced analytics",
        "Dedicated support",
        "SLA guarantees"
      ],
      popular: false
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-[#7b5cff] via-[#a259e6] to-[#f3f4fa] text-white py-20">
        <div className="max-w-6xl mx-auto px-4 text-center">
          <Badge className="mb-6 bg-white/20 text-white border-white/30">
            <Zap className="w-4 h-4 mr-2" />
            AI-Powered Recruitment Platform
          </Badge>
          <h1 className="text-5xl font-bold mb-6">
            Transform Your Hiring Process
          </h1>
          <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
            HireFlow combines artificial intelligence with intuitive design to streamline your recruitment workflow. 
            Screen candidates faster, schedule interviews automatically, and make better hiring decisions.
          </p>
          <div className="flex items-center justify-center space-x-4">
            <Button 
              size="lg" 
              onClick={() => navigate('/resume-screening')}
              className="bg-white text-[#7b5cff] hover:bg-gray-100"
            >
              <Brain className="w-5 h-5 mr-2" />
              Start Screening
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
              Watch Demo
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-[#7b5cff]/10 rounded-full mb-4">
                  <stat.icon className="w-8 h-8 text-[#7b5cff]" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Hire Better
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our comprehensive platform handles every aspect of your recruitment process, 
              from initial screening to final onboarding.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader>
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4 ${feature.color}`}>
                    <feature.icon className="w-6 h-6" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                  <CardDescription className="text-gray-600">
                    {feature.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, i) => (
                      <li key={i} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Pricing Section */}
      <div className="py-20 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Choose Your Plan
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Flexible pricing options to match your team size and requirements. 
              All plans include core features with scalable add-ons.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {pricingPlans.map((plan, index) => (
              <Card key={index} className={`relative border-2 ${plan.popular ? 'border-[#7b5cff]' : 'border-gray-200'}`}>
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-[#7b5cff] text-white">
                      <Star className="w-4 h-4 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <div className="text-4xl font-bold text-gray-900 mb-2">
                    {plan.price}
                    <span className="text-lg font-normal text-gray-600">/{plan.period}</span>
                  </div>
                  <CardDescription>{plan.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-center text-sm">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Button 
                    className={`w-full ${plan.popular ? 'bg-[#7b5cff] hover:bg-[#6a4ef0]' : 'bg-gray-900 hover:bg-gray-800'}`}
                  >
                    {plan.price === 'Custom' ? 'Contact Sales' : 'Start Free Trial'}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20 bg-gradient-to-r from-[#7b5cff] to-[#a259e6] text-white">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">
            Ready to Transform Your Hiring?
          </h2>
          <p className="text-xl mb-8 text-white/90">
            Join thousands of companies already using HireFlow to find better candidates faster.
          </p>
          <div className="flex items-center justify-center space-x-4">
            <Button 
              size="lg" 
              onClick={() => navigate('/resume-screening')}
              className="bg-white text-[#7b5cff] hover:bg-gray-100"
            >
              <UserCheck className="w-5 h-5 mr-2" />
              Get Started Today
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
              Schedule Demo
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}