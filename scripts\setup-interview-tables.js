import { neon } from '@neondatabase/serverless';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function setupInterviewTables() {
  try {
    console.log('🚀 Setting up interview automation tables...');
    
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    const sql = neon(process.env.DATABASE_URL);
    
    // Read the SQL file
    const sqlScript = readFileSync(join(__dirname, 'create-interview-tables.sql'), 'utf8');

    // Split the SQL script into individual commands, handling multi-line statements
    const commands = [];
    let currentCommand = '';
    const lines = sqlScript.split('\n');

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Skip empty lines and comments
      if (!trimmedLine || trimmedLine.startsWith('--')) {
        continue;
      }

      currentCommand += line + '\n';

      // If line ends with semicolon, it's the end of a command
      if (trimmedLine.endsWith(';')) {
        commands.push(currentCommand.trim());
        currentCommand = '';
      }
    }

    // Add any remaining command
    if (currentCommand.trim()) {
      commands.push(currentCommand.trim());
    }

    // Execute each command individually
    console.log('📝 Executing SQL commands...');
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i];
      if (command.toLowerCase().includes('commit')) continue; // Skip COMMIT

      try {
        console.log(`   Executing command ${i + 1}/${commands.length}...`);
        await sql(command);
      } catch (error) {
        if (error.message.includes('already exists') || error.message.includes('duplicate')) {
          console.log(`   ⚠️  Skipping existing object: ${error.message.split(':')[1]?.trim()}`);
        } else {
          throw error;
        }
      }
    }
    
    console.log('✅ Interview automation tables created successfully!');
    console.log('📋 Created tables:');
    console.log('   - agent_profiles');
    console.log('   - interviews_v2');
    console.log('   - interview_runs');
    console.log('   - interview_artifacts');
    console.log('🎯 Default agent profiles created for all organizations');
    
  } catch (error) {
    console.error('❌ Error setting up interview tables:', error);
    process.exit(1);
  }
}

setupInterviewTables();
