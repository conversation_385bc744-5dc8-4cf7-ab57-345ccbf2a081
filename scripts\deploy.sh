#!/bin/bash

# HRMS Platform Deployment Script
# This script handles the deployment of the HRMS platform with interview automation

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}
BACKUP_ENABLED=${BACKUP_ENABLED:-true}
MONITORING_ENABLED=${MONITORING_ENABLED:-false}

echo -e "${BLUE}🚀 Starting HRMS Platform Deployment${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    echo -e "${BLUE}🔍 Checking prerequisites...${NC}"
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if environment file exists
    if [ ! -f ".env.${ENVIRONMENT}" ]; then
        print_error "Environment file .env.${ENVIRONMENT} not found."
        print_warning "Please create .env.${ENVIRONMENT} based on .env.example"
        exit 1
    fi
    
    print_status "Prerequisites check passed"
}

# Create necessary directories
create_directories() {
    echo -e "${BLUE}📁 Creating necessary directories...${NC}"
    
    mkdir -p uploads/resumes
    mkdir -p uploads/audio
    mkdir -p recordings
    mkdir -p logs
    mkdir -p backups
    mkdir -p ssl
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    
    print_status "Directories created"
}

# Setup SSL certificates (self-signed for development)
setup_ssl() {
    echo -e "${BLUE}🔒 Setting up SSL certificates...${NC}"
    
    if [ ! -f "ssl/cert.pem" ] || [ ! -f "ssl/key.pem" ]; then
        if [ "$ENVIRONMENT" = "development" ]; then
            print_warning "Creating self-signed SSL certificates for development"
            openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes \
                -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
        else
            print_error "SSL certificates not found. Please provide valid SSL certificates for production."
            print_warning "Place your certificates at ssl/cert.pem and ssl/key.pem"
            exit 1
        fi
    fi
    
    print_status "SSL certificates ready"
}

# Database backup
backup_database() {
    if [ "$BACKUP_ENABLED" = "true" ] && [ "$ENVIRONMENT" = "production" ]; then
        echo -e "${BLUE}💾 Creating database backup...${NC}"
        
        # Create backup with timestamp
        BACKUP_FILE="backups/hrms_backup_$(date +%Y%m%d_%H%M%S).sql"
        
        # Run backup if database is running
        if docker-compose ps postgres | grep -q "Up"; then
            docker-compose exec -T postgres pg_dump -U hrms_user hrms_production > "$BACKUP_FILE"
            print_status "Database backup created: $BACKUP_FILE"
        else
            print_warning "Database not running, skipping backup"
        fi
    fi
}

# Build and deploy
deploy_application() {
    echo -e "${BLUE}🏗️  Building and deploying application...${NC}"
    
    # Copy environment file
    cp ".env.${ENVIRONMENT}" .env
    
    # Build the application
    docker-compose build --no-cache app
    
    # Start the services
    if [ "$MONITORING_ENABLED" = "true" ]; then
        docker-compose --profile monitoring up -d
    else
        docker-compose up -d postgres redis app nginx
    fi
    
    print_status "Application deployed"
}

# Run database migrations
run_migrations() {
    echo -e "${BLUE}🗄️  Running database migrations...${NC}"
    
    # Wait for database to be ready
    echo "Waiting for database to be ready..."
    sleep 10
    
    # Run migrations
    docker-compose exec app npm run db:push
    
    print_status "Database migrations completed"
}

# Health check
health_check() {
    echo -e "${BLUE}🏥 Performing health check...${NC}"
    
    # Wait for application to start
    sleep 15
    
    # Check if application is responding
    if curl -f http://localhost/health > /dev/null 2>&1; then
        print_status "Application is healthy"
    else
        print_error "Application health check failed"
        echo -e "${YELLOW}Checking logs...${NC}"
        docker-compose logs app
        exit 1
    fi
}

# Display deployment information
show_deployment_info() {
    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
    echo ""
    echo -e "${BLUE}📋 Deployment Information:${NC}"
    echo -e "Environment: ${ENVIRONMENT}"
    echo -e "Application URL: https://localhost"
    echo -e "API Documentation: https://localhost/api-docs"
    echo -e "Health Check: https://localhost/health"
    
    if [ "$MONITORING_ENABLED" = "true" ]; then
        echo -e "Grafana Dashboard: http://localhost:3001"
        echo -e "Prometheus Metrics: http://localhost:9091"
    fi
    
    echo ""
    echo -e "${BLUE}🔧 Useful Commands:${NC}"
    echo -e "View logs: docker-compose logs -f app"
    echo -e "Stop services: docker-compose down"
    echo -e "Restart app: docker-compose restart app"
    echo -e "Database shell: docker-compose exec postgres psql -U hrms_user hrms_production"
    echo ""
    echo -e "${YELLOW}⚠️  Important Notes:${NC}"
    echo -e "- Update your DNS to point to this server"
    echo -e "- Configure your SSL certificates for production"
    echo -e "- Set up monitoring and alerting"
    echo -e "- Configure backup schedules"
    echo -e "- Review security settings"
}

# Cleanup function
cleanup() {
    if [ $? -ne 0 ]; then
        print_error "Deployment failed. Cleaning up..."
        docker-compose down
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main deployment flow
main() {
    check_prerequisites
    create_directories
    setup_ssl
    backup_database
    deploy_application
    run_migrations
    health_check
    show_deployment_info
}

# Run main function
main

print_status "Deployment script completed successfully!"
