import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { 
  FileText, 
  Users, 
  Calendar, 
  Building2, 
  Search,
  BarChart3,
  TrendingUp,
  Clock,
  CheckCircle
} from 'lucide-react';

export default function Dashboard() {
  const { user } = useAuth();
  const [greeting, setGreeting] = useState(() => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  });

  const quickActions = [
    {
      title: 'Resume Screening',
      description: 'Screen and analyze candidate resumes',
      icon: FileText,
      path: '/resume-screening',
      color: 'bg-blue-500'
    },
    {
      title: 'Job Postings',
      description: 'Manage job postings and candidates',
      icon: Building2,
      path: '/job-postings',
      color: 'bg-green-500'
    },
    {
      title: 'Scheduling',
      description: 'Schedule interviews and manage calendar',
      icon: Calendar,
      path: '/scheduling',
      color: 'bg-purple-500'
    },
    {
      title: 'Candidate Tracker',
      description: 'View analytics and reports',
      icon: Users,
      path: '/tracker',
      color: 'bg-orange-500'
    },
    {
      title: 'Reports',
      description: 'View analytics and reports',
      icon: BarChart3,
      path: '/reports',
      color: 'bg-red-500'
    },
    {
      title: 'Job Search',
      description: 'Browse available positions',
      icon: Search,
      path: '/candidate-search',
      color: 'bg-indigo-500'
    }
  ];

  const recentActivity = [
    {
      type: 'resume',
      title: 'New resume analyzed',
      description: 'Software Engineer position',
      time: '5 minutes ago',
      icon: FileText
    },
    {
      type: 'interview',
      title: 'Interview scheduled',
      description: 'John Smith - Frontend Developer',
      time: '1 hour ago',
      icon: Calendar
    },
    {
      type: 'candidate',
      title: 'Candidate approved',
      description: 'Sarah Johnson - UX Designer',
      time: '2 hours ago',
      icon: CheckCircle
    }
  ];

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {greeting}, {user?.full_name?.split(' ')[0] || 'User'}
        </h1>
        
      </div>

      {/* Quick Actions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {quickActions.map((action) => {
          const Icon = action.icon;
          return (
            <Link
              key={action.path}
              to={action.path}
              className="group bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200"
            >
              <div className="flex items-center mb-4">
                <div className={`p-3 rounded-lg ${action.color} text-white mr-4 pt-[4px] pb-[4px] pl-[6px] pr-[6px]`}>
                  <Icon className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-gray-900 group-hover:text-blue-600 transition-colors font-bold text-[14px]">
                    {action.title}
                  </h3>
                  <p className="text-sm text-gray-500">{action.description}</p>
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="font-semibold text-gray-900 mb-4 flex items-center text-[18px]">
          <Clock className="w-5 h-5 mr-2" />
          Recent Activity
        </h2>
        <div className="space-y-4">
          {recentActivity.map((activity, index) => {
            const Icon = activity.icon;
            return (
              <div key={index} className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="p-2 rounded-full bg-gray-100 mr-4">
                  <Icon className="w-4 h-4 text-gray-600" />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{activity.title}</h4>
                  <p className="text-sm text-gray-500">{activity.description}</p>
                </div>
                <span className="text-xs text-gray-400">{activity.time}</span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}