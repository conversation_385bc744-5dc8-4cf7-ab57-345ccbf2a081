# 🚀 AI-Powered HRMS Platform with Interview Automation

A comprehensive, multi-tenant SaaS platform for human resource management with advanced AI-powered interview automation, voice agents, and intelligent candidate screening.

## 🌟 Key Features

### 🎯 Core HRMS Platform
- **Multi-tenant Architecture**: Secure organization-based data isolation
- **Advanced Authentication**: JWT-based auth with role-based access control
- **Candidate Management**: Complete candidate lifecycle management
- **Resume Analysis**: AI-powered resume parsing and skill extraction
- **Job Portal**: Public job posting and application system

### 🤖 AI Interview Automation (NEW)
- **Automated Interview Scheduling**: Smart scheduling with calendar integration
- **AI Interview Agent**: ElevenLabs-powered conversational AI interviewer
- **Zoom Video SDK Integration**: Seamless video interview hosting
- **Bot Auto-Join**: Automated bot participation in interviews
- **Real-time Transcription**: OpenAI Whisper-powered transcription
- **Intelligent Scoring**: AI-generated competency assessments
- **Email Notifications**: Automated invitations with ICS calendar attachments

### 🎙️ Voice Agent System
- **Twilio Integration**: Outbound calling capabilities
- **ElevenLabs Voice Synthesis**: Human-like voice interactions
- **Conversation AI**: Context-aware dialogue management
- **Call Analytics**: Comprehensive call tracking and analysis

## 🏗️ System Architecture

### Backend Stack
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js with comprehensive middleware
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: JWT with bcrypt password hashing
- **AI Services**: OpenAI GPT-4, ElevenLabs, Whisper STT
- **Video**: Zoom Video SDK integration
- **Communication**: Twilio for voice, Gmail for email

### Frontend Stack
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for optimized development
- **UI Components**: Radix UI with Tailwind CSS
- **State Management**: React Context and hooks
- **Real-time**: WebSocket connections for live updates

### Interview Automation Components
- **Zoom Video SDK Service**: Token generation and session management
- **Bot Runner Service**: Puppeteer-based automation for meeting participation
- **ElevenLabs Service**: Conversational AI and voice synthesis
- **Transcription Service**: Audio processing and analysis
- **Interview Invitation Service**: Email automation with calendar integration

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- PostgreSQL database
- Required API keys (see Environment Variables)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hire-ai-workflow
   ```

2. **Install dependencies**
   ```bash
   npm install --legacy-peer-deps
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Initialize database**
   ```bash
   npm run db:push
   ```

5. **Start development server**
   ```bash
   npm run dev (OR) npx tsx server/index.ts
   ```

The application will be available at `http://localhost:5000`

## 🔧 Environment Variables

### Core Configuration
```env
NODE_ENV=development
PORT=5000
DATABASE_URL=postgresql://user:password@localhost:5432/hrms
JWT_SECRET=your-jwt-secret-key
BASE_URL=http://localhost:5000
```

### AI & Interview Services
```env
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key

# ElevenLabs Configuration
ELEVENLABS_API_KEY=your-elevenlabs-api-key
ELEVENLABS_AGENT_ID=your-agent-id

# Zoom Video SDK
ZOOM_SDK_KEY=your-zoom-sdk-key
ZOOM_SDK_SECRET=your-zoom-sdk-secret

# Gmail Integration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GMAIL_FROM_EMAIL=<EMAIL>

# Twilio (for voice calls)
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=your-twilio-number
```

## 📋 API Documentation

### Interview Automation Endpoints

#### Interviews Management
- `GET /api/interviews-v2` - List interviews
- `POST /api/interviews-v2` - Create interview
- `GET /api/interviews-v2/:id` - Get interview details
- `PUT /api/interviews-v2/:id` - Update interview
- `DELETE /api/interviews-v2/:id` - Delete interview
- `POST /api/interviews-v2/:id/send-invitation` - Send invitation
- `POST /api/interviews-v2/:id/cancel` - Cancel interview

#### Zoom Video SDK
- `GET /api/zoom/token` - Generate SDK tokens
- `POST /api/zoom/session` - Create session
- `POST /api/zoom/webhooks` - Handle Zoom events

#### Bot Automation
- `POST /api/bot-runner/start` - Start bot session
- `POST /api/bot-runner/stop` - Stop bot session
- `GET /api/bot-runner/status/:id` - Get bot status

#### ElevenLabs Integration
- `POST /api/elevenlabs/signed-url` - Get signed URL
- `POST /api/elevenlabs/conversation/start` - Start conversation
- `POST /api/elevenlabs/conversation/end` - End conversation
- `GET /api/elevenlabs/conversation/status` - Get status

#### Transcription & Analysis
- `POST /api/transcription/upload` - Upload and transcribe
- `GET /api/transcription/interview/:id` - Get transcription
- `POST /api/transcription/summary/:id` - Regenerate summary

## 🧪 Testing

### Run Tests
```bash
# All tests
npm test

# Unit tests only
npm run test:unit

# Integration tests
npm run test:integration

# End-to-end tests
npm run test:e2e

# With coverage
npm run test:coverage

# Watch mode
npm run test:watch
```

### Test Structure
- `tests/unit/` - Unit tests for services and utilities
- `tests/integration/` - API integration tests
- `tests/e2e/` - End-to-end workflow tests
- `tests/setup.ts` - Test configuration and mocks

## 🎯 Interview Automation Workflow

### 1. Setup Phase
1. Create agent profile with interview configuration
2. Schedule interview with candidate details
3. Generate Zoom Video SDK tokens

### 2. Invitation Phase
1. Send email invitation with ICS calendar attachment
2. Provide candidate join link and Zoom meeting details
3. Set up automated reminders

### 3. Interview Execution
1. Bot automatically joins Zoom meeting as host
2. ElevenLabs AI agent conducts the interview
3. Real-time audio streaming between Zoom and ElevenLabs
4. Continuous recording and transcription

### 4. Post-Interview Processing
1. Generate transcript using OpenAI Whisper
2. AI-powered competency scoring and analysis
3. Create comprehensive interview summary
4. Store all artifacts for review

## 🔒 Security Features

- **Multi-tenant Data Isolation**: Organization-scoped data access
- **JWT Authentication**: Secure token-based authentication
- **Role-based Access Control**: Admin, Member, Viewer roles
- **Input Validation**: Comprehensive request validation
- **Rate Limiting**: API rate limiting and abuse prevention
- **Secure File Handling**: Safe file upload and processing

## 📊 Monitoring & Analytics

- **Interview Metrics**: Success rates, duration, completion stats
- **AI Performance**: Transcription accuracy, response quality
- **System Health**: Service availability, error rates
- **User Analytics**: Usage patterns, feature adoption

## 🚀 Deployment

### Production Deployment
1. Set production environment variables
2. Build the application: `npm run build`
3. Start production server: `npm start`
4. Configure reverse proxy (nginx recommended)
5. Set up SSL certificates
6. Configure monitoring and logging

### Docker Deployment
```bash
# Build image
docker build -t hrms-platform .

# Run container
docker run -p 5000:5000 --env-file .env hrms-platform
```

### Environment-Specific Configurations
- **Development**: Use `.env` with local services
- **Staging**: Use `.env.staging` with staging APIs
- **Production**: Use `.env.production` with production services

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write comprehensive tests for new features
- Update documentation for API changes
- Ensure all tests pass before submitting PR

## 📚 Additional Documentation

- [Interview Automation Architecture](./docs/INTERVIEW_AUTOMATION_ARCHITECTURE.md)
- [ElevenLabs Integration Guide](./docs/ELEVENLABS_CONVERSATIONAL_AI_SETUP.md)
- [Gmail Setup Instructions](./docs/GMAIL_SETUP_INSTRUCTIONS.md)
- [Swagger API Testing](./docs/SWAGGER_TESTING_GUIDE.md)
- [Voice Architecture Flow](./docs/VOICE_ARCHITECTURE_FLOW.md)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check existing documentation
- Review API documentation at `/api-docs`

---

**Built with ❤️ for the future of AI-powered recruitment**
