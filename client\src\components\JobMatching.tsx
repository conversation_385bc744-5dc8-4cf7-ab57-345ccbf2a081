import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import CandidateDetailsModal from './CandidateDetailsModal';
import { 
  Home, 
  Briefcase, 
  Users, 
  Search, 
  MapPin, 
  Calendar,
  DollarSign,
  CheckCircle,
  Monitor,
  ChevronLeft,
  ChevronRight,
  Expand,
  X,
  FileText,
  Download,
  ExternalLink,
  Star,
  Mail,
  Phone,
  Eye,
  Filter,
  SortAsc,
  RefreshCw
} from 'lucide-react';

// Types for real data
interface JobPosting {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  department?: string;
  location?: string;
  salaryRange?: string;
  employmentType?: string;
  skillsRequired?: string[];
  experienceLevel?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface Candidate {
  id: string;
  email: string;
  fullName: string;
  phone?: string;
  location?: string;
  linkedinUrl?: string;
  resumeText?: string;
  skills?: string[];
  experienceYears?: number;
  status?: string;
  analysisResult?: any;
  overallScore?: number;
  matchScore?: number;
  recommendation?: string;
  appliedJobId?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface CandidateWithMatch extends Candidate {
  matchScore: number;
  color?: string;
  isSelected?: boolean;
}

const JobMatching: React.FC = () => {
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [selectedJobId, setSelectedJobId] = useState<string>('');
  const [candidates, setCandidates] = useState<CandidateWithMatch[]>([]);
  const [filteredCandidates, setFilteredCandidates] = useState<CandidateWithMatch[]>([]);
  const [selectedCandidate, setSelectedCandidate] = useState<CandidateWithMatch | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('matchScore');
  const [filterStatus, setFilterStatus] = useState('all');
  const { toast } = useToast();
  const [selectedTab, setSelectedTab] = useState('all');
  const [candidateTab, setCandidateTab] = useState('application');

  // Fetch job postings and candidates
  useEffect(() => {
    fetchJobPostings();
    fetchCandidates();
  }, []);

  // Filter candidates when search term or filters change
  useEffect(() => {
    let filtered = candidates;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(candidate =>
        candidate.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        candidate.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        candidate.skills?.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(candidate => candidate.status === filterStatus);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      if (sortBy === 'matchScore') {
        return (b.matchScore || 0) - (a.matchScore || 0);
      } else if (sortBy === 'name') {
        return a.fullName.localeCompare(b.fullName);
      } else if (sortBy === 'date') {
        return new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime();
      }
      return 0;
    });

    setFilteredCandidates(filtered);
  }, [candidates, searchTerm, filterStatus, sortBy]);

  const fetchJobPostings = async () => {
    try {
      const response = await fetch('/api/job-postings');
      if (response.ok) {
        const data = await response.json();
        setJobPostings(data);
        if (data.length > 0) {
          setSelectedJobId(data[0].id);
        }
      }
    } catch (error) {
      console.error('Error fetching job postings:', error);
      toast({
        title: "Error",
        description: "Failed to load job postings",
        variant: "destructive",
      });
    }
  };

  const fetchCandidates = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/candidates');
      if (response.ok) {
        const data = await response.json();
        const enhancedCandidates = data.map((candidate: Candidate, index: number) => ({
          ...candidate,
          matchScore: candidate.matchScore || candidate.overallScore || 0,
          color: getRandomColor(index),
          isSelected: false
        }));
        setCandidates(enhancedCandidates);
        if (enhancedCandidates.length > 0) {
          setSelectedCandidate(enhancedCandidates[0]);
        }
      }
    } catch (error) {
      console.error('Error fetching candidates:', error);
      toast({
        title: "Error",
        description: "Failed to load candidates",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const mapCandidatesToJob = async (jobId: string) => {
    if (!jobId) return;
    
    try {
      setIsLoading(true);
      const response = await fetch(`/api/candidates/match-job/${jobId}`, {
        method: 'POST'
      });
      
      if (response.ok) {
        const matchedCandidates = await response.json();
        const enhancedCandidates = matchedCandidates.map((candidate: Candidate, index: number) => ({
          ...candidate,
          matchScore: candidate.matchScore || candidate.overallScore || 0,
          color: getRandomColor(index),
          isSelected: false
        }));
        setCandidates(enhancedCandidates);
        toast({
          title: "Success",
          description: `Found ${matchedCandidates.length} candidates for this job posting`,
        });
      }
    } catch (error) {
      console.error('Error mapping candidates to job:', error);
      toast({
        title: "Error",
        description: "Failed to map candidates to job posting",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getRandomColor = (index: number) => {
    const colors = [
      'bg-purple-500', 'bg-blue-500', 'bg-yellow-500', 'bg-green-500', 
      'bg-red-500', 'bg-indigo-500', 'bg-pink-500', 'bg-teal-500'
    ];
    return colors[index % colors.length];
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-600';
    if (score >= 70) return 'bg-yellow-100 text-yellow-600';
    return 'bg-red-100 text-red-600';
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'approved_for_interview':
        return 'bg-green-100 text-green-800';
      case 'pending_review':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const viewCandidateDetails = (candidate: CandidateWithMatch) => {
    setSelectedCandidate(candidate);
    if (candidate.analysisResult) {
      setShowModal(true);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-600';
    if (score >= 70) return 'bg-yellow-100 text-yellow-600';
    return 'bg-red-100 text-red-600';
  };

  return (
    <div className="flex w-full min-h-screen bg-gray-50">
      {/* Left Panel */}
      <div className="w-[38%] bg-white border-r border-gray-200 pt-6 pl-8 pr-4 pb-8 shadow-md" style={{ minWidth: '480px' }}>
        {/* Header */}
        <div className="flex items-center mb-8">
          <span className="text-2xl font-bold tracking-tight text-black mr-2">recruit</span>
          <div className="flex-1"></div>
          <Button variant="ghost" size="sm" className="p-1">
            <span className="sr-only">Menu</span>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </Button>
        </div>

        {/* Navigation */}
        <div className="flex items-center space-x-8 mb-8">
          <Button variant="ghost" className="flex items-center text-gray-400 hover:text-blue-600 p-0">
            <Home className="w-4 h-4 mr-2" />
            Home
          </Button>
          <Button variant="ghost" className="flex items-center text-gray-400 hover:text-blue-600 p-0">
            <Briefcase className="w-4 h-4 mr-2" />
            Jobs
          </Button>
          <Button variant="ghost" className="flex items-center text-blue-600 border-b-2 border-blue-600 pb-1 p-0">
            <Users className="w-4 h-4 mr-2" />
            Matches
          </Button>
          <Button variant="ghost" className="flex items-center text-gray-400 hover:text-blue-600 p-0">
            <Search className="w-4 h-4 mr-2" />
            Screening
          </Button>
        </div>

        {/* Job Posting Selector */}
        <div className="mb-4">
          <Label htmlFor="job-select" className="text-sm font-medium text-gray-700">Select Job Posting</Label>
          <Select value={selectedJobId} onValueChange={setSelectedJobId}>
            <SelectTrigger className="w-full mt-2">
              <SelectValue placeholder="Select a job posting" />
            </SelectTrigger>
            <SelectContent>
              {jobPostings.map((job) => (
                <SelectItem key={job.id} value={job.id}>
                  {job.title} - {job.department || 'N/A'}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Map Candidates Button */}
        <div className="mb-4">
          <Button 
            onClick={() => mapCandidatesToJob(selectedJobId)}
            disabled={isLoading || !selectedJobId}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isLoading ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Users className="w-4 h-4 mr-2" />
            )}
            Map Candidates to Job
          </Button>
        </div>

        {/* Search and Filter Controls */}
        <div className="mb-4 space-y-2">
          <div className="flex space-x-2">
            <Input
              placeholder="Search candidates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
            <Button variant="outline" size="sm">
              <Search className="w-4 h-4" />
            </Button>
          </div>
          <div className="flex space-x-2">
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="flex-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending_review">Pending Review</SelectItem>
                <SelectItem value="approved_for_interview">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="flex-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="matchScore">Match Score</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="date">Date Added</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Candidates Count */}
        <div className="mb-4">
          <span className="text-sm text-gray-600">
            {filteredCandidates.length} candidates {selectedJobId ? 'for selected job' : 'total'}
          </span>
        </div>

        {/* Candidates List */}
        <div className="overflow-y-auto" style={{ maxHeight: '60vh' }}>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
            </div>
          ) : filteredCandidates.length > 0 ? (
            filteredCandidates.map((candidate) => (
              <div 
                key={candidate.id} 
                className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${
                  selectedCandidate?.id === candidate.id ? 'bg-blue-50 border-blue-200' : ''
                }`}
                onClick={() => setSelectedCandidate(candidate)}
              >
                <div className="flex items-center">
                  <div className={`w-10 h-10 rounded-full ${candidate.color} flex items-center justify-center text-white font-medium mr-3`}>
                    {candidate.fullName.charAt(0).toUpperCase()}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-900 font-medium">{candidate.fullName}</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getMatchScoreColor(candidate.matchScore)}`}>
                        {candidate.matchScore}%
                      </span>
                    </div>
                    <div className="flex items-center mt-1 text-sm text-gray-500">
                      <Mail className="w-3 h-3 mr-1" />
                      {candidate.email}
                    </div>
                    <div className="flex items-center justify-between mt-2">
                      <Badge className={`text-xs ${getStatusBadgeColor(candidate.status || 'pending_review')}`}>
                        {candidate.status?.replace('_', ' ').toUpperCase() || 'PENDING'}
                      </Badge>
                      <div className="flex space-x-2">
                        {candidate.analysisResult && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              viewCandidateDetails(candidate);
                            }}
                          >
                            <Eye className="w-3 h-3 mr-1" />
                            View Details
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Skills Preview */}
                {candidate.skills && candidate.skills.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-1">
                    {candidate.skills.slice(0, 3).map((skill, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                    {candidate.skills.length > 3 && (
                      <Badge variant="outline" className="text-xs text-gray-500">
                        +{candidate.skills.length - 3} more
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || filterStatus !== 'all' ? 'No candidates match your filters' : 'No candidates available'}
            </div>
          )}
        </div>
      </div>

      {/* Right Panel */}
      <div className="flex-1 bg-gray-50 flex flex-col items-center justify-start pt-8 pb-8 px-8">
        {selectedCandidate ? (
          <div className="w-full max-w-3xl bg-white rounded-2xl shadow-lg p-8 relative">
            {/* Candidate Header */}
            <div className="flex items-center mb-8">
              <div className={`w-16 h-16 rounded-full ${selectedCandidate.color} flex items-center justify-center text-white font-medium text-xl mr-5`}>
                {selectedCandidate.fullName.charAt(0).toUpperCase()}
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <span className="text-xl font-semibold text-gray-900">{selectedCandidate.fullName}</span>
                  <span className="text-sm text-gray-400">{selectedCandidate.location}</span>
                </div>
                <div className="flex items-center space-x-4 mt-2">
                  <span className="flex items-center text-green-500 text-sm font-medium">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    {selectedCandidate.matchScore}% Matched
                  </span>
                  <span className="flex items-center text-gray-400 text-sm font-medium">
                    <Briefcase className="w-4 h-4 mr-1" />
                    {selectedCandidate.experienceYears || 0} years exp
                  </span>
                  <span className="flex items-center text-gray-400 text-sm font-medium">
                    <Mail className="w-4 h-4 mr-1" />
                    {selectedCandidate.email}
                  </span>
                </div>
              </div>
              <div className="flex-1"></div>
              <div className="flex items-center space-x-3">
                {selectedCandidate.analysisResult && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex items-center"
                    onClick={() => viewCandidateDetails(selectedCandidate)}
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    View Analysis
                  </Button>
                )}
                <Button variant="outline" size="sm" className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2" />
                  Schedule Call
                </Button>
              </div>
            </div>

            {/* Candidate Details */}
            <div className="grid grid-cols-2 gap-6 mb-6">
              <div>
                <div className="text-xs text-gray-400 font-medium mb-1">Status</div>
                <Badge className={`${getStatusBadgeColor(selectedCandidate.status || 'pending_review')}`}>
                  {selectedCandidate.status?.replace('_', ' ').toUpperCase() || 'PENDING'}
                </Badge>
              </div>
              <div>
                <div className="text-xs text-gray-400 font-medium mb-1">Match Score</div>
                <div className="text-sm text-gray-900 font-medium">{selectedCandidate.matchScore}%</div>
              </div>
              <div>
                <div className="text-xs text-gray-400 font-medium mb-1">Experience</div>
                <div className="text-sm text-gray-900 font-medium">{selectedCandidate.experienceYears || 0} years</div>
              </div>
              <div>
                <div className="text-xs text-gray-400 font-medium mb-1">Phone</div>
                <div className="text-sm text-gray-900 font-medium">{selectedCandidate.phone || 'N/A'}</div>
              </div>
            </div>

            {/* Skills Section */}
            {selectedCandidate.skills && selectedCandidate.skills.length > 0 && (
              <div className="mb-6">
                <div className="text-sm font-semibold text-gray-900 mb-3">Skills</div>
                <div className="flex flex-wrap gap-2">
                  {selectedCandidate.skills.map((skill, index) => (
                    <Badge key={index} variant="outline" className="text-sm">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Resume Text Preview */}
            {selectedCandidate.resumeText && (
              <div className="mb-6">
                <div className="text-sm font-semibold text-gray-900 mb-3">Resume Preview</div>
                <div className="bg-gray-50 p-4 rounded-lg text-sm text-gray-600 max-h-40 overflow-y-auto">
                  {selectedCandidate.resumeText.substring(0, 500)}...
                </div>
              </div>
            )}

            {/* LinkedIn Profile */}
            {selectedCandidate.linkedinUrl && (
              <div className="mb-6">
                <div className="text-sm font-semibold text-gray-900 mb-3">LinkedIn Profile</div>
                <a 
                  href={selectedCandidate.linkedinUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center text-blue-600 hover:text-blue-800"
                >
                  <ExternalLink className="w-4 h-4 mr-1" />
                  View Profile
                </a>
              </div>
            )}
          </div>
        ) : (
          <div className="w-full max-w-3xl bg-white rounded-2xl shadow-lg p-8 text-center">
            <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Select a Candidate</h3>
            <p className="text-gray-600">
              Choose a candidate from the list to view their profile and details
            </p>
          </div>
        )}
      </div>

      {/* Candidate Details Modal */}
      {selectedCandidate && (
        <CandidateDetailsModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          analysisResult={{
            contactInfo: {
              name: selectedCandidate.fullName,
              email: selectedCandidate.email,
              phone: selectedCandidate.phone,
              location: selectedCandidate.location,
              linkedin: selectedCandidate.linkedinUrl
            },
            analysis: selectedCandidate.analysisResult,
            extractedText: selectedCandidate.resumeText || '',
            method: 'database',
            hasJobAnalysis: true
          }}
          fileName={`${selectedCandidate.fullName}_Resume.pdf`}
        />
      )}
    </div>
  );
};

export default JobMatching;