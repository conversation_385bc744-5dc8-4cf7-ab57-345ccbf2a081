now agent is speaking static message , when candidate respond back agent is keep speaking same static message continously in loop
It looks like agent is stay in call and continuing the conversation but it is keep repeating same static message in loop.

could you check recent log message and fix the issue.

🎯 STEP 4: Configuration sent - using ElevenLabs portal agent settings
🎯 STEP 4: Agent will speak portal static message automatically when first audio arrives
🎯🎯🎯 STEP 4: RAW MESSAGE FROM ELEVENLABS RECEIVED! 🎯🎯🎯
🎯 STEP 4: Raw data type: object
🎯 STEP 4: Raw data length: 218
🎯 STEP 4: Raw data preview: {"conversation_initiation_metadata_event":{"conversation_id":"conv_3501k3xg311despar920dfh80bn8","agent_output_audio_format":"ulaw_8000","user_input_audio_format":"pcm_16000"},"type":"conversation_ini
📨 EL Message type: conversation_initiation_metadata
🔍 EL Full Message: {
  "conversation_initiation_metadata_event": {
    "conversation_id": "conv_3501k3xg311despar920dfh80bn8",
    "agent_output_audio_format": "ulaw_8000",
    "user_input_audio_format": "pcm_16000"
  },
  "type": "conversation_initiation_metadata"
}
🔧 STEP 4: ElevenLabs conversation initialized successfully!
🔧 STEP 4: Conversation metadata: {
  "conversation_initiation_metadata_event": {
    "conversation_id": "conv_3501k3xg311despar920dfh80bn8",
    "agent_output_audio_format": "ulaw_8000",
    "user_input_audio_format": "pcm_16000"
  },
  "type": "conversation_initiation_metadata"
}
🎯 STEP 4: Agent should now speak the first_message automatically
📨 STEP 4: Received message type: media
📨 STEP 4: Received message type: media
📨 STEP 4: Received message type: media
📨 STEP 4: Received message type: media
📨 STEP 4: Received message type: media
📨 STEP 4: Received message type: media
📨 STEP 4: Received message type: media
📨 STEP 4: Received message type: media
📨 STEP 4: Received message type: media
📨 STEP 4: Received message type: media
📨 STEP 4: Received message type: media
🎯🎯🎯 STEP 4: RAW MESSAGE FROM ELEVENLABS RECEIVED! 🎯🎯🎯




📨 STEP 4: Received message type: media
🎯🎯🎯 STEP 4: RAW MESSAGE FROM ELEVENLABS RECEIVED! 🎯🎯🎯
🎯 STEP 4: Raw data type: object
🎯 STEP 4: Raw data length: 100
🎯 STEP 4: Raw data preview: {"agent_response_event":{"agent_response":"Hello! I am Sarah from Steorra"},"type":"agent_response"}
📨 EL Message type: agent_response
🔍 EL Full Message: {
  "agent_response_event": {
    "agent_response": "Hello! I am Sarah from Steorra"
  },
  "type": "agent_response"
}
🤖 STEP 4: Agent response received: No text
🤖 STEP 4: Agent response full data: {
  "agent_response_event": {
    "agent_response": "Hello! I am Sarah from Steorra"
  },
  "type": "agent_response"
}
