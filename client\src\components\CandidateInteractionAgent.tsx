
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Calendar, Clock, Mail, User, CheckCircle, Calendar as CalendarIcon, MessageSquare } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import GmailAuth from './GmailAuth';

interface ApprovedCandidate {
  id: string;
  name: string;
  email: string;
  position?: string;
  match_score?: number;
  status: string;
}

interface CandidateInteractionAgentProps {
  approvedCandidates?: ApprovedCandidate[];
}

const CandidateInteractionAgent: React.FC<CandidateInteractionAgentProps> = ({ 
  approvedCandidates = [] 
}) => {
  const [selectedCandidate, setSelectedCandidate] = useState<any>(null);
  const [emailTemplate, setEmailTemplate] = useState('');
  const [candidateAvailability, setCandidateAvailability] = useState('');
  const [managerAvailability, setManagerAvailability] = useState('');
  const [scheduledInterview, setScheduledInterview] = useState<any>(null);
  const [isEmailSending, setIsEmailSending] = useState(false);
  const { toast } = useToast();

  // Use provided approved candidates or fallback to mock data
  const candidatesToShow = approvedCandidates.length > 0 ? approvedCandidates : [
    {
      id: "1",
      name: "John Doe",
      email: "<EMAIL>",
      position: "Senior Frontend Developer",
      match_score: 8,
      status: "pending_schedule"
    },
    {
      id: "2", 
      name: "Sarah Johnson",
      email: "<EMAIL>",
      position: "Backend Engineer", 
      match_score: 9,
      status: "pending_schedule"
    },
    {
      id: "3",
      name: "Mike Chen", 
      email: "<EMAIL>",
      position: "Full Stack Developer",
      match_score: 7,
      status: "pending_schedule"
    }
  ];

  const generateEmailTemplate = (candidate: any) => {
    const template = `Subject: Interview Invitation - ${candidate.position || 'Position'} Role

Dear ${candidate.name},

Thank you for your interest in the ${candidate.position || 'position'} at our company. We were impressed with your background and would like to invite you for an interview.

Please reply with your availability for the following time slots:
- Option 1: Monday 2:00 PM - 3:00 PM
- Option 2: Tuesday 10:00 AM - 11:00 AM 
- Option 3: Wednesday 3:00 PM - 4:00 PM

We look forward to speaking with you!

Best regards,
HR Team`;

    setEmailTemplate(template);
    console.log("Generated email template for:", candidate.name);
  };

  const sendInitialEmail = async (candidate: any) => {
    console.log("Sending initial email to:", candidate.email);
    setIsEmailSending(true);
    
    try {
      const { data, error } = await supabase.functions.invoke('send-interview-email', {
        body: {
          candidateName: candidate.name,
          candidateEmail: candidate.email,
          position: candidate.position || 'Position',
          emailType: 'invitation'
        }
      });

      if (error) {
        throw error;
      }

      generateEmailTemplate(candidate);
      
      toast({
        title: "Email sent successfully",
        description: `Interview invitation sent to ${candidate.name}`,
      });
      
      // Simulate candidate response after 3 seconds
      setTimeout(() => {
        setCandidateAvailability("Monday 2PM-4PM, Tuesday 10AM-12PM, Wednesday 3PM-5PM");
        toast({
          title: "Candidate responded",
          description: `${candidate.name} provided their availability`,
        });
      }, 3000);
      
    } catch (error: any) {
      console.error('Error sending email:', error);
      toast({
        title: "Error sending email",
        description: error.message || "Failed to send email",
        variant: "destructive"
      });
    } finally {
      setIsEmailSending(false);
    }
  };

  const checkManagerAvailability = () => {
    console.log("Checking hiring manager calendar...");
    
    // Simulate calendar check
    setTimeout(() => {
      setManagerAvailability("Monday 2PM-3PM (busy), Tuesday 10AM-11AM (available), Wednesday 3PM-4PM (available)");
      toast({
        title: "Manager calendar checked",
        description: "Found available time slots",
      });
    }, 1500);
  };

  const scheduleInterview = async (timeSlot: string) => {
    const interview = {
      candidate: selectedCandidate,
      time: timeSlot,
      status: "scheduled",
      meetingLink: "https://meet.google.com/abc-defg-hij"
    };
    
    setScheduledInterview(interview);
    console.log("Interview scheduled:", interview);
    
    // Send confirmation email
    try {
      const { data, error } = await supabase.functions.invoke('send-interview-email', {
        body: {
          candidateName: selectedCandidate.name,
          candidateEmail: selectedCandidate.email,
          position: selectedCandidate.position || 'Position',
          emailType: 'confirmation',
          scheduledTime: timeSlot,
          meetingLink: interview.meetingLink
        }
      });

      if (error) {
        throw error;
      }

      toast({
        title: "Interview scheduled & confirmed",
        description: `Interview with ${selectedCandidate?.name} scheduled for ${timeSlot}. Confirmation email sent.`,
      });
      
    } catch (error: any) {
      console.error('Error sending confirmation email:', error);
      toast({
        title: "Interview scheduled",
        description: `Interview with ${selectedCandidate?.name} scheduled for ${timeSlot}. Note: Confirmation email failed to send.`,
        variant: "destructive"
      });
    }
  };

  const sendReminder = async () => {
    if (!scheduledInterview) return;
    
    try {
      const { data, error } = await supabase.functions.invoke('send-interview-email', {
        body: {
          candidateName: scheduledInterview.candidate.name,
          candidateEmail: scheduledInterview.candidate.email,
          position: scheduledInterview.candidate.position || 'Position',
          emailType: 'reminder',
          scheduledTime: scheduledInterview.time,
          meetingLink: scheduledInterview.meetingLink
        }
      });

      if (error) {
        throw error;
      }

      toast({
        title: "Reminder sent",
        description: `Interview reminder sent to ${scheduledInterview.candidate.name}`,
      });
      
    } catch (error: any) {
      console.error('Error sending reminder:', error);
      toast({
        title: "Error sending reminder",
        description: error.message || "Failed to send reminder",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Gmail Authentication */}
      <GmailAuth />
      
      {/* Approved Candidates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="w-5 h-5 mr-2" />
            Approved Candidates - Ready for Scheduling
          </CardTitle>
          <CardDescription>
            {approvedCandidates.length > 0 
              ? "Candidates who passed the resume screening and need interview scheduling"
              : "No approved candidates yet. Candidates who pass resume screening will appear here."
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {candidatesToShow.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <User className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No approved candidates available for scheduling.</p>
              <p className="text-sm">Approve candidates from the Resume Screening tab first.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {candidatesToShow.map((candidate) => (
                <Card 
                  key={candidate.id} 
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedCandidate?.id === candidate.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => setSelectedCandidate(candidate)}
                >
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold">{candidate.name}</h3>
                      {candidate.match_score && (
                        <Badge variant="outline">Score: {candidate.match_score}/10</Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{candidate.position || 'Position TBD'}</p>
                    <p className="text-xs text-gray-500">{candidate.email}</p>
                    <Badge className="mt-2 text-xs">
                      <Clock className="w-3 h-3 mr-1" />
                      Pending Schedule
                    </Badge>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Scheduling Workflow */}
      {selectedCandidate && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Interview Scheduling - {selectedCandidate.name}
            </CardTitle>
            <CardDescription>
              Automated scheduling workflow with email integration
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Step 1: Send Initial Email */}
            <div className="border rounded-lg p-4">
              <h4 className="font-medium mb-3 flex items-center">
                <Mail className="w-4 h-4 mr-2" />
                Step 1: Send Interview Invitation
              </h4>
              
              {emailTemplate ? (
                <div className="space-y-3">
                  <Textarea 
                    value={emailTemplate}
                    onChange={(e) => setEmailTemplate(e.target.value)}
                    rows={8}
                    className="text-sm"
                  />
                  <div className="text-sm text-green-600">
                    ✓ Email sent successfully to {selectedCandidate.email}
                  </div>
                </div>
              ) : (
                <Button 
                  onClick={() => sendInitialEmail(selectedCandidate)}
                  disabled={isEmailSending}
                >
                  {isEmailSending ? (
                    <>
                      <Clock className="w-4 h-4 mr-2 animate-spin" />
                      Sending Email...
                    </>
                  ) : (
                    <>
                      <MessageSquare className="w-4 h-4 mr-2" />
                      Send Interview Invitation
                    </>
                  )}
                </Button>
              )}
            </div>

            {/* Step 2: Candidate Response */}
            {candidateAvailability && (
              <div className="border rounded-lg p-4 bg-blue-50">
                <h4 className="font-medium mb-3 flex items-center">
                  <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                  Step 2: Candidate Availability Received
                </h4>
                <div className="bg-white p-3 rounded border">
                  <p className="text-sm font-medium mb-1">From: {selectedCandidate.email}</p>
                  <p className="text-sm">{candidateAvailability}</p>
                </div>
                
                {!managerAvailability && (
                  <Button onClick={checkManagerAvailability} className="mt-3">
                    <CalendarIcon className="w-4 h-4 mr-2" />
                    Check Manager Calendar
                  </Button>
                )}
              </div>
            )}

            {/* Step 3: Manager Calendar */}
            {managerAvailability && (
              <div className="border rounded-lg p-4 bg-green-50">
                <h4 className="font-medium mb-3 flex items-center">
                  <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                  Step 3: Manager Calendar Checked
                </h4>
                <div className="bg-white p-3 rounded border mb-3">
                  <p className="text-sm">{managerAvailability}</p>
                </div>
                
                <div className="space-y-2">
                  <h5 className="font-medium text-sm">Available Time Slots:</h5>
                  <div className="space-y-2">
                    <Button 
                      onClick={() => scheduleInterview("Tuesday 10AM-11AM")}
                      variant="outline" 
                      size="sm"
                      className="w-full justify-start"
                    >
                      Tuesday 10AM-11AM (Both Available)
                    </Button>
                    <Button 
                      onClick={() => scheduleInterview("Wednesday 3PM-4PM")}
                      variant="outline" 
                      size="sm"
                      className="w-full justify-start"
                    >
                      Wednesday 3PM-4PM (Both Available)
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Interview Scheduled */}
            {scheduledInterview && (
              <div className="border rounded-lg p-4 bg-green-100">
                <h4 className="font-medium mb-3 flex items-center">
                  <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                  Interview Successfully Scheduled
                </h4>
                <div className="space-y-2 text-sm">
                  <p><strong>Candidate:</strong> {scheduledInterview.candidate.name}</p>
                  <p><strong>Time:</strong> {scheduledInterview.time}</p>
                  <p><strong>Meeting Link:</strong> {scheduledInterview.meetingLink}</p>
                  <p><strong>Status:</strong> Confirmation emails sent to both parties</p>
                </div>
                
                <div className="flex space-x-2 mt-4">
                  <Button size="sm" variant="outline" onClick={sendReminder}>
                    <Mail className="w-4 h-4 mr-2" />
                    Send Reminder
                  </Button>
                  <Button size="sm" variant="outline">
                    <Calendar className="w-4 h-4 mr-2" />
                    Add to Calendar
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CandidateInteractionAgent;
