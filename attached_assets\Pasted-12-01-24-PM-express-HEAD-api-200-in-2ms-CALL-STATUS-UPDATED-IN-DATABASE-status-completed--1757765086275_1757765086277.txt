12:01:24 PM [express] HEAD /api 200 in 2ms
✅ CALL STATUS UPDATED IN DATABASE: { status: 'completed', endedAt: 2025-09-13T12:01:24.882Z, duration: 4 }
📊 Full Status Data: {
  callSid: 'CA76ae54519f4a6ec447b6401cc3f07e93',
  callStatus: 'completed',
  answeredBy: undefined,
  duration: '4',
  timestamp: '2025-09-13T12:01:25.083Z',
  from: '+14089006967',
  to: '+14086219491',
  direction: 'outbound-api'
}
12:01:25 PM [express] POST /api/twilio-status 200 in 268ms
12:01:25 PM [express] HEAD /api 200 in 2ms
📞 STEP 4: Twilio WebSocket CLOSED for call: CA76ae54519f4a6ec447b6401cc3f07e93
📊 Final stats - Frames: 11673 Duration: 234282ms
12:01:25 PM [express] HEAD /api 200 in 5ms
⏰ 3 seconds elapsed since user speech - checking if agent responded...
12:01:26 PM [express] HEAD /api 200 in 2ms
12:01:26 PM [express] HEAD /api 200 in 5ms
12:01:27 PM [express] HEAD /api 200 in 2ms
12:01:27 PM [express] HEAD /api 200 in 2ms
12:01:28 PM [express] HEAD /api 200 in 3ms
12:01:28 PM [express] HEAD /api 200 in 2ms
12:01:29 PM [express] HEAD /api 200 in 3ms
12:01:29 PM [express] HEAD /api 200 in 3ms
12:01:30 PM [express] HEAD /api 200 in 2ms
12:01:30 PM [express] HEAD /api 200 in 3ms
🏁 ElevenLabs Conversation Ended Webhook: {
  type: 'post_call_transcription',
  event_timestamp: 1757764890,
  data: {
    agent_id: 'agent_8901k3kvyy39fyt9jhfq6fhjtxrk',
    conversation_id: 'conv_0901k51f02jzeemsswe2svp4e75y',
    status: 'done',
    user_id: null,
    transcript: [
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object],
      [Object], [Object], [Object], [Object]
    ],
    metadata: {
      start_time_unix_secs: 1757764651,
      accepted_time_unix_secs: 1757764651,
      call_duration_secs: 232,
      cost: 1572,
      deletion_settings: [Object],
      feedback: [Object],
      authorization_method: 'signed_url',
      charging: [Object],
      phone_call: null,
      batch_call: null,
      termination_reason: 'end_call tool was called.',
      error: null,
      main_language: 'en',
      rag_usage: null,
      text_only: false,
      features_usage: [Object],
      eleven_assistant: [Object],
      initiator_id: null,
      conversation_initiation_source: 'unknown',
      conversation_initiation_source_version: null,
      timezone: null
    },
    analysis: {
      evaluation_criteria_results: {},
      data_collection_results: {},
      call_successful: 'success',
      transcript_summary: "Sarah from Demo ATS Company contacted Morley to discuss the VP - Enterprise Engineering position. Morley expressed interest and discussed his experience. They discussed salary expectations and scheduled a technical interview for September 20th at 2 p.m. Eastern. Morley inquired about the role's background and specific technologies required. The call concluded with Morley thanking Sarah.\n",
      call_summary_title: 'VP Engineering Screening'
    },
    conversation_initiation_client_data: {
      conversation_config_override: [Object],
      custom_llm_extra_body: {},
      user_id: null,
      source_info: [Object],
      dynamic_variables: [Object]
    }
  }
}
12:01:30 PM [express] POST /api/elevenlabs/conversation-ended 200 in 4ms :: {"success":true}