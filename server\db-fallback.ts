// Temporary in-memory fallback for testing when database is disabled
import * as schema from '../shared/schema';

// Mock data for testing Phase 2 admin dashboard
export const mockOrganizations = [
  {
    id: 'org-1',
    name: 'TestCorp Phase 2',
    domain: 'testcorp-phase2.com',
    isActive: true,
    allowSelfRegistration: true,
    requireApprovalForNewUsers: true,
    defaultUserRole: 'recruiter' as const,
    emailDomainRestriction: null,
    maxUsers: 100,
    customBranding: {
      logoUrl: null,
      primaryColor: '#2563eb',
      companyName: 'TestCorp Phase 2'
    },
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

export const mockUsers = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    fullName: 'Phase 2 Admin',
    hashedPassword: 'hashed_password_here',
    role: 'admin' as const,
    organizationId: 'org-1',
    isActive: true,
    isApproved: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    fullName: 'Test Recruiter',
    hashedPassword: 'hashed_password_here',
    role: 'recruiter' as const,
    organizationId: 'org-1',
    isActive: true,
    isApproved: false,
    lastLogin: null,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

export const mockJobPostings = [
  {
    id: 'job-1',
    title: 'Senior Developer',
    description: 'Looking for experienced developer',
    organizationId: 'org-1',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

export const mockCandidates = [
  {
    id: 'candidate-1',
    name: 'John Doe',
    email: '<EMAIL>',
    status: 'pending_review' as const,
    organizationId: 'org-1',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Mock database interface for testing
export const mockDb = {
  select: () => ({
    from: () => ({
      where: () => ({
        groupBy: () => Promise.resolve([
          { total: 2, role: 'admin', isActive: true, isApproved: true },
          { total: 1, role: 'recruiter', isActive: true, isApproved: false }
        ]),
        orderBy: () => ({
          limit: () => Promise.resolve(mockJobPostings)
        })
      }),
      groupBy: () => Promise.resolve([
        { total: 1, isActive: true },
        { total: 1, status: 'pending_review' }
      ]),
      orderBy: () => Promise.resolve(mockUsers),
      limit: () => Promise.resolve(mockOrganizations)
    })
  }),
  update: () => ({
    set: () => ({
      where: () => ({
        returning: () => Promise.resolve([mockUsers[0]])
      })
    })
  })
};

export const createMockToken = () => {
  // Create a simple JWT-like token for testing
  const payload = {
    id: 'user-1',
    email: '<EMAIL>',
    role: 'admin',
    organizationId: 'org-1',
    organizationName: 'TestCorp Phase 2',
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 86400 // 24 hours
  };
  
  return 'mock-jwt-token-for-testing-' + Buffer.from(JSON.stringify(payload)).toString('base64');
};

console.log('🔧 Mock database initialized for testing Phase 2 admin dashboard');
console.log('📝 Test admin token:', createMockToken());