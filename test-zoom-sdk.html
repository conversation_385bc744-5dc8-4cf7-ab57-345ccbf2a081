<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zoom SDK Test</title>
</head>
<body>
    <h1>Zoom Video SDK Loading Test</h1>
    <div id="status">Testing SDK loading...</div>
    <div id="results"></div>

    <script>
        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('results');
        
        function updateStatus(message) {
            statusDiv.innerHTML = message;
            console.log(message);
        }
        
        function addResult(message, isError = false) {
            const p = document.createElement('p');
            p.innerHTML = message;
            p.style.color = isError ? 'red' : 'green';
            resultsDiv.appendChild(p);
            console.log(message);
        }

        // Test multiple SDK versions
        const sdkVersions = [
            'https://source.zoom.us/videosdk/zoom-video-1.9.8.min.js',
            'https://source.zoom.us/videosdk/zoom-video-1.9.7.min.js',
            'https://source.zoom.us/videosdk/zoom-video-1.9.6.min.js',
            'https://unpkg.com/@zoom/videosdk@1.9.8/dist/index.js',
            'https://unpkg.com/@zoom/videosdk@1.9.7/dist/index.js'
        ];

        async function testSDKVersion(url) {
            return new Promise((resolve) => {
                updateStatus(`Testing: ${url}`);
                
                const script = document.createElement('script');
                script.src = url;
                
                const timeout = setTimeout(() => {
                    addResult(`❌ Timeout loading: ${url}`, true);
                    resolve(false);
                }, 10000);
                
                script.onload = () => {
                    clearTimeout(timeout);
                    if (window.ZoomVideo) {
                        addResult(`✅ Successfully loaded: ${url}`);
                        addResult(`✅ ZoomVideo object available: ${typeof window.ZoomVideo}`);
                        resolve(true);
                    } else {
                        addResult(`❌ Loaded but ZoomVideo not available: ${url}`, true);
                        resolve(false);
                    }
                };
                
                script.onerror = (error) => {
                    clearTimeout(timeout);
                    addResult(`❌ Failed to load: ${url} - ${error}`, true);
                    resolve(false);
                };
                
                document.head.appendChild(script);
            });
        }

        async function testAllVersions() {
            updateStatus('Starting SDK version tests...');
            
            for (const url of sdkVersions) {
                const success = await testSDKVersion(url);
                if (success) {
                    updateStatus('✅ Found working SDK version!');
                    return;
                }
                
                // Wait a bit between tests
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            updateStatus('❌ No working SDK versions found');
            addResult('❌ All SDK versions failed to load', true);
        }

        // Start testing
        testAllVersions();
    </script>
</body>
</html>
