import axios from 'axios';
import WebSocket from 'ws';
import { EventEmitter } from 'events';
import OpenAI from 'openai';

interface ElevenLabsVoiceSettings {
  stability: number;
  similarity_boost: number;
  style?: number;
  use_speaker_boost?: boolean;
}

interface ElevenLabsResponse {
  success: boolean;
  audioUrl?: string;
  error?: string;
}

interface ConversationSession {
  sessionId: string;
  websocket: WebSocket;
  conversationId?: string;
  isActive: boolean;
  startedAt: Date;
  agentId: string;
}

interface ConversationConfig {
  agentId: string;
  conversationConfig?: {
    agent: {
      prompt: {
        prompt: string;
      };
    };
  };
}

// Twilio Integration Interfaces
interface ConversationalSettings {
  model?: string;
  voiceId?: string;
  temperature?: number;
  maxTokens?: number;
  conversationContext?: string;
  personality?: 'professional' | 'friendly' | 'warm' | 'authoritative';
}

interface ConversationMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
  timestamp?: Date;
}

interface ConversationState {
  messages: ConversationMessage[];
  candidateInfo?: {
    name: string;
    position: string;
    experience: string;
    skills: string[];
    currentCompany: string;
    education: string;
    location: string;
    experienceYears: number;
    matchScore: number;
    aiSummary: string;
  };
  jobInfo?: {
    title: string;
    description: string;
    responsibilities: string;
    requirements: string;
    preferredQualifications: string;
    benefits: string;
    workEnvironment: string;
    salaryRange: string;
    location: string;
    experienceLevel: string;
    skillsRequired: string[];
  };
  organizationInfo?: {
    name: string;
    domain: string;
    companyOverview: string;
    customBranding: any;
  };
  stage: 'opening' | 'qualification' | 'scheduling' | 'closing' | 'completed';
  callPurpose: 'screening' | 'interview_scheduling' | 'follow_up' | 'offer_discussion';
}

interface ConversationalResponse {
  success: boolean;
  response?: string;
  audioUrl?: string;
  nextAction?: 'continue' | 'schedule' | 'end_call' | 'transfer' | 'collect_info';
  conversationState?: ConversationState;
  error?: string;
}

class ElevenLabsService extends EventEmitter {
  private apiKey: string;
  private baseUrl = 'https://api.elevenlabs.io/v1';
  private conversationSessions: Map<string, ConversationSession> = new Map();
  private openai: OpenAI;
  
  // Popular voice IDs from ElevenLabs
  private voices = {
    rachel: 'JBFqnCBsd6RMkjVDRZzb', // Professional female voice
    sarah: 'EXAVITQu4vr4xnSDxMaL', // Warm female voice
    adam: '21m00Tcm4TlvDq8ikWAM', // Professional male voice
    antoni: 'ErXwobaYiN019PkySvjV', // Natural male voice
    arnold: 'VR6AewLTigWG4xSOukaG', // Authoritative male voice
    callum: 'N2lVS1w4EtoT3dr4eOWO', // British male voice
    domi: 'AZnzlk1XvdvUeBnXmlld', // Strong female voice
    elli: 'MF3mGyEYCl7XYWbV9V6O', // Emotional female voice
    ethan: 'g5CIjZEefAph4nQFvHAz', // Young male voice
    freya: 'jsCqWAovK2LkecY7zXl4', // Young female voice
    gigi: 'jBpfuIE2acCO8z3wKNLl', // Childlike female voice
    giovanni: 'zcAOhNBS3c14rBihAFp1', // English-Italian male voice
    glinda: 'z9fAnlkpzviPz146aGWa', // Witch-like female voice
    grace: 'oWAxZDx7w5VEj9dCyTzz', // Elegant female voice
    james: 'ZQe5CZNOzWyzPSCn5a3c', // Calm male voice
    jeremy: 'bVMeCyTHy58xNoL34h3p', // Excited male voice
    jessie: 't0jbNlBVZ17f02VDIeMI', // Confident female voice
    liam: 'TX3LPaxmHKxFdv7VOQHJ', // Male narrator voice
    matilda: 'XrExE9yKIg1WjnnlVkGX', // Mature female voice
    nicole: 'piTKgcLEGmPE4e6mEKli', // Whisper female voice
    paul: 'pMsXgVXv3BLzUgSXRplE', // Middle-aged male voice
    sam: 'yoZ06aMxZJJ28mfd3POQ', // Professional announcer male voice
    serena: 'pMsXgVXv3BLzUgSXRplE', // Female news anchor voice
    thomas: 'GBv7mTt0atIp3Br8iCZE' // British narrator male voice
  };

  constructor() {
    super();
    this.apiKey = process.env.ELEVENLABS_API_KEY || '';
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY || ''
    });

    if (!this.apiKey) {
      console.warn('⚠️ ElevenLabs API key not configured');
    } else {
      console.log('🎙️ ElevenLabs service initialized');
    }
  }

  isConfigured(): boolean {
    return !!this.apiKey;
  }

  async generateSpeech(
    text: string, 
    voiceId: string = this.voices.rachel,
    options: {
      model?: string;
      voiceSettings?: ElevenLabsVoiceSettings;
      outputFormat?: string;
      optimizeStreaming?: number;
    } = {}
  ): Promise<ElevenLabsResponse> {
    if (!this.isConfigured()) {
      return {
        success: false,
        error: 'ElevenLabs API key not configured'
      };
    }

    try {
      const {
        model = 'eleven_multilingual_v2',
        voiceSettings = {
          stability: 0.5,
          similarity_boost: 0.8,
          style: 0.0,
          use_speaker_boost: true
        },
        outputFormat = 'mp3_44100_128',
        optimizeStreaming = 2
      } = options;

      console.log(`🎤 ElevenLabs: Generating speech for "${text.substring(0, 50)}..." using voice ${voiceId}`);

      const response = await axios.post(
        `${this.baseUrl}/text-to-speech/${voiceId}`,
        {
          text,
          model_id: model,
          voice_settings: voiceSettings,
          output_format: outputFormat,
          optimize_streaming_latency: optimizeStreaming
        },
        {
          headers: {
            'Accept': 'audio/mpeg',
            'Content-Type': 'application/json',
            'xi-api-key': this.apiKey
          },
          responseType: 'arraybuffer'
        }
      );

      // Convert audio buffer to base64
      const audioBuffer = Buffer.from(response.data);
      const audioBase64 = audioBuffer.toString('base64');
      const audioUrl = `data:audio/mpeg;base64,${audioBase64}`;

      console.log(`✅ ElevenLabs: Successfully generated ${audioBuffer.length} bytes of audio`);

      return {
        success: true,
        audioUrl
      };
    } catch (error: any) {
      console.error('❌ ElevenLabs API error:', error.response?.data || error.message);
      
      if (error.response?.status === 401) {
        return {
          success: false,
          error: 'Invalid ElevenLabs API key'
        };
      } else if (error.response?.status === 429) {
        return {
          success: false,
          error: 'ElevenLabs rate limit exceeded'
        };
      } else {
        return {
          success: false,
          error: `ElevenLabs API error: ${error.message}`
        };
      }
    }
  }

  async generateSpeechStream(
    text: string,
    voiceId: string = this.voices.rachel,
    options: {
      model?: string;
      voiceSettings?: ElevenLabsVoiceSettings;
      optimizeStreaming?: number;
    } = {}
  ): Promise<ReadableStream | null> {
    if (!this.isConfigured()) {
      console.error('ElevenLabs API key not configured');
      return null;
    }

    try {
      const {
        model = 'eleven_flash_v2_5', // Use fastest model for streaming
        voiceSettings = {
          stability: 0.5,
          similarity_boost: 0.8,
          style: 0.0,
          use_speaker_boost: true
        },
        optimizeStreaming = 3 // Maximum optimization for streaming
      } = options;

      console.log(`🎤 ElevenLabs Streaming: "${text.substring(0, 50)}..." with voice ${voiceId}`);

      const response = await axios.post(
        `${this.baseUrl}/text-to-speech/${voiceId}/stream`,
        {
          text,
          model_id: model,
          voice_settings: voiceSettings,
          optimize_streaming_latency: optimizeStreaming
        },
        {
          headers: {
            'Accept': 'audio/mpeg',
            'Content-Type': 'application/json',
            'xi-api-key': this.apiKey
          },
          responseType: 'stream'
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('❌ ElevenLabs Streaming API error:', error.response?.data || error.message);
      return null;
    }
  }

  // Get available voices
  async getVoices(): Promise<any[]> {
    if (!this.isConfigured()) {
      return [];
    }

    try {
      const response = await axios.get(`${this.baseUrl}/voices`, {
        headers: {
          'xi-api-key': this.apiKey
        }
      });

      return response.data.voices || [];
    } catch (error: any) {
      console.error('❌ Error fetching ElevenLabs voices:', error.message);
      return [];
    }
  }

  // Get voice settings for optimal Sarah personality
  getSarahVoiceSettings(): ElevenLabsVoiceSettings {
    return {
      stability: 0.6,        // Slightly more stable for professional sound
      similarity_boost: 0.8, // High similarity to voice
      style: 0.2,            // Light style for natural warmth
      use_speaker_boost: true // Enhanced clarity
    };
  }

  // Get recommended voice for Sarah personality
  getSarahVoiceId(): string {
    // Rachel is professional, warm, and perfect for HR/recruiting scenarios
    return this.voices.rachel;
  }

  // Convert audio for Twilio compatibility
  async generateTwilioCompatibleAudio(
    text: string,
    voiceId?: string
  ): Promise<ElevenLabsResponse> {
    return this.generateSpeech(text, voiceId || this.getSarahVoiceId(), {
      model: 'eleven_multilingual_v2', // High quality for important calls
      voiceSettings: this.getSarahVoiceSettings(),
      outputFormat: 'mp3_44100_128', // Twilio-compatible format
      optimizeStreaming: 2 // Good balance of quality and latency
    });
  }

  // ===== CONVERSATIONAL AI METHODS =====

  /**
   * Create a signed URL for ElevenLabs Conversational AI
   */
  async createConversationSignedUrl(agentId: string): Promise<string> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/convai/conversation/get_signed_url`,
        { agent_id: agentId },
        {
          headers: {
            'Content-Type': 'application/json',
            'xi-api-key': this.apiKey
          }
        }
      );

      return response.data.signed_url;
    } catch (error: any) {
      console.error('❌ Error creating ElevenLabs signed URL:', error.message);
      throw new Error(`Failed to create signed URL: ${error.message}`);
    }
  }

  /**
   * Start a new conversation session
   */
  async startConversation(sessionId: string, config: ConversationConfig): Promise<string> {
    try {
      // Get signed URL
      const signedUrl = await this.createConversationSignedUrl(config.agentId);

      // Create WebSocket connection
      const ws = new WebSocket(signedUrl);

      const session: ConversationSession = {
        sessionId,
        websocket: ws,
        isActive: false,
        startedAt: new Date(),
        agentId: config.agentId
      };

      // Set up WebSocket event handlers
      ws.on('open', () => {
        console.log(`✅ ElevenLabs conversation started for session ${sessionId}`);
        session.isActive = true;

        // Send initial configuration
        const initMessage = {
          type: 'conversation_initiation_client_data',
          conversation_initiation_client_data: {
            conversation_config_override: config.conversationConfig || {
              agent: {
                prompt: {
                  prompt: 'You are a professional AI interviewer conducting a technical interview. Be friendly, professional, and ask relevant questions based on the candidate\'s responses.'
                }
              }
            }
          }
        };

        ws.send(JSON.stringify(initMessage));
        this.emit('conversationStarted', sessionId);
      });

      ws.on('message', (data: Buffer) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleConversationMessage(sessionId, message);
        } catch (error) {
          console.error('❌ Error parsing ElevenLabs message:', error);
        }
      });

      ws.on('close', (code: number, reason: Buffer) => {
        console.log(`🔌 ElevenLabs conversation closed for session ${sessionId}:`, code, reason.toString());
        session.isActive = false;
        this.emit('conversationEnded', sessionId);
      });

      ws.on('error', (error: Error) => {
        console.error(`❌ ElevenLabs WebSocket error for session ${sessionId}:`, error);
        session.isActive = false;
        this.emit('conversationError', sessionId, error);
      });

      // Store session
      this.conversationSessions.set(sessionId, session);

      return sessionId;
    } catch (error) {
      console.error('❌ Error starting ElevenLabs conversation:', error);
      throw error;
    }
  }

  /**
   * Send audio data to ElevenLabs
   */
  sendAudioToConversation(sessionId: string, audioData: Buffer): boolean {
    const session = this.conversationSessions.get(sessionId);

    if (!session || !session.isActive || session.websocket.readyState !== WebSocket.OPEN) {
      console.warn(`⚠️ Cannot send audio to inactive session ${sessionId}`);
      return false;
    }

    try {
      // Convert audio buffer to base64
      const base64Audio = audioData.toString('base64');

      const audioMessage = {
        user_audio_chunk: base64Audio
      };

      session.websocket.send(JSON.stringify(audioMessage));
      return true;
    } catch (error) {
      console.error(`❌ Error sending audio to session ${sessionId}:`, error);
      return false;
    }
  }

  /**
   * End a conversation session
   */
  async endConversation(sessionId: string): Promise<void> {
    const session = this.conversationSessions.get(sessionId);

    if (!session) {
      console.warn(`⚠️ Session ${sessionId} not found`);
      return;
    }

    try {
      if (session.websocket.readyState === WebSocket.OPEN) {
        // Send end conversation message
        const endMessage = {
          type: 'conversation_end'
        };

        session.websocket.send(JSON.stringify(endMessage));

        // Close WebSocket
        session.websocket.close();
      }

      session.isActive = false;
      this.conversationSessions.delete(sessionId);

      console.log(`✅ ElevenLabs conversation ended for session ${sessionId}`);
    } catch (error) {
      console.error(`❌ Error ending conversation for session ${sessionId}:`, error);
    }
  }

  /**
   * Get conversation session status
   */
  getConversationStatus(sessionId: string): { isActive: boolean; startedAt?: Date; conversationId?: string } | null {
    const session = this.conversationSessions.get(sessionId);

    if (!session) {
      return null;
    }

    return {
      isActive: session.isActive,
      startedAt: session.startedAt,
      conversationId: session.conversationId
    };
  }

  /**
   * Get all active conversation sessions
   */
  getActiveConversations(): string[] {
    return Array.from(this.conversationSessions.entries())
      .filter(([_, session]) => session.isActive)
      .map(([sessionId, _]) => sessionId);
  }

  /**
   * Handle incoming messages from ElevenLabs conversation
   */
  private handleConversationMessage(sessionId: string, message: any): void {
    const session = this.conversationSessions.get(sessionId);
    if (!session) return;

    try {
      switch (message.type) {
        case 'conversation_initiation_metadata':
          session.conversationId = message.conversation_initiation_metadata?.conversation_id;
          console.log(`🎯 Conversation initiated for session ${sessionId}:`, session.conversationId);
          this.emit('conversationInitiated', sessionId, session.conversationId);
          break;

        case 'agent_response':
          console.log(`🤖 Agent response for session ${sessionId}:`, message.agent_response?.agent_response);
          this.emit('agentResponse', sessionId, message.agent_response);
          break;

        case 'agent_response_audio_chunk':
          // Forward audio chunk to be played in Zoom
          this.emit('agentAudio', sessionId, message.audio_chunk);
          break;

        case 'user_transcript':
          console.log(`👤 User transcript for session ${sessionId}:`, message.user_transcript?.text);
          this.emit('userTranscript', sessionId, message.user_transcript);
          break;

        case 'agent_response_correction':
          console.log(`🔧 Agent correction for session ${sessionId}:`, message.agent_response_correction);
          this.emit('agentCorrection', sessionId, message.agent_response_correction);
          break;

        case 'ping':
          // Respond to ping to keep connection alive
          if (session.websocket.readyState === WebSocket.OPEN) {
            session.websocket.send(JSON.stringify({ type: 'pong' }));
          }
          break;

        case 'conversation_end':
          console.log(`🏁 Conversation ended for session ${sessionId}`);
          this.endConversation(sessionId);
          break;

        default:
          console.log(`❓ Unknown message type for session ${sessionId}:`, message.type);
          break;
      }
    } catch (error) {
      console.error(`❌ Error handling ElevenLabs message for session ${sessionId}:`, error);
    }
  }

  /**
   * Cleanup all conversation sessions
   */
  async cleanupConversations(): Promise<void> {
    const sessionIds = Array.from(this.conversationSessions.keys());

    await Promise.all(
      sessionIds.map(sessionId => this.endConversation(sessionId))
    );

    console.log('🧹 ElevenLabs conversation cleanup completed');
  }

  // ===== TWILIO CONVERSATIONAL AI METHODS =====

  /**
   * Personality configurations for different conversation styles
   */
  private personalities = {
    professional: {
      voiceId: this.voices.sarah,
      temperature: 0.7,
      systemPrompt: 'You are a professional HR representative conducting a recruitment call. Be courteous, clear, and focused on gathering relevant information about the candidate\'s experience and interest in the position.',
      maxTokens: 150
    },
    friendly: {
      voiceId: this.voices.rachel,
      temperature: 0.8,
      systemPrompt: 'You are a friendly HR representative making a recruitment call. Be warm, conversational, and enthusiastic while maintaining professionalism. Make the candidate feel comfortable and valued.',
      maxTokens: 180
    },
    warm: {
      voiceId: this.voices.sarah,
      temperature: 0.75,
      systemPrompt: 'You are a warm and approachable HR representative. Be empathetic, understanding, and create a welcoming atmosphere while discussing the opportunity with the candidate.',
      maxTokens: 160
    },
    authoritative: {
      voiceId: this.voices.adam,
      temperature: 0.6,
      systemPrompt: 'You are a senior HR representative conducting an important recruitment call. Be confident, direct, and thorough in your questioning while maintaining respect and professionalism.',
      maxTokens: 140
    }
  };

  /**
   * Generate contextual conversation response for Twilio calls
   */
  async generateConversationalResponse(
    userInput: string,
    conversationState: ConversationState,
    settings: ConversationalSettings = {}
  ): Promise<ConversationalResponse> {
    if (!this.isConfigured()) {
      return {
        success: false,
        error: 'ElevenLabs API key not configured'
      };
    }

    try {
      const personality = settings.personality || 'professional';
      const personalityConfig = this.personalities[personality];

      // Generate AI response using OpenAI
      const aiResponse = await this.generateAIResponse(
        userInput,
        conversationState,
        personalityConfig.systemPrompt,
        {
          temperature: settings.temperature || personalityConfig.temperature,
          maxTokens: settings.maxTokens || personalityConfig.maxTokens
        }
      );

      if (!aiResponse.success || !aiResponse.response) {
        return aiResponse;
      }

      // Generate speech audio
      const audioResponse = await this.generateSpeech(
        aiResponse.response!,
        personalityConfig.voiceId,
        {
          model: 'eleven_multilingual_v2',
          voiceSettings: {
            stability: 0.6,
            similarity_boost: 0.8,
            style: 0.3,
            use_speaker_boost: true
          }
        }
      );

      // Update conversation state
      const updatedState = this.updateConversationState(
        conversationState,
        userInput,
        aiResponse.response!
      );

      return {
        success: true,
        response: aiResponse.response,
        audioUrl: audioResponse.audioUrl,
        nextAction: this.determineNextAction(aiResponse.response!, updatedState),
        conversationState: updatedState
      };

    } catch (error: any) {
      console.error('❌ Error generating conversational response:', error);
      return {
        success: false,
        error: error.message || 'Failed to generate response'
      };
    }
  }

  /**
   * Generate AI response using OpenAI
   */
  private async generateAIResponse(
    userInput: string,
    conversationState: ConversationState,
    systemPrompt: string,
    settings: { temperature?: number; maxTokens?: number }
  ): Promise<{ success: boolean; response?: string; error?: string }> {
    try {
      const messages = this.buildConversationMessages(userInput, conversationState, systemPrompt);

      // Use OpenAI for conversation generation
      const response = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: messages,
        temperature: settings.temperature,
        max_tokens: settings.maxTokens,
        frequency_penalty: 0.2,
        presence_penalty: 0.1
      });

      const aiMessage = response.choices[0]?.message?.content;

      if (!aiMessage) {
        return {
          success: false,
          error: 'No response generated'
        };
      }

      return {
        success: true,
        response: aiMessage.trim()
      };

    } catch (error: any) {
      console.error('❌ Error generating AI response:', error);
      return {
        success: false,
        error: error.message || 'Failed to generate AI response'
      };
    }
  }

  /**
   * Build conversation messages for OpenAI
   */
  private buildConversationMessages(
    userInput: string,
    conversationState: ConversationState,
    systemPrompt: string
  ): Array<{ role: 'system' | 'user' | 'assistant'; content: string }> {
    const messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }> = [];

    // Add system prompt with context
    let contextualPrompt = systemPrompt;

    if (conversationState.candidateInfo) {
      contextualPrompt += `\n\nCandidate Information:
- Name: ${conversationState.candidateInfo.name}
- Position: ${conversationState.candidateInfo.position}
- Experience: ${conversationState.candidateInfo.experienceYears} years
- Current Company: ${conversationState.candidateInfo.currentCompany}
- Skills: ${conversationState.candidateInfo.skills.join(', ')}
- Match Score: ${conversationState.candidateInfo.matchScore}%`;
    }

    if (conversationState.jobInfo) {
      contextualPrompt += `\n\nJob Information:
- Title: ${conversationState.jobInfo.title}
- Location: ${conversationState.jobInfo.location}
- Experience Level: ${conversationState.jobInfo.experienceLevel}
- Key Requirements: ${conversationState.jobInfo.skillsRequired.join(', ')}`;
    }

    if (conversationState.organizationInfo) {
      contextualPrompt += `\n\nCompany: ${conversationState.organizationInfo.name}
${conversationState.organizationInfo.companyOverview}`;
    }

    contextualPrompt += `\n\nCall Purpose: ${conversationState.callPurpose}
Current Stage: ${conversationState.stage}
Keep responses concise and natural for voice conversation.`;

    messages.push({ role: 'system', content: contextualPrompt });

    // Add conversation history
    conversationState.messages.forEach(msg => {
      messages.push({
        role: msg.role,
        content: msg.content
      });
    });

    // Add current user input
    messages.push({ role: 'user', content: userInput });

    return messages;
  }

  /**
   * Update conversation state with new messages
   */
  private updateConversationState(
    state: ConversationState,
    userInput: string,
    aiResponse: string
  ): ConversationState {
    const updatedMessages = [
      ...state.messages,
      { role: 'user' as const, content: userInput, timestamp: new Date() },
      { role: 'assistant' as const, content: aiResponse, timestamp: new Date() }
    ];

    // Determine next stage based on conversation flow
    let nextStage = state.stage;
    const responseText = aiResponse.toLowerCase();

    if (state.stage === 'opening' && (responseText.includes('tell me') || responseText.includes('experience'))) {
      nextStage = 'qualification';
    } else if (state.stage === 'qualification' && (responseText.includes('schedule') || responseText.includes('interview'))) {
      nextStage = 'scheduling';
    } else if (state.stage === 'scheduling' && (responseText.includes('thank you') || responseText.includes('goodbye'))) {
      nextStage = 'closing';
    }

    return {
      ...state,
      messages: updatedMessages,
      stage: nextStage
    };
  }

  /**
   * Determine next action based on AI response
   */
  private determineNextAction(
    aiResponse: string,
    conversationState: ConversationState
  ): 'continue' | 'schedule' | 'end_call' | 'transfer' | 'collect_info' {
    const responseText = aiResponse.toLowerCase();

    if (responseText.includes('schedule') || responseText.includes('interview')) {
      return 'schedule';
    }

    if (responseText.includes('thank you') || responseText.includes('goodbye') ||
        responseText.includes('end') || conversationState.stage === 'closing') {
      return 'end_call';
    }

    if (responseText.includes('transfer') || responseText.includes('manager')) {
      return 'transfer';
    }

    if (responseText.includes('tell me more') || responseText.includes('information')) {
      return 'collect_info';
    }

    return 'continue';
  }

  /**
   * Initialize conversation for Twilio calls
   */
  async initializeConversation(
    candidateInfo: any,
    jobInfo: any,
    organizationInfo: any,
    callPurpose: 'screening' | 'interview_scheduling' | 'follow_up' | 'offer_discussion',
    personality: 'professional' | 'friendly' | 'warm' | 'authoritative' = 'professional'
  ): Promise<ConversationState> {
    return {
      messages: [],
      candidateInfo,
      jobInfo,
      organizationInfo,
      stage: 'opening',
      callPurpose
    };
  }

  /**
   * Generate opening message for Twilio calls
   */
  async generateOpeningMessage(
    candidateName: string,
    companyName: string,
    jobTitle: string,
    purpose: 'screening' | 'interview_scheduling' | 'follow_up' | 'offer_discussion',
    personality: 'professional' | 'friendly' | 'warm' | 'authoritative' = 'professional'
  ): Promise<{ success: boolean; message?: string; audioUrl?: string; error?: string }> {
    try {
      const openingMessages = {
        screening: `Hello ${candidateName}! This is Sarah calling from ${companyName}. I hope you're having a great day! I'm reaching out regarding your application for the ${jobTitle} position. Do you have a few minutes to chat about this exciting opportunity?`,
        interview_scheduling: `Hi ${candidateName}! This is Sarah from ${companyName}. I'm calling about your ${jobTitle} application - we're really impressed with your background! We'd love to schedule an interview with you. Is this a good time to discuss some available time slots?`,
        follow_up: `Hello ${candidateName}! This is Sarah calling from ${companyName}. I wanted to follow up on our previous conversation about the ${jobTitle} position. How are you feeling about the opportunity, and do you have any questions I can help answer?`,
        offer_discussion: `Hello ${candidateName}! This is Sarah calling from ${companyName} with some exciting news about your ${jobTitle} application. I'd love to discuss the offer details with you. Is this a good time to chat?`
      };

      const openingMessage = openingMessages[purpose] || openingMessages.screening;

      // Generate speech for opening
      const personalityConfig = this.personalities[personality];
      const audioResponse = await this.generateSpeech(
        openingMessage,
        personalityConfig.voiceId,
        {
          model: 'eleven_multilingual_v2',
          voiceSettings: {
            stability: 0.6,
            similarity_boost: 0.8,
            style: 0.4,
            use_speaker_boost: true
          }
        }
      );

      return {
        success: true,
        message: openingMessage,
        audioUrl: audioResponse.audioUrl
      };

    } catch (error: any) {
      console.error('❌ Error generating opening message:', error);
      return {
        success: false,
        error: error.message || 'Failed to generate opening message'
      };
    }
  }
}

export const elevenLabsService = new ElevenLabsService();