import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function fixInterviewRunsSchema() {
  try {
    console.log('🔧 Fixing interview_runs table schema...');
    
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    const sql = neon(process.env.DATABASE_URL);
    
    // Check if zoom_meeting_id column exists
    console.log('1️⃣ Checking current table structure...');
    const columns = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'interview_runs' 
      ORDER BY ordinal_position
    `;
    
    console.log('📋 Current columns in interview_runs:');
    columns.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // Check if zoom_meeting_id exists
    const hasZoomMeetingId = columns.some(col => col.column_name === 'zoom_meeting_id');
    
    if (hasZoomMeetingId) {
      console.log('✅ zoom_meeting_id column already exists');
    } else {
      console.log('❌ zoom_meeting_id column missing, adding it...');
      
      await sql`
        ALTER TABLE interview_runs 
        ADD COLUMN zoom_meeting_id TEXT
      `;
      
      console.log('✅ Added zoom_meeting_id column');
    }
    
    // Check if elevenlabs_conversation_id exists
    const hasElevenlabsId = columns.some(col => col.column_name === 'elevenlabs_conversation_id');
    
    if (hasElevenlabsId) {
      console.log('✅ elevenlabs_conversation_id column already exists');
    } else {
      console.log('❌ elevenlabs_conversation_id column missing, adding it...');
      
      await sql`
        ALTER TABLE interview_runs 
        ADD COLUMN elevenlabs_conversation_id TEXT
      `;
      
      console.log('✅ Added elevenlabs_conversation_id column');
    }
    
    // Check if bot_session_id exists
    const hasBotSessionId = columns.some(col => col.column_name === 'bot_session_id');
    
    if (hasBotSessionId) {
      console.log('✅ bot_session_id column already exists');
    } else {
      console.log('❌ bot_session_id column missing, adding it...');
      
      await sql`
        ALTER TABLE interview_runs 
        ADD COLUMN bot_session_id TEXT
      `;
      
      console.log('✅ Added bot_session_id column');
    }
    
    // Verify final structure
    console.log('2️⃣ Verifying final table structure...');
    const finalColumns = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'interview_runs' 
      ORDER BY ordinal_position
    `;
    
    console.log('📋 Final columns in interview_runs:');
    finalColumns.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    console.log('🎉 Schema fix completed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing schema:', error);
    process.exit(1);
  }
}

fixInterviewRunsSchema();
