import { neon } from '@neondatabase/serverless';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testCompleteFlow() {
  try {
    console.log('🧪 Testing complete interview automation flow...');
    
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    const sql = neon(process.env.DATABASE_URL);
    
    // 1. Test database schema
    console.log('1️⃣ Testing database schema...');
    
    // Check interview_runs table structure
    const interviewRunsColumns = await sql`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'interview_runs' 
      ORDER BY ordinal_position
    `;
    
    console.log('📋 interview_runs columns:');
    interviewRunsColumns.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type}`);
    });
    
    const hasZoomMeetingId = interviewRunsColumns.some(col => col.column_name === 'zoom_meeting_id');
    const hasElevenlabsId = interviewRunsColumns.some(col => col.column_name === 'elevenlabs_conversation_id');
    const hasBotSessionId = interviewRunsColumns.some(col => col.column_name === 'bot_session_id');
    
    console.log('✅ Required columns check:');
    console.log(`   - zoom_meeting_id: ${hasZoomMeetingId ? '✅' : '❌'}`);
    console.log(`   - elevenlabs_conversation_id: ${hasElevenlabsId ? '✅' : '❌'}`);
    console.log(`   - bot_session_id: ${hasBotSessionId ? '✅' : '❌'}`);
    
    // 2. Test interview creation
    console.log('2️⃣ Testing interview creation...');
    
    const testInterview = {
      id: 'test-interview-' + Date.now(),
      candidateId: 'b7b16d20-d21e-41f9-85a1-96b1b1034584',
      organizationId: '74cbd810-0e90-4a59-ab9e-36c3d32e4ca3',
      role: 'Test Role',
      scheduledAt: new Date(Date.now() + 5 * 60 * 1000),
      durationMin: 60,
      status: 'scheduled',
      sdkType: 'video',
      roomOrMeetingId: 'test-session-' + Date.now(),
      joinUrls: {
        candidate: 'http://localhost:5000/interview-join?interviewId=test',
        host: 'http://localhost:5000/interview-host?interviewId=test'
      }
    };
    
    // Insert test interview
    await sql`
      INSERT INTO interviews_v2 (
        id, candidate_id, organization_id, role, scheduled_at, 
        duration_min, status, sdk_type, room_or_meeting_id, join_urls
      ) VALUES (
        ${testInterview.id}, ${testInterview.candidateId}, ${testInterview.organizationId},
        ${testInterview.role}, ${testInterview.scheduledAt}, ${testInterview.durationMin},
        ${testInterview.status}, ${testInterview.sdkType}, ${testInterview.roomOrMeetingId},
        ${JSON.stringify(testInterview.joinUrls)}
      )
    `;
    
    console.log('✅ Test interview created');
    
    // 3. Test interview_runs insertion
    console.log('3️⃣ Testing interview_runs insertion...');
    
    const testRun = {
      id: 'test-run-' + Date.now(),
      interviewId: testInterview.id,
      organizationId: testInterview.organizationId,
      status: 'starting',
      botSessionId: 'test-bot-session-' + Date.now(),
      zoomMeetingId: testInterview.roomOrMeetingId,
      elevenlabsConversationId: 'test-el-conv-' + Date.now(),
      metricsJson: {
        botStarted: new Date().toISOString(),
        agentProfile: 'test'
      }
    };
    
    await sql`
      INSERT INTO interview_runs (
        id, interview_id, organization_id, status, bot_session_id,
        zoom_meeting_id, elevenlabs_conversation_id, metrics_json
      ) VALUES (
        ${testRun.id}, ${testRun.interviewId}, ${testRun.organizationId},
        ${testRun.status}, ${testRun.botSessionId}, ${testRun.zoomMeetingId},
        ${testRun.elevenlabsConversationId}, ${JSON.stringify(testRun.metricsJson)}
      )
    `;
    
    console.log('✅ Test interview run created');
    
    // 4. Verify data
    console.log('4️⃣ Verifying created data...');
    
    const createdInterview = await sql`
      SELECT * FROM interviews_v2 WHERE id = ${testInterview.id}
    `;
    
    const createdRun = await sql`
      SELECT * FROM interview_runs WHERE id = ${testRun.id}
    `;
    
    console.log('📋 Created interview:', createdInterview[0]);
    console.log('📋 Created run:', createdRun[0]);
    
    // 5. Clean up test data
    console.log('5️⃣ Cleaning up test data...');
    
    await sql`DELETE FROM interview_runs WHERE id = ${testRun.id}`;
    await sql`DELETE FROM interviews_v2 WHERE id = ${testInterview.id}`;
    
    console.log('✅ Test data cleaned up');
    
    console.log('🎉 All tests passed! Database schema and flow are working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testCompleteFlow();
