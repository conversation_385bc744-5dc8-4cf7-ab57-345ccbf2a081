🤖 STEP 4: Agent response received: Hello! I am <PERSON> from Steorra
🤖 STEP 4: Agent response full data: {
  "agent_response_event": {
    "agent_response": "Hello! I am <PERSON> from Steorra"
  },
  "type": "agent_response"
}
🎯 STEP 4: Agent spoke, waiting for natural user response...
🔇 STEP 4: Silence/background noise. Frame: 75 Amplitude: 8
🗣️ STEP 4: <PERSON><PERSON><PERSON><PERSON><PERSON> SPEAKING DETECTED! Frame: 80 Amplitude: 212
🎙️ STEP 4: Active speech input - ElevenLabs should detect this
🔇 STEP 4: Silence/background noise. Frame: 82 Amplitude: 112
🗣️ STEP 4: CANDIDATE SPEAKING DETECTED! Frame: 89 Amplitude: 196
🎙️ STEP 4: Active speech input - Eleven<PERSON>abs should detect this
🔇 STEP 4: Silence/background noise. Frame: 91 Amplitude: 56
🗣️ STEP 4: CA<PERSON><PERSON>ATE SPEAKING DETECTED! Frame: 99 Amplitude: 180
🎙️ STEP 4: Active speech input - ElevenLabs should detect this
🔇 STEP 4: Silence/background noise. Frame: 100 Amplitude: 112
🎯 STEP 4: Media frame 100 ElevenLabs connected: true readyState: 1
📊 STEP 4: Media frames received: 100
✅ STEP 4: Audio CONVERTED (μ-law→PCM) and forwarded to ElevenLabs (frame 100)
🔄 STEP 4: Original μ-law length: 160 → PCM length: 320
🎧 STEP 4: DETAILED AUDIO ANALYSIS:
  📊 Original μ-law sample (first 8 bytes): [
  119, 121, 124,
  126, 253, 250,
  248, 245
]
  📊 Converted PCM sample (first 16 bytes): [
  192, 255, 208, 255, 232, 255,
  248, 255,  16,   0,  40,   0,
   56,   0,  80,   0
]
  📊 PCM as hex: c0ffd0ffe8fff8ff1000280038005000
  📊 Base64 length sent to ElevenLabs: 428
  📊 Max amplitude in sample: 112 (0 = silence, >100 = likely speech)
  📤 Message sent to ElevenLabs: {
  "type": "audio",
  "audio_event": {
    "audio_base_64": "wP/Q/+j/+P8QACgAOABQAFgAaABoAHAAaABoAFgAUABIAEAAMAAoABgAEAAAAPD/6P/Y/9D/yP/A/8D/uP/A/8D/yP/Q/9j/2P/g/+j/6P/o/+j/8P/w//D/8P/4//j/AAAAAAAACAAAAAgAEAAQABgAIAAoACgAMAAwADAAKAAoABgAEAAAAPD/6P/Y/9D/yP/I/8j/yP/I/8j/yP/I/8j/0P/Y/+D/6P/w//j/CAAQABAAEAAQABAACAAIABAACAAQABAAGAAgACgAKAAwADAAMAAoACgAIAAYABAAAAD4//D/6P/g/+D/2P/Q/9D/0P/Q/9D/2P/g/+D/8P/4//j/CAAIAAgACAAIAAgACAAAAPj/+P/4//j/+P8AAAgACAAQABAAEAAYABAAEAAQABAACAAIAAAACAAAAAAA+P8="
  }
}