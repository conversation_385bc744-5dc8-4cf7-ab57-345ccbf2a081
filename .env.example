# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Application Configuration
NODE_ENV=development
PORT=5000

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/hrms_dev

# Security Configuration
JWT_SECRET=your-jwt-secret-key-change-this-in-production
BCRYPT_ROUNDS=10

# Application URLs
BASE_URL=http://localhost:5000
FRONTEND_URL=http://localhost:3000
CORS_ORIGIN=http://localhost:3000

# AI Services Configuration
OPENAI_API_KEY=your-openai-api-key
ELEVENLABS_API_KEY=your-elevenlabs-api-key
ELEVENLABS_AGENT_ID=your-elevenlabs-agent-id

# Zoom Video SDK Configuration
ZOOM_SDK_KEY=your-zoom-sdk-key
ZOOM_SDK_SECRET=your-zoom-sdk-secret

# Gmail Integration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GMAIL_FROM_EMAIL=<EMAIL>

# Twilio Configuration (Optional - for voice calls)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# File Storage Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=********
ALLOWED_FILE_TYPES=pdf,doc,docx,txt

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE=./logs/app.log

# Bot Configuration
BOT_AUTH_TOKEN=your-bot-auth-token
BOT_SESSION_TIMEOUT=********

# Email Configuration
EMAIL_RATE_LIMIT=10
EMAIL_BATCH_SIZE=5

# Feature Flags
ENABLE_INTERVIEW_AUTOMATION=true
ENABLE_VOICE_CALLS=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_TRANSCRIPTION=true

# Development Only
DISABLE_EXTERNAL_SERVICES=false
MOCK_EXTERNAL_APIS=false
