import { google } from 'googleapis';
import fs from 'fs';
import path from 'path';
import { db } from '../db';
import { candidateAvailability, candidates } from '@shared/schema';
import { eq, desc } from 'drizzle-orm';
import { isEmailBlocked, logBlockedEmail } from '../utils/emailBlocklist';

const SCOPES = [
  'https://www.googleapis.com/auth/gmail.readonly',
  'https://www.googleapis.com/auth/gmail.send',
  'https://www.googleapis.com/auth/gmail.compose',
  'https://www.googleapis.com/auth/gmail.modify',
  'https://www.googleapis.com/auth/calendar',
  'https://www.googleapis.com/auth/calendar.events'
];
const TOKEN_PATH = 'token.json';
// Use environment variables instead of credentials file for security

class GmailService {
  private auth: any;
  private gmail: any;
  private processedEmails: Set<string> = new Set(); // Track processed email IDs
  private emailSendingEnabled: boolean = true; // Re-enabled, will <NAME_EMAIL> issue

  constructor() {
    this.initializeAuth();
  }

  private async initializeAuth() {
    try {
      // Try to use the same credentials file as calendar service
      const credentialsPath = path.join(process.cwd(), 'attached_assets/client_secret_264185390531-di7joe5ov5eu7927pdd0g09sdupko547.apps.googleusercontent.com_1750734731656.json');
      const tokenPath = path.join(process.cwd(), 'token.json');

      // Check if credentials file exists
      if (fs.existsSync(credentialsPath)) {
        console.log('📧 Using Gmail credentials from file...');
        const credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));
        const { client_secret, client_id, redirect_uris } = credentials.web;

        this.auth = new google.auth.OAuth2(client_id, client_secret, redirect_uris[0]);

        // Check if we have a token (same as calendar service)
        if (fs.existsSync(tokenPath)) {
          const token = fs.readFileSync(tokenPath, 'utf8');
          this.auth.setCredentials(JSON.parse(token));

          this.gmail = google.gmail({ version: 'v1', auth: this.auth });
          console.log('✅ Gmail API initialized with existing token');
          console.log('📧 Gmail authentication status:', {
            hasAuth: !!this.auth,
            hasCredentials: !!(this.auth && this.auth.credentials),
            hasAccessToken: !!(this.auth && this.auth.credentials && this.auth.credentials.access_token),
            scopes: this.auth.credentials ? this.auth.credentials.scope : 'none'
          });
        } else {
          console.log('⚠️ Gmail token not found. Email features will require authentication.');
        }
      } else {
        // Fallback to environment variables
        const client_id = process.env.GOOGLE_CLIENT_ID || '264185390531-di7joe5ov5eu7927pdd0g09sdupko547.apps.googleusercontent.com';
        const client_secret = process.env.GOOGLE_CLIENT_SECRET;
        if (!client_secret) {
          console.log('❌ Gmail OAuth not configured - missing credentials file and GOOGLE_CLIENT_SECRET environment variable');
          return;
        }

        const redirectUri = process.env.REPLIT_DEV_DOMAIN
          ? `https://${process.env.REPLIT_DEV_DOMAIN}/api/gmail/callback`
          : `http://localhost:5000/api/gmail/callback`;

        this.auth = new google.auth.OAuth2(client_id, client_secret, redirectUri);

        if (fs.existsSync(TOKEN_PATH)) {
          const token = fs.readFileSync(TOKEN_PATH, 'utf8');
          this.auth.setCredentials(JSON.parse(token));
          this.gmail = google.gmail({ version: 'v1', auth: this.auth });
          console.log('✅ Gmail API initialized with environment variables');
        } else {
          console.log('⚠️ No Gmail token found. Need to authorize first.');
        }
      }
    } catch (error) {
      console.error('❌ Error initializing Gmail service:', error);
    }
  }

  getAuthUrl(): string {
    if (!this.auth) {
      throw new Error('Auth not initialized');
    }
    
    return this.auth.generateAuthUrl({
      access_type: 'offline',
      scope: SCOPES,
      prompt: 'consent'
    });
  }

  async checkAuthentication(): Promise<boolean> {
    try {
      if (!this.auth || !fs.existsSync(TOKEN_PATH)) {
        return false;
      }
      
      // Try to make a simple API call to test authentication
      if (this.gmail) {
        await this.gmail.users.getProfile({ userId: 'me' });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Gmail authentication check failed:', error);
      return false;
    }
  }

  async refreshAuth(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.auth) {
        return { success: false, error: 'Auth not initialized' };
      }

      if (!fs.existsSync(TOKEN_PATH)) {
        return { success: false, error: 'No token found, requires initial authentication' };
      }

      // Try to refresh the token
      await this.auth.getAccessToken();
      
      // Test the refreshed token
      await this.gmail.users.getProfile({ userId: 'me' });
      
      return { success: true };
    } catch (error) {
      console.error('Gmail token refresh failed:', error);
      return { success: false, error: 'Token refresh failed, requires re-authentication' };
    }
  }

  async setAuthCode(code: string): Promise<void> {
    if (!this.auth) {
      throw new Error('Auth not initialized');
    }

    try {
      const { tokens } = await this.auth.getToken(code);
      this.auth.setCredentials(tokens);
      
      // Save token for future use
      fs.writeFileSync(TOKEN_PATH, JSON.stringify(tokens));
      
      this.gmail = google.gmail({ version: 'v1', auth: this.auth });
      console.log('Gmail API authorized successfully');
    } catch (error) {
      console.error('Error setting auth code:', error);
      throw error;
    }
  }

  getAuth(): any {
    return this.auth;
  }

  async checkForEmailResponses(): Promise<void> {

    if (!this.gmail) {
      console.log('Gmail not initialized. Please authorize first.');
      return;
    }

    try {
      // Search for replies to availability emails - look for unread emails with specific subject
      // Exclude problematic domains and automated responses
      const response = await this.gmail.users.messages.list({
        userId: 'me',
        q: 'is:unread subject:"Re: Interview Opportunity" -from:<EMAIL> -from:homeadvisor -from:express -from:peachybbies -from:company.com -from:<EMAIL> -subject:"Delivery Status Notification"',
        maxResults: 5
      });

      const messages = response.data.messages || [];
      
      if (messages.length === 0) {
        console.log('No new candidate email responses found');
        return;
      }

      console.log(`Found ${messages.length} potential candidate email responses`);

      for (const message of messages) {
        await this.processEmailMessage(message.id);
      }
    } catch (error) {
      console.error('Error checking for email responses:', error);
      // If token is invalid, reset the service
      if ((error as Error).message && (error as Error).message.includes('invalid_grant')) {
        console.log('Token expired - resetting Gmail service');
        this.gmail = null;
        if (fs.existsSync(TOKEN_PATH)) {
          fs.unlinkSync(TOKEN_PATH);
        }
      }
    }
  }

  private async processEmailMessage(messageId: string): Promise<void> {
    // Skip if already processed
    if (this.processedEmails.has(messageId)) {
      console.log(`Email ${messageId} already processed, skipping`);
      return;
    }

    try {
      console.log(`Processing email message ID: ${messageId}`);
      
      const message = await this.gmail.users.messages.get({
        userId: 'me',
        id: messageId,
        format: 'full'
      });

      const headers = message.data.payload.headers;
      const fromHeader = headers.find((h: any) => h.name === 'From');
      const subjectHeader = headers.find((h: any) => h.name === 'Subject');
      
      const from = fromHeader?.value || '';
      const subject = subjectHeader?.value || '';

      // Get email body
      let emailBody = '';
      if (message.data.payload.body.data) {
        emailBody = Buffer.from(message.data.payload.body.data, 'base64').toString();
      } else if (message.data.payload.parts) {
        for (const part of message.data.payload.parts) {
          if (part.mimeType === 'text/plain' && part.body.data) {
            emailBody = Buffer.from(part.body.data, 'base64').toString();
            break;
          }
        }
      }

      console.log(`Processing email from: ${from}, subject: ${subject}`);

      // Skip old Resend emails or promotional emails
      if (from.includes('<EMAIL>') ||
          from.includes('homeadvisor') || 
          from.includes('express') || 
          from.includes('peachybbies') ||
          subject.includes('💸') ||
          subject.includes('PTO') ||
          subject.includes('OFF')) {
        console.log('Skipping promotional or old Resend email');
        return;
      }

      // Only process emails with the correct subject
      if (!subject.includes('Re: Interview Opportunity')) {
        console.log('Skipping non-interview email');
        return;
      }

      // For self-replies (testing), look for candidate ID in the email body
      let candidateId = '';
      let candidateEmail = '';

      // Extract candidate ID from email body first
      const candidateIdMatch = emailBody.match(/Candidate ID:\s*([a-f0-9-]+)/i);
      if (candidateIdMatch) {
        candidateId = candidateIdMatch[1];
        console.log(`Found candidate ID in email body: ${candidateId}`);
        
        // Get candidate by ID to verify
        const candidate = await db
          .select()
          .from(candidates)
          .where(eq(candidates.id, candidateId))
          .limit(1);

        if (candidate.length > 0) {
          candidateEmail = candidate[0].email;
          console.log(`Found candidate: ${candidate[0].fullName} (${candidateEmail})`);
        } else {
          console.log(`No candidate found with ID: ${candidateId}`);
          return;
        }
      } else {
        // Fallback: find candidate by email address
        const emailMatch = from.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
        if (!emailMatch) {
          console.log('No valid email address found in From field and no candidate ID in body');
          return;
        }

        candidateEmail = emailMatch[1];
        console.log(`Looking for candidate with email: ${candidateEmail}`);

        // SECURITY WARNING: Email-only lookup can cause cross-tenant issues!
        // TODO: This should be eliminated - requires candidate ID in emails or Gmail thread mapping
        // For now, prioritize most recent application but this is not secure
        console.warn('🚨 USING INSECURE EMAIL-ONLY LOOKUP - may cause cross-tenant issues');
        const candidate = await db
          .select()
          .from(candidates)
          .where(eq(candidates.email, candidateEmail))
          .orderBy(desc(candidates.createdAt))
          .limit(1);

        if (candidate.length === 0) {
          console.log(`No candidate found with email: ${candidateEmail}`);
          return;
        }

        candidateId = candidate[0].id;
        console.log(`Found candidate: ${candidate[0].fullName} (ID: ${candidateId})`);
      }

      // Extract only the candidate's response part (before the original email)
      const responsePart = emailBody.split(/On\s+\w+,.*wrote:|On\s+\d+.*\d+.*at.*wrote:/i)[0].trim();
      console.log(`Candidate response part: ${responsePart}`);
      
      // Extract timezone information first, before other processing
      let timezone = 'Not specified';
      const timezoneLineMatch = responsePart.match(/Time zone:\s*([A-Z]{2,4})/i);
      if (timezoneLineMatch) {
        timezone = timezoneLineMatch[1].toUpperCase();
        console.log(`Found timezone from "Time zone:" line: ${timezone}`);
      } else {
        // Fallback: look for standalone timezone abbreviations
        const timezoneMatch = responsePart.match(/\b(EST|PST|CST|MST|EDT|PDT|CDT|MDT|UTC|GMT)\b/i);
        if (timezoneMatch) {
          timezone = timezoneMatch[0].toUpperCase();
          console.log(`Found timezone from pattern match: ${timezone}`);
        }
      }
      
      console.log(`Time zone: ${timezone}`);

      // Enhanced parsing for various date/time formats with smart deduplication
      const patterns = [
        // Format like "Aug 10 11am to 12pm" or "Oct 3 10am to 11 am"
        /(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2}\s+\d{1,2}(?::\d{2})?\s*(?:am|pm|AM|PM)\s+to\s+\d{1,2}(?::\d{2})?\s*(?:am|pm|AM|PM)/gi,
        // Standard date formats: July 2, 2025 2:00 PM - 3:00 PM
        /(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2}[,\s]+\d{4}\s+\d{1,2}:\d{2}\s*(?:AM|PM)\s*[-–]\s*\d{1,2}:\d{2}\s*(?:AM|PM)/gi,
        // Time only: 2:00 PM - 3:00 PM
        /\d{1,2}:\d{2}\s*(?:AM|PM)\s*[-–]\s*\d{1,2}:\d{2}\s*(?:AM|PM)/gi,
        // Day of week + time: Monday 2:00 PM - 3:00 PM
        /(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)\s+\d{1,2}:\d{2}\s*(?:AM|PM)\s*[-–]\s*\d{1,2}:\d{2}\s*(?:AM|PM)/gi,
        // Date without year: July 2 2:00 PM - 3:00 PM
        /(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2}\s+\d{1,2}:\d{2}\s*(?:AM|PM)\s*[-–]\s*\d{1,2}:\d{2}\s*(?:AM|PM)/gi,
        // Simple format: 2-3pm, 2pm-3pm
        /\d{1,2}(?::\d{2})?\s*(?:AM|PM|am|pm)?\s*[-–]\s*\d{1,2}(?::\d{2})?\s*(?:AM|PM|am|pm)/gi
      ];

      let allMatches: string[] = [];
      
      // Process patterns in order of specificity (most specific first)
      patterns.forEach((pattern) => {
        const matches = responsePart.match(pattern) || [];
        allMatches = allMatches.concat(matches);
      });

      // Smart deduplication - remove substring matches and normalize
      const uniqueMatches = new Set<string>();
      const normalizedMatches = allMatches.map(match => match.trim().toLowerCase());
      
      // Sort by length (longest first) to keep most specific matches
      const sortedMatches = allMatches.sort((a, b) => b.length - a.length);
      
      for (const match of sortedMatches) {
        const normalized = match.trim().toLowerCase();
        let isDuplicate = false;
        
        // Check if this match is already covered by a longer, more specific match
        for (const existing of Array.from(uniqueMatches)) {
          if (existing.toLowerCase().includes(normalized) || normalized.includes(existing.toLowerCase())) {
            // Keep the longer, more specific match
            if (match.length > existing.length) {
              uniqueMatches.delete(existing);
              uniqueMatches.add(match.trim());
            }
            isDuplicate = true;
            break;
          }
        }
        
        if (!isDuplicate) {
          uniqueMatches.add(match.trim());
        }
      }

      const finalSlots = Array.from(uniqueMatches).slice(0, 5); // Limit to 5 most relevant slots
      
      console.log(`Found ${finalSlots.length} time slots:`, finalSlots);
      
      // Timezone was already extracted above

      // Filter out generic availability statements that contain specific time slots
      const filteredSlots = finalSlots.filter(slot => {
        const slotLower = slot.toLowerCase();
        // Remove generic statements like "available on" that contain specific times
        if (slotLower.includes('available') && slotLower.includes('\n')) {
          return false;
        }
        // Remove slots that are just repetitive text
        if (slotLower.includes('candidate') || slotLower.includes('email') || slotLower.includes('response')) {
          return false;
        }
        return true;
      });

      // Create more detailed availability slots
      const availabilitySlots = filteredSlots.length > 0 ? 
        filteredSlots.slice(0, 5).map(slot => slot.trim()) : // Limit to 5 clean slots
        ['Candidate responded but specific times need clarification'];

      // Extract preferred time from first slot
      let preferredTime = availabilitySlots.length > 0 ? availabilitySlots[0] : 'Available - details in email';

      // Create comprehensive notes
      const notes = `Candidate provided ${finalSlots.length} time references. Timezone: ${timezone}. Full response requires review.`;

      console.log(`Parsed availability - Time: ${preferredTime}, Slots: ${availabilitySlots.join(', ')}, Notes: ${notes}`);
      console.log(`Full email body for reference: ${emailBody.substring(0, 500)}...`);

      // Check if this email has already been processed
      if (this.processedEmails.has(messageId)) {
        console.log(`Email ${messageId} already processed, skipping...`);
        return;
      }

      // Create a hash of the response content to check for duplicate processing
      const responseHash = Buffer.from(responsePart).toString('base64').substring(0, 50);
      
      // Check if we already have a response with the same content hash
      const existingAvailability = await db
        .select()
        .from(candidateAvailability)
        .where(eq(candidateAvailability.candidateId, candidateId))
        .limit(1);
      
      // If there's an existing response, check if it's the same content
      if (existingAvailability.length > 0) {
        const existingData = existingAvailability[0].selectedSlot;
        if (existingData && typeof existingData === 'string') {
          try {
            const parsed = JSON.parse(existingData);
            if (parsed.rawEmailBody && parsed.rawEmailBody.includes(responsePart.substring(0, 100))) {
              console.log(`Duplicate response content detected for candidate ${candidateId}, skipping...`);
              // Mark email as processed to prevent reprocessing
              this.processedEmails.add(messageId);
              await this.markAsRead(messageId);
              return;
            }
          } catch (e) {
            // Continue processing if we can't parse existing data
          }
        }
      }

      const availabilityData = {
        respondedAt: new Date(),
        status: 'received' as const,
        availableSlots: JSON.stringify(availabilitySlots),
        selectedSlot: JSON.stringify({
          time: preferredTime,
          slots: availabilitySlots,
          notes: notes,
          timezone: timezone,
          rawEmailBody: responsePart.substring(0, 1000), // Store candidate's response part
          parsedSlots: finalSlots.length
        })
      };

      console.log(`Saving availability response for candidate ${candidateId}:`, availabilityData);

      if (existingAvailability.length > 0) {
        // Update existing
        await db
          .update(candidateAvailability)
          .set(availabilityData)
          .where(eq(candidateAvailability.candidateId, candidateId));
        
        console.log(`Updated existing availability for candidate ${candidateId}`);
      } else {
        // Create new
        await db
          .insert(candidateAvailability)
          .values({
            candidateId,
            ...availabilityData
          });
        
        console.log(`Created new availability record for candidate ${candidateId}`);
      }

      // Mark email as read to prevent reprocessing
      await this.markAsRead(messageId);
      
      // Add to processed set
      this.processedEmails.add(messageId);
      
      console.log(`Successfully processed availability response for candidate ${candidateId}`);

    } catch (error) {
      console.error('Error processing email message:', error);
    }
  }

  private async markAsRead(messageId: string): Promise<void> {
    try {
      await this.gmail.users.messages.modify({
        userId: 'me',
        id: messageId,
        requestBody: {
          removeLabelIds: ['UNREAD']
        }
      });
      console.log(`Email ${messageId} marked as read`);
    } catch (error) {
      console.log(`Cannot mark email as read: ${(error as Error).message}`);
      // Skip marking as read if permissions insufficient - continue processing
    }
  }

  async startMonitoring(): Promise<void> {
    console.log('Starting Gmail monitoring for candidate responses...');
    
    // Check immediately
    await this.checkForEmailResponses();
    
    // Then check every 15 seconds for faster response processing
    setInterval(async () => {
      await this.checkForEmailResponses();
    }, 15000);
  }

  async sendEmail(to: string, subject: string, htmlBody: string): Promise<boolean> {
    // Check against comprehensive blocklist
    if (isEmailBlocked(to)) {
      logBlockedEmail(to, 'Domain/address in blocklist - prevents delivery failures');
      return false;
    }

    if (!this.emailSendingEnabled) {
      console.log('📧 Email sending DISABLED to prevent connection timeouts. Email blocked to:', to);
      console.log('   To re-enable, set emailSendingEnabled = true in GmailService');
      return false;
    }

    if (!this.isAuthenticated()) {
      console.error('Gmail not authenticated - cannot send email');
      return false;
    }

    try {
      console.log(`Sending Gmail email to: ${to}`);
      console.log(`Subject: ${subject}`);
      
      const message = [
        `From: HR Team <<EMAIL>>`,
        `To: ${to}`,
        `Reply-To: <EMAIL>`,
        `Subject: ${subject}`,
        'Content-Type: text/html; charset=utf-8',
        'MIME-Version: 1.0',
        '',
        htmlBody
      ].join('\r\n');

      const encodedMessage = Buffer.from(message)
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');

      const response = await this.gmail.users.messages.send({
        userId: 'me',
        requestBody: {
          raw: encodedMessage,
        },
      });

      console.log(`Gmail email sent successfully to ${to}:`, response.data);
      return true;
    } catch (error) {
      console.error('Error sending Gmail email:', error);
      return false;
    }
  }

  async cleanupCompanyEmails(): Promise<{ cleaned: number; details: string[] }> {
    if (!this.gmail) {
      console.log('Gmail not initialized for cleanup');
      return { cleaned: 0, details: ['Gmail not initialized'] };
    }

    const details: string[] = [];
    let cleaned = 0;

    try {
      // 1. Clean up all company.com related messages in inbox
      const inboxResponse = await this.gmail.users.messages.list({
        userId: 'me',
        q: 'company.<NAME_EMAIL> OR "Delivery Status Notification"',
        maxResults: 50
      });

      const inboxMessages = inboxResponse.data.messages || [];
      console.log(`Found ${inboxMessages.length} potentially problematic emails in inbox`);

      for (const message of inboxMessages) {
        try {
          await this.gmail.users.messages.modify({
            userId: 'me',
            id: message.id,
            requestBody: {
              removeLabelIds: ['UNREAD', 'INBOX'],
              addLabelIds: ['TRASH']
            }
          });
          
          cleaned++;
          details.push(`Moved message ${message.id} to trash`);
        } catch (error: any) {
          details.push(`Failed to clean ${message.id}: ${error?.message || 'Unknown error'}`);
        }
      }

      // 2. Clean up drafts that might contain company.com
      const draftsResponse = await this.gmail.users.drafts.list({
        userId: 'me',
        maxResults: 50
      });

      const drafts = draftsResponse.data.drafts || [];
      console.log(`Found ${drafts.length} drafts to check`);

      for (const draft of drafts) {
        try {
          const draftDetail = await this.gmail.users.drafts.get({
            userId: 'me',
            id: draft.id
          });

          const message = draftDetail.data.message;
          const headers = message.payload?.headers || [];
          const toHeader = headers.find((h: any) => h.name.toLowerCase() === 'to');
          
          if (toHeader && toHeader.value.includes('company.com')) {
            await this.gmail.users.drafts.delete({
              userId: 'me',
              id: draft.id
            });
            
            cleaned++;
            details.push(`Deleted draft to ${toHeader.value}`);
            console.log(`🗑️ Deleted draft to ${toHeader.value}`);
          }
        } catch (error: any) {
          details.push(`Failed to process draft ${draft.id}: ${error?.message || 'Unknown error'}`);
        }
      }

      // 3. Clean up sent items that might be causing bounces
      const sentResponse = await this.gmail.users.messages.list({
        userId: 'me',
        q: 'in:sent company.com',
        maxResults: 20
      });

      const sentMessages = sentResponse.data.messages || [];
      console.log(`Found ${sentMessages.length} sent messages to company.com`);

      for (const message of sentMessages) {
        try {
          await this.gmail.users.messages.modify({
            userId: 'me',
            id: message.id,
            requestBody: {
              addLabelIds: ['TRASH']
            }
          });
          
          cleaned++;
          details.push(`Archived sent message ${message.id}`);
        } catch (error: any) {
          details.push(`Failed to archive sent message ${message.id}: ${error?.message || 'Unknown error'}`);
        }
      }

      console.log(`🧹 Comprehensive email cleanup completed. Processed ${cleaned} items.`);
      return { cleaned, details };

    } catch (error: any) {
      console.error('Error during Gmail cleanup:', error);
      return { cleaned, details: [`Cleanup error: ${error?.message || 'Unknown error'}`] };
    }
  }

  isAuthenticated(): boolean {
    const tokenPath = path.join(process.cwd(), 'token.json');
    const hasToken = fs.existsSync(tokenPath) || fs.existsSync(TOKEN_PATH);
    const hasAuth = !!this.auth && !!this.auth.credentials && !!this.auth.credentials.access_token;
    const hasGmail = !!this.gmail;

    console.log('📧 Gmail authentication check:', {
      hasToken,
      hasAuth,
      hasGmail,
      tokenPath: tokenPath,
      legacyTokenPath: TOKEN_PATH
    });

    return hasAuth && hasGmail;
  }
}

export const gmailService = new GmailService();