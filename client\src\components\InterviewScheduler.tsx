import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Clock, User, Bot, Send } from 'lucide-react';
import { toast } from 'sonner';

interface Candidate {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  currentPosition?: string;
  appliedJobId?: string;
  status: string;
}

interface AgentProfile {
  id: string;
  name: string;
  scriptVersion: string;
}

interface InterviewSchedulerProps {
  agentProfiles: AgentProfile[];
  onInterviewCreated: (interview: any) => void;
}

const InterviewScheduler: React.FC<InterviewSchedulerProps> = ({
  agentProfiles,
  onInterviewCreated
}) => {
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    candidateId: '',
    role: '',
    scheduledAt: '',
    durationMin: 60,
    agentProfileId: 'default',
    notes: ''
  });

  useEffect(() => {
    loadCandidates();
  }, []);

  const loadCandidates = async () => {
    try {
      console.log('📋 Loading approved candidates...');
      // Load only approved candidates for interview scheduling
      const response = await fetch('/api/candidates?status=approved_for_interview', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      console.log('📡 Candidates API response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('📋 Candidates API response:', data);

        // Handle both direct array and wrapped response formats
        const candidatesArray = Array.isArray(data) ? data : (data.candidates || data.data || []);
        console.log('👥 Processed candidates array:', candidatesArray);

        setCandidates(candidatesArray);
        console.log(`✅ Loaded ${candidatesArray.length} approved candidates`);
      } else {
        console.error('Failed to load candidates:', response.statusText);
        toast.error('Failed to load approved candidates');
      }
    } catch (error) {
      console.error('Error loading candidates:', error);
      toast.error('Failed to load candidates');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log('🚀 Form submission started');
    console.log('📝 Form data:', formData);

    if (!formData.candidateId || !formData.role || !formData.scheduledAt) {
      console.log('❌ Validation failed - missing required fields');
      console.log('Missing fields:', {
        candidateId: !formData.candidateId,
        role: !formData.role,
        scheduledAt: !formData.scheduledAt
      });
      toast.error('Please fill in all required fields');
      return;
    }

    console.log('✅ Validation passed, proceeding with API call');
    setLoading(true);

    try {
      // Prepare the payload, handling the "default" agent profile value and datetime format
      const payload = {
        ...formData,
        scheduledAt: formatDateTimeForAPI(formData.scheduledAt), // Convert to proper ISO format
        agentProfileId: formData.agentProfileId === 'default' ? null : formData.agentProfileId || null
      };

      console.log('📤 Sending payload:', JSON.stringify(payload, null, 2));

      const response = await fetch('/api/interviews-v2', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(payload)
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Interview created successfully:', data);

        onInterviewCreated(data.interview);

        // Calculate bot join time more safely
        try {
          const scheduledTime = new Date(payload.scheduledAt);
          const botJoinTime = Math.ceil((scheduledTime.getTime() - Date.now() - 2*60*1000) / 60000);
          const joinMessage = botJoinTime > 0 ? `Bot will join in ${botJoinTime} minutes.` : 'Bot will join shortly!';
          toast.success(`🎉 Interview scheduled successfully! ${joinMessage}`);
        } catch (timeError) {
          toast.success('🎉 Interview scheduled successfully!');
        }

        // Reset form
        setFormData({
          candidateId: '',
          role: '',
          scheduledAt: '',
          durationMin: 60,
          agentProfileId: 'default',
          notes: ''
        });
      } else {
        const error = await response.json();
        console.error('❌ Interview creation failed:', error);
        console.error('❌ Error details:', error.details);
        toast.error(error.error || 'Failed to schedule interview');
      }
    } catch (error) {
      console.error('Error scheduling interview:', error);
      toast.error('Failed to schedule interview');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCandidateSelect = (candidateId: string) => {
    console.log('👤 Candidate selected:', candidateId);
    const selectedCandidate = candidates.find(c => c.id === candidateId);
    console.log('📋 Selected candidate details:', selectedCandidate);

    if (selectedCandidate) {
      const newFormData = {
        ...formData,
        candidateId,
        // Auto-fill role with candidate's current position or a default
        role: selectedCandidate.currentPosition || 'Software Engineer'
      };
      console.log('📝 Updated form data:', newFormData);
      setFormData(newFormData);
    }
  };

  // Generate default datetime (5 minutes from now for immediate testing)
  const getDefaultDateTime = () => {
    const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000);
    return fiveMinutesFromNow.toISOString().slice(0, 16);
  };

  // Convert datetime-local format to ISO string for API
  const formatDateTimeForAPI = (datetimeLocal: string) => {
    if (!datetimeLocal) return '';
    // datetime-local format: "2025-09-21T08:11"
    // Need to convert to full ISO: "2025-09-21T08:11:00.000Z"
    const date = new Date(datetimeLocal);
    return date.toISOString();
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="w-5 h-5" />
          <span>Schedule New Interview</span>
        </CardTitle>
        <CardDescription>
          Create an AI-powered interview session with automatic bot hosting
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Candidate Selection */}
          <div className="space-y-2">
            <Label htmlFor="candidate" className="flex items-center space-x-2">
              <User className="w-4 h-4" />
              <span>Candidate *</span>
            </Label>
            <Select
              value={formData.candidateId}
              onValueChange={handleCandidateSelect}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an approved candidate" />
              </SelectTrigger>
              <SelectContent>
                {candidates.length === 0 ? (
                  <div className="p-2 text-sm text-gray-500">
                    No approved candidates available
                  </div>
                ) : (
                  candidates.map(candidate => (
                    <SelectItem key={candidate.id} value={candidate.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{candidate.fullName}</span>
                        <span className="text-sm text-gray-500">{candidate.email}</span>
                        {candidate.currentPosition && (
                          <span className="text-xs text-blue-600">{candidate.currentPosition}</span>
                        )}
                      </div>
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Role */}
          <div className="space-y-2">
            <Label htmlFor="role">Position/Role *</Label>
            <Input
              id="role"
              value={formData.role}
              onChange={(e) => handleInputChange('role', e.target.value)}
              placeholder="e.g., Senior Software Engineer"
              required
            />
          </div>

          {/* Date and Time */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="scheduledAt" className="flex items-center space-x-2">
                <Clock className="w-4 h-4" />
                <span>Scheduled Date & Time *</span>
              </Label>
              <Input
                id="scheduledAt"
                type="datetime-local"
                value={formData.scheduledAt || getDefaultDateTime()}
                onChange={(e) => handleInputChange('scheduledAt', e.target.value)}
                min={new Date(Date.now() + 2 * 60 * 1000).toISOString().slice(0, 16)} // Minimum 2 minutes from now
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                🌍 Timezone: {Intl.DateTimeFormat().resolvedOptions().timeZone}<br/>
                🤖 Bot will join 2 minutes before scheduled time
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="duration">Duration (minutes)</Label>
              <Select 
                value={formData.durationMin.toString()} 
                onValueChange={(value) => handleInputChange('durationMin', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30">30 minutes</SelectItem>
                  <SelectItem value="45">45 minutes</SelectItem>
                  <SelectItem value="60">60 minutes</SelectItem>
                  <SelectItem value="90">90 minutes</SelectItem>
                  <SelectItem value="120">120 minutes</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Agent Profile */}
          <div className="space-y-2">
            <Label htmlFor="agentProfile" className="flex items-center space-x-2">
              <Bot className="w-4 h-4" />
              <span>AI Agent Profile</span>
            </Label>
            <Select 
              value={formData.agentProfileId} 
              onValueChange={(value) => handleInputChange('agentProfileId', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an agent profile (optional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">Default Agent</SelectItem>
                {agentProfiles.map(profile => (
                  <SelectItem key={profile.id} value={profile.id}>
                    <div className="flex flex-col">
                      <span className="font-medium">{profile.name}</span>
                      <span className="text-sm text-gray-500">Version {profile.scriptVersion}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Additional Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Any special instructions or notes for the interview..."
              rows={3}
            />
          </div>

          {/* Interview Preview */}
          {formData.candidateId && formData.role && formData.scheduledAt && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Interview Preview</h4>
              <div className="space-y-1 text-sm text-blue-800">
                <p><strong>Candidate:</strong> {candidates.find(c => c.id === formData.candidateId)?.fullName}</p>
                <p><strong>Role:</strong> {formData.role}</p>
                <p><strong>Date:</strong> {new Date(formData.scheduledAt).toLocaleString()}</p>
                <p><strong>Duration:</strong> {formData.durationMin} minutes</p>
                <p><strong>Agent:</strong> {formData.agentProfileId === 'default' ? 'Default Agent' : agentProfiles.find(p => p.id === formData.agentProfileId)?.name || 'Default Agent'}</p>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-between">
            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setFormData({
                  candidateId: '',
                  role: '',
                  scheduledAt: '',
                  durationMin: 60,
                  agentProfileId: 'default',
                  notes: ''
                })}
              >
                Clear
              </Button>

              <Button
                type="button"
                variant="secondary"
                onClick={() => {
                  console.log('⚡ Quick Test button clicked');
                  const quickTestTime = new Date(Date.now() + 3 * 60 * 1000); // 3 minutes from now
                  const formattedTime = quickTestTime.toISOString().slice(0, 16);
                  console.log('🕐 Quick test time (datetime-local):', formattedTime);
                  console.log('🕐 Quick test time (ISO):', quickTestTime.toISOString());
                  handleInputChange('scheduledAt', formattedTime);
                  toast.info('⚡ Quick test time set! Bot will join in ~1 minute');
                }}
                disabled={!formData.candidateId}
                className="bg-orange-100 hover:bg-orange-200 text-orange-800"
              >
                🚀 Quick Test (3 min)
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={async () => {
                  console.log('🧪 Testing API connection...');
                  try {
                    const response = await fetch('/api/interviews-v2/test', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                      },
                      body: JSON.stringify({ test: 'data', timestamp: new Date().toISOString() })
                    });
                    const result = await response.json();
                    console.log('🧪 Test API response:', result);
                    toast.success('API connection test successful!');
                  } catch (error) {
                    console.error('🧪 Test API error:', error);
                    toast.error('API connection test failed!');
                  }
                }}
                className="bg-blue-100 hover:bg-blue-200 text-blue-800"
              >
                🧪 Test API
              </Button>
            </div>

            <Button type="submit" disabled={loading}>
              {loading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Scheduling...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Send className="w-4 h-4" />
                  <span>Schedule Interview</span>
                </div>
              )}
            </Button>
          </div>
        </form>

        {/* Help Text */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-900 mb-2">🚀 Interview Automation Process</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• 📧 <strong>Email invitation</strong> sent to candidate automatically</li>
            <li>• 🎥 <strong>Zoom Video SDK session</strong> created with join links</li>
            <li>• 🤖 <strong>AI bot joins</strong> 2 minutes before scheduled time</li>
            <li>• 📹 <strong>Interview recorded</strong> and transcribed automatically</li>
            <li>• 📊 <strong>Summary and scoring</strong> report generated after completion</li>
            <li>• ⚡ <strong>Quick Test:</strong> Use 3-minute scheduling for immediate testing</li>
            <li>• 🌍 <strong>Timezone:</strong> All times in {Intl.DateTimeFormat().resolvedOptions().timeZone}</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default InterviewScheduler;
