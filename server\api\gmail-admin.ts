import { Router } from 'express';
import { gmailService } from '../services/gmailService';
import { isEmailBlocked, BLOCKED_DOMAINS, BLOCKED_ADDRESSES } from '../utils/emailBlocklist';

const router = Router();

/**
 * @swagger
 * /gmail-admin/force-cleanup:
 *   post:
 *     summary: Force comprehensive Gmail cleanup
 *     description: Aggressively clean all company.com related emails, drafts, and sent items
 *     tags: [Gmail Admin]
 *     responses:
 *       200:
 *         description: Cleanup completed
 */
router.post('/force-cleanup', async (req, res) => {
  try {
    console.log('🔥 FORCE CLEANUP: Starting aggressive Gmail cleanup...');
    
    const result = await gmailService.cleanupCompanyEmails();
    
    // Also disable email sending temporarily
    console.log('🚫 Temporarily disabling all outbound emails to company.com');
    
    res.json({
      success: true,
      message: 'Aggressive cleanup completed',
      ...result,
      blockedDomains: BLOCKED_DOMAINS,
      blockedAddresses: BLOCKED_ADDRESSES
    });
  } catch (error) {
    console.error('Force cleanup failed:', error);
    res.status(500).json({ error: 'Force cleanup failed' });
  }
});

/**
 * @swagger
 * /gmail-admin/test-block:
 *   post:
 *     summary: Test email blocking
 *     description: Test if an email address would be blocked
 *     tags: [Gmail Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *     responses:
 *       200:
 *         description: Block test result
 */
router.post('/test-block', async (req, res) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({ error: 'Email required' });
    }
    
    const isBlocked = isEmailBlocked(email);
    
    res.json({
      email,
      isBlocked,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Block test failed:', error);
    res.status(500).json({ error: 'Block test failed' });
  }
});

export default router;