# Gmail Authentication Setup for Email Notifications

## Current Status
The ATS system is configured to send interview availability requests to candidates via Gmail, but requires re-authentication to function properly.

## Authentication Steps

1. **Get the Gmail Authorization URL**
   ```bash
   curl http://localhost:5000/api/gmail/auth-url
   ```

2. **Visit the Authorization URL**
   - Copy the URL from the response
   - Open it in your browser
   - Sign in with the Gmail account: <EMAIL>
   - Grant the required permissions:
     - Gmail read access
     - Gmail send access
     - Gmail compose access

3. **Complete OAuth Flow**
   - After granting permissions, you'll be redirected to the callback URL
   - The system will automatically save the authentication tokens

4. **Test Email Functionality**
   ```bash
   curl -X POST http://localhost:5000/api/availability/request \
     -H "Content-Type: application/json" \
     -d '{"candidateId": "9845d708-b039-42c7-86c7-a015037236da"}'
   ```

## Required Gmail Permissions
- `https://www.googleapis.com/auth/gmail.readonly` - Read emails
- `https://www.googleapis.com/auth/gmail.send` - Send emails
- `https://www.googleapis.com/auth/gmail.compose` - Compose emails
- `https://www.googleapis.com/auth/gmail.modify` - Mark emails as read

## Email Template
When authentication is working, candidates will receive:
- Subject: "Interview Opportunity - Please Share Your Availability"
- Professional HTML email with interview details
- Instructions to reply with available time slots
- Company branding and contact information

## Monitoring
The system automatically monitors for candidate responses and updates the database when availability is received.