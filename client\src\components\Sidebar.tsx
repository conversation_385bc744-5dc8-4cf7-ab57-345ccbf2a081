import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import {
  FileText,
  Calendar,
  Users,
  UserCheck,
  Building2,
  Search,
  LogOut,
  Settings,
  Home,
  Inbox,
  BarChart3,
  CreditCard,
  Shield,
  Phone,
  Mic,
  Bot
} from 'lucide-react';
import { useAuth } from "../contexts/AuthContext";
import { 
  ARIA_LABELS, 
  announceToScreenReader, 
  focusManagement, 
  createKeyboardNavigationHandler, 
  KEYBOARD_SHORTCUTS 
} from '@/utils/accessibility';

interface SidebarProps {
  currentPage: string;
  onNavigate: (page: string) => void;
}

export default function Sidebar({ currentPage, onNavigate }: SidebarProps) {
  const { user, logout } = useAuth();
  const [focusedIndex, setFocusedIndex] = useState(0);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const navItemsRef = useRef<HTMLButtonElement[]>([]);
  
  const navItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      path: '/dashboard',
      section: 'main'
    },
    {
      id: 'calendar',
      label: 'Calendar',
      icon: Calendar,
      path: '/scheduling',
      section: 'main'
    },
    {
      id: 'screening',
      label: 'Resume Screening',
      icon: FileText,
      path: '/resume-screening',
      section: 'recruitment'
    },
    {
      id: 'job-postings',
      label: 'Jobs',
      icon: Building2,
      path: '/job-postings',
      section: 'recruitment'
    },
    {
      id: 'candidates',
      label: 'Candidates',
      icon: Users,
      path: '/tracker',
      section: 'recruitment'
    },
    {
      id: 'career-site',
      label: 'Career Site',
      icon: Search,
      path: '/candidate-search',
      section: 'recruitment'
    },
    {
      id: 'interview-automation',
      label: 'AI Interviews',
      icon: Bot,
      path: '/interview-automation',
      section: 'recruitment'
    },
    {
      id: 'users',
      label: 'User Management',
      icon: UserCheck,
      path: '/users',
      section: 'organization'
    },
    {
      id: 'sso',
      label: 'SSO Setup',
      icon: Shield,
      path: '/sso',
      section: 'organization'
    },
    {
      id: 'twilio-setup',
      label: 'Voice Agent Setup',
      icon: Phone,
      path: '/twilio-setup',
      section: 'organization'
    },
    {
      id: 'conversational-ai-demo',
      label: 'AI Voice Demo',
      icon: Mic,
      path: '/conversational-ai-demo',
      section: 'organization'
    },
    {
      id: 'reports',
      label: 'Report',
      icon: BarChart3,
      path: '/reports',
      section: 'organization'
    },
    {
      id: 'subscription',
      label: 'Subscription',
      icon: CreditCard,
      path: '/subscription',
      section: 'organization'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      path: '/settings',
      section: 'organization'
    }
  ];

  const superAdminNavItems = [
    { 
      id: 'super-admin', 
      label: 'Platform Management', 
      icon: Settings, 
      path: '/super-admin',
      section: 'admin'
    }
  ];

  const handleLogout = () => {
    announceToScreenReader('Signing out', 'assertive');
    logout();
  };

  // Focus management for keyboard navigation
  useEffect(() => {
    if (focusedIndex >= 0 && navItemsRef.current[focusedIndex]) {
      navItemsRef.current[focusedIndex].focus();
    }
  }, [focusedIndex]);

  // Keyboard navigation handler
  const handleKeyDown = (event: React.KeyboardEvent) => {
    const allItems = [...navItems, ...(user?.role === 'super_admin' ? superAdminNavItems : [])];
    
    switch (event.key) {
      case KEYBOARD_SHORTCUTS.ARROW_DOWN:
        event.preventDefault();
        setFocusedIndex((prev) => Math.min(prev + 1, allItems.length - 1));
        break;
      case KEYBOARD_SHORTCUTS.ARROW_UP:
        event.preventDefault();
        setFocusedIndex((prev) => Math.max(prev - 1, 0));
        break;
      case KEYBOARD_SHORTCUTS.HOME:
        event.preventDefault();
        setFocusedIndex(0);
        break;
      case KEYBOARD_SHORTCUTS.END:
        event.preventDefault();
        setFocusedIndex(allItems.length - 1);
        break;
      case KEYBOARD_SHORTCUTS.ENTER:
      case KEYBOARD_SHORTCUTS.SPACE:
        event.preventDefault();
        const currentItem = allItems[focusedIndex];
        if (currentItem) {
          announceToScreenReader(`Navigating to ${currentItem.label}`, 'polite');
          onNavigate(currentItem.path);
        }
        break;
    }
  };

  const renderNavItem = (item: any, isActive: boolean, index: number) => {
    const Icon = item.icon;
    
    return (
      <Button
        key={item.id}
        ref={(el) => {
          if (el) navItemsRef.current[index] = el;
        }}
        variant="ghost"
        onClick={() => {
          announceToScreenReader(`Navigating to ${item.label}`, 'polite');
          onNavigate(item.path);
        }}
        onKeyDown={handleKeyDown}
        className={`w-full justify-start mb-1 px-3 py-2 h-auto rounded-lg transition-all duration-200 ${
          isActive 
            ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-105' 
            : 'text-slate-700 hover:text-white hover:bg-gradient-to-r hover:from-blue-400 hover:to-purple-500 hover:shadow-md hover:scale-102'
        }`}
        aria-label={`${item.label} navigation item`}
        aria-current={isActive ? 'page' : undefined}
        tabIndex={index === focusedIndex ? 0 : -1}
        role="menuitem"
      >
        <Icon className="w-4 h-4 mr-2" aria-hidden="true" />
        <span className="text-sm ml-[-9px] mr-[-9px]">{item.label}</span>
      </Button>
    );
  };

  const renderSection = (title: string, items: any[]) => {
    if (items.length === 0) return null;
    
    return (
      <div className="mb-6">
        <h3 className="text-xs font-semibold text-indigo-600 uppercase tracking-wide mb-3 px-3">
          {title}
        </h3>
        <div className="space-y-1">
          {items.map((item) => {
            const isActive = currentPage === item.id;
            return renderNavItem(item, isActive, items.indexOf(item));
          })}
        </div>
      </div>
    );
  };

  return (
    <div 
      ref={sidebarRef}
      className="w-64 border-r border-indigo-200 flex flex-col h-screen shadow-xl"
      style={{ backgroundColor: '#A3D3FF' }}
      role="navigation"
      aria-label={ARIA_LABELS.mainNavigation}
    >
      {/* Logo/Brand */}
      <div className="p-6 border-b border-indigo-200 pt-[6px] pb-[6px]">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-700 rounded-lg flex items-center justify-center shadow-lg">
            <span className="text-white font-bold text-sm">S</span>
          </div>
          <span className="ml-2 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Steorra
          </span>
        </div>
      </div>

      {/* Navigation */}
      <div className="flex-1 px-3 py-6 overflow-y-auto">
        {user?.role === 'super_admin' ? (
          // Super Admin Navigation
          <>
            {renderSection('Admin', superAdminNavItems)}
            {renderSection('Main', navItems.filter(item => item.section === 'main'))}
          </>
        ) : (
          // Regular User Navigation
          <>
            {renderSection('Main', navItems.filter(item => item.section === 'main'))}
            {renderSection('Recruitment', navItems.filter(item => item.section === 'recruitment'))}
            {renderSection('Organization', navItems.filter(item => item.section === 'organization'))}
          </>
        )}
      </div>

      {/* User Profile & Logout */}
      <div className="border-t border-indigo-200 p-4 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="flex items-center mb-3">
          <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full flex items-center justify-center shadow-lg">
            <span className="text-xs font-semibold text-white">
              {user?.fullName?.charAt(0)?.toUpperCase() || 'U'}
            </span>
          </div>
          <div className="ml-2 flex-1">
            <p className="text-sm font-medium text-slate-800">
              {user?.fullName || 'User'}
            </p>
            <p className="text-xs text-indigo-600 capitalize font-medium">
              {user?.role?.replace('_', ' ') || 'Member'}
            </p>
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleLogout}
          className="w-full justify-start text-rose-600 hover:text-white hover:bg-gradient-to-r hover:from-rose-500 hover:to-pink-600 rounded-lg transition-all duration-200 hover:shadow-md"
        >
          <LogOut className="w-4 h-4 mr-2" />
          <span className="text-sm pl-[-2px] pr-[-2px] ml-[-10px] mr-[-10px]">Sign Out</span>
        </Button>
      </div>
    </div>
  );
}