import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Bot, Play, Square, MoreHorizontal, RefreshCw, Activity, Clock, User, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface BotSession {
  sessionId: string;
  interviewId: string;
  status: 'starting' | 'connected' | 'in_progress' | 'ending' | 'ended' | 'failed';
  startedAt: string;
  zoomSessionName?: string;
  elevenlabsConversationId?: string;
}

interface Interview {
  id: string;
  role: string;
  scheduledAt: string;
  candidate?: {
    fullName: string;
    email: string;
  };
}

interface BotSessionMonitorProps {
  sessions: BotSession[];
  interviews: Interview[];
  onRefresh: () => void;
}

const BotSessionMonitor: React.FC<BotSessionMonitorProps> = ({
  sessions,
  interviews,
  onRefresh
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedSession, setSelectedSession] = useState<BotSession | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'starting': return 'bg-yellow-100 text-yellow-800';
      case 'connected': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-green-100 text-green-800';
      case 'ending': return 'bg-orange-100 text-orange-800';
      case 'ended': return 'bg-gray-100 text-gray-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'starting': return <Clock className="w-4 h-4" />;
      case 'connected': return <Activity className="w-4 h-4" />;
      case 'in_progress': return <Bot className="w-4 h-4" />;
      case 'ending': return <Square className="w-4 h-4" />;
      case 'ended': return <Square className="w-4 h-4" />;
      case 'failed': return <AlertCircle className="w-4 h-4" />;
      default: return <Bot className="w-4 h-4" />;
    }
  };

  const handleStartBotSession = async (interviewId: string) => {
    setLoading(true);

    try {
      const response = await fetch('/api/bot-runner/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({ interviewId })
      });

      if (response.ok) {
        const data = await response.json();
        toast.success('Bot session started successfully');
        onRefresh();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to start bot session');
      }
    } catch (error) {
      console.error('Error starting bot session:', error);
      toast.error('Failed to start bot session');
    } finally {
      setLoading(false);
    }
  };

  const handleStopBotSession = async (sessionId: string) => {
    if (!confirm('Are you sure you want to stop this bot session?')) {
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/bot-runner/stop', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({ sessionId })
      });

      if (response.ok) {
        toast.success('Bot session stopped successfully');
        onRefresh();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to stop bot session');
      }
    } catch (error) {
      console.error('Error stopping bot session:', error);
      toast.error('Failed to stop bot session');
    } finally {
      setLoading(false);
    }
  };

  const openSessionDetails = (session: BotSession) => {
    setSelectedSession(session);
    setIsDetailsDialogOpen(true);
  };

  const getInterviewForSession = (interviewId: string) => {
    return interviews.find(interview => interview.id === interviewId);
  };

  const activeSessions = sessions.filter(s => ['starting', 'connected', 'in_progress'].includes(s.status));
  const inactiveSessions = sessions.filter(s => ['ending', 'ended', 'failed'].includes(s.status));

  // Get scheduled interviews that don't have active bot sessions
  const scheduledInterviews = interviews.filter(interview => {
    const hasActiveSession = sessions.some(session => 
      session.interviewId === interview.id && 
      ['starting', 'connected', 'in_progress'].includes(session.status)
    );
    return !hasActiveSession && new Date(interview.scheduledAt) > new Date();
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Bot Session Monitor</h2>
          <p className="text-gray-600">Monitor and control AI interview bot sessions</p>
        </div>
        <Button variant="outline" onClick={onRefresh}>
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Active Sessions */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Active Sessions ({activeSessions.length})</h3>
        
        {activeSessions.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {activeSessions.map(session => {
              const interview = getInterviewForSession(session.interviewId);
              return (
                <Card key={session.sessionId} className="border-l-4 border-l-green-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(session.status)}
                          <div>
                            <CardTitle className="text-lg">
                              {interview?.role || 'Unknown Role'}
                            </CardTitle>
                            <CardDescription className="flex items-center space-x-2">
                              <User className="w-4 h-4" />
                              <span>{interview?.candidate?.fullName || 'Unknown Candidate'}</span>
                            </CardDescription>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getStatusColor(session.status)}>
                          {session.status.replace('_', ' ')}
                        </Badge>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openSessionDetails(session)}>
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleStopBotSession(session.sessionId)}
                              className="text-red-600"
                            >
                              <Square className="w-4 h-4 mr-2" />
                              Stop Session
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-2">
                    <div className="text-sm text-gray-600">
                      <p><strong>Session ID:</strong> {session.sessionId.slice(0, 8)}...</p>
                      <p><strong>Started:</strong> {new Date(session.startedAt).toLocaleString()}</p>
                      {session.zoomSessionName && (
                        <p><strong>Zoom Session:</strong> {session.zoomSessionName}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        ) : (
          <Card className="text-center py-8">
            <CardContent>
              <Bot className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Sessions</h3>
              <p className="text-gray-600">All bot sessions are currently inactive</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Manual Start Options */}
      {scheduledInterviews.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Start Bot Session</h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {scheduledInterviews.slice(0, 4).map(interview => (
              <Card key={interview.id} className="border-dashed">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{interview.role}</CardTitle>
                      <CardDescription className="flex items-center space-x-2">
                        <User className="w-4 h-4" />
                        <span>{interview.candidate?.fullName}</span>
                      </CardDescription>
                    </div>
                    <Button 
                      size="sm"
                      onClick={() => handleStartBotSession(interview.id)}
                      disabled={loading}
                    >
                      <Play className="w-4 h-4 mr-2" />
                      Start Bot
                    </Button>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="text-sm text-gray-600">
                    <p><strong>Scheduled:</strong> {new Date(interview.scheduledAt).toLocaleString()}</p>
                    <p><strong>Email:</strong> {interview.candidate?.email}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Recent Sessions */}
      {inactiveSessions.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Recent Sessions</h3>
          <div className="space-y-2">
            {inactiveSessions.slice(0, 5).map(session => {
              const interview = getInterviewForSession(session.interviewId);
              return (
                <Card key={session.sessionId} className="hover:shadow-sm transition-shadow">
                  <CardContent className="py-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(session.status)}
                        <div>
                          <p className="font-medium">{interview?.role || 'Unknown Role'}</p>
                          <p className="text-sm text-gray-600">{interview?.candidate?.fullName}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge className={getStatusColor(session.status)}>
                          {session.status.replace('_', ' ')}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          {new Date(session.startedAt).toLocaleDateString()}
                        </span>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => openSessionDetails(session)}
                        >
                          Details
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Session Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Bot Session Details</DialogTitle>
            <DialogDescription>
              Detailed information about the bot session
            </DialogDescription>
          </DialogHeader>
          
          {selectedSession && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Session Info</h4>
                  <div className="space-y-1 text-sm">
                    <p><strong>Session ID:</strong> {selectedSession.sessionId}</p>
                    <p><strong>Status:</strong> 
                      <Badge className={`ml-2 ${getStatusColor(selectedSession.status)}`}>
                        {selectedSession.status.replace('_', ' ')}
                      </Badge>
                    </p>
                    <p><strong>Started:</strong> {new Date(selectedSession.startedAt).toLocaleString()}</p>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Interview Info</h4>
                  <div className="space-y-1 text-sm">
                    {(() => {
                      const interview = getInterviewForSession(selectedSession.interviewId);
                      return interview ? (
                        <>
                          <p><strong>Role:</strong> {interview.role}</p>
                          <p><strong>Candidate:</strong> {interview.candidate?.fullName}</p>
                          <p><strong>Scheduled:</strong> {new Date(interview.scheduledAt).toLocaleString()}</p>
                        </>
                      ) : (
                        <p className="text-gray-500">Interview details not available</p>
                      );
                    })()}
                  </div>
                </div>
              </div>

              {selectedSession.zoomSessionName && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Zoom Integration</h4>
                  <div className="space-y-1 text-sm">
                    <p><strong>Session Name:</strong> {selectedSession.zoomSessionName}</p>
                  </div>
                </div>
              )}

              {selectedSession.elevenlabsConversationId && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">ElevenLabs Integration</h4>
                  <div className="space-y-1 text-sm">
                    <p><strong>Conversation ID:</strong> {selectedSession.elevenlabsConversationId}</p>
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-3 pt-4">
                <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
                  Close
                </Button>
                {['starting', 'connected', 'in_progress'].includes(selectedSession.status) && (
                  <Button 
                    variant="destructive"
                    onClick={() => {
                      handleStopBotSession(selectedSession.sessionId);
                      setIsDetailsDialogOpen(false);
                    }}
                  >
                    <Square className="w-4 h-4 mr-2" />
                    Stop Session
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BotSessionMonitor;
