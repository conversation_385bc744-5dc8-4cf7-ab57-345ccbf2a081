Step 1 — Prove the webhook/TwiML path (no streaming, no EL)

Point <PERSON><PERSON><PERSON>’s voice webhook to an endpoint that returns immediate static TwiML. Keep it dumb and fast.
app.post('/twiml-static', (req, res) => {
  res.set('Content-Type', 'text/xml');
  res.status(200).send(
    `<?xml version="1.0" encoding="UTF-8"?>
     <Response>
       <Say>This is a connectivity test. Staying on the line for five seconds.</Say>
       <Pause length="5"/>
     </Response>`
  );
});


Step 2 — Add StatusCallbacks (truth from <PERSON><PERSON><PERSON>’s side)

When you originate the call (do this server-side, not from React), include:
client.calls.create({
  to: calleeNumber,
  from: yourTwilioNumber,
  url: 'https://<your-replit>/twiml-static',   // or later /twiml-stream
  statusCallback: 'https://<your-replit>/twilio-status',
  statusCallbackEvent: ['initiated','ringing','answered','completed']
});

And log this:

app.post('/twilio-status', (req, res) => {
  console.log('StatusCB', req.body.CallSid, req.body.CallStatus, req.body.AnsweredBy, req.body.Duration);
  res.sendStatus(200);
});
This tells you who ended the call and when.

Step 3 — Prove <Connect><Stream> without ElevenLabs

Create a dummy WS handler that accepts Twilio Media Streams and keeps the socket open for ~10–20s. No EL code yet.

app.post('/twiml-stream', (req, res) => {
  res.set('Content-Type', 'text/xml');
  res.status(200).send(
    `<?xml version="1.0" encoding="UTF-8"?>
     <Response>
       <Say>Connecting to test stream.</Say>
       <Connect>
         <Stream url="wss://<your-replit>/twilio-stream"/>
       </Connect>
     </Response>`
  );
});

import WebSocket, { WebSocketServer } from 'ws';

const wss = new WebSocketServer({ server: httpServer, path: '/twilio-stream' });

wss.on('connection', (ws, req) => {
  let frames = 0, callSid = 'unknown';
  const keepAlive = setInterval(() => { try { ws.ping(); } catch(_){} }, 15000);

  ws.on('message', (buf) => {
    try {
      const msg = JSON.parse(buf.toString());
      if (msg.event === 'start') { callSid = msg.start?.callSid || callSid; console.log('WS start', callSid); }
      if (msg.event === 'media') { frames++; if (frames % 100 === 0) console.log('WS media frames', frames); }
      if (msg.event === 'stop')  { console.log('WS stop', callSid); ws.close(); }
    } catch (e) { console.error('WS parse error', e); }
  });

  ws.on('close', () => { clearInterval(keepAlive); console.log('WS closed', callSid, 'frames:', frames); });
  ws.on('error', (e) => console.error('WS error', e));

  // DO NOT close the socket yourself early. Let Twilio close when call ends.
});
Expected: Call connects, you hear “Connecting…”, then silence; the call should stay up ~10+ seconds while WS stays open.

If it drops immediately now but Step 1 was fine: your WS path (Replit proxy, TLS, headers, or handler) is closing early → fix WS lifecycle/keep-alive.

Step 4 — Only then wire in ElevenLabs

Wrap EL connect/TTs in try/catch and never close the Twilio WS on EL errors. If EL fails, just keep the WS open and optionally inject a fallback <Say> via a redirect later—don’t terminate the call.

Key interop notes:

Twilio sends 8kHz μ-law PCM (base64 in media.payload). Most EL realtime paths want 16k+ PCM. You need μ-law→PCM decode and resample to 16k before sending to EL; reverse for EL → Twilio if you talk back over WS. If you skip this, your adapter may crash silently.

Back-pressure: buffer/queue frames; never throw on transient EL stalls.

Keep-alive: WS ping/pong every ~15s. Some Replit proxies kill idle WS quickly.
