import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer } from 'ws';
import path from 'path';
import { storage } from "./storage";
import resumeRoutes from "./api/resume";
import multiTenantAuthRoutes from "./api/auth";
import candidateRoutes from "./api/candidates";
import { authenticateToken } from "./auth";
import availabilityRoutes from "./api/availability";
import webhookRoutes from "./api/webhooks";
import gmailRoutes from "./api/gmail";
import { TwilioMediaStreamHandler } from './services/twilioMediaStreamHandler';
import { VoiceCallManager } from './services/voiceCallManager';

export async function registerRoutes(app: Express): Promise<Server> {
  // API routes with detailed logging
  console.log('Registering API routes...');
  app.use('/api/resume', resumeRoutes);
  app.use('/api/auth', multiTenantAuthRoutes);
  console.log('Multi-tenant auth routes registered at /api/auth');
  app.use('/api/candidates', candidateRoutes);
  app.use('/api/availability', availabilityRoutes);
  app.use('/api/webhooks', webhookRoutes);
  app.use('/api/gmail', gmailRoutes);
  console.log('Gmail routes registered at /api/gmail');
  
  const calendarRoutes = (await import('./api/calendar')).default;
  app.use('/api/calendar', calendarRoutes);
  console.log('Calendar routes registered at /api/calendar');

  // Job postings routes
  const jobPostingsRoutes = (await import('./api/job-postings')).default;
  app.use('/api/job-postings', jobPostingsRoutes);
  console.log('Job postings routes registered at /api/job-postings');

  // Candidate search routes
  const candidateSearchRoutes = (await import('./api/candidate-search')).default;
  app.use('/api/candidates', candidateSearchRoutes);
  console.log('Candidate search routes registered at /api/candidates');

  // LinkedIn test routes
  const linkedinTestRoutes = (await import('./api/linkedin-test')).linkedinTestRouter;
  app.use('/api/linkedin-test', linkedinTestRoutes);
  console.log('LinkedIn test routes registered at /api/linkedin-test');

  // Zoom Video SDK routes
  const zoomSDKRoutes = (await import('./api/zoom-sdk')).default;
  app.use('/api/zoom', zoomSDKRoutes);
  console.log('Zoom SDK routes registered at /api/zoom');

  // Zoom webhooks
  const zoomWebhookRoutes = (await import('./api/zoom-webhooks')).default;
  app.use('/api/webhooks', zoomWebhookRoutes);
  console.log('Zoom webhook routes registered at /api/webhooks');

  // Agent profiles routes
  const agentProfileRoutes = (await import('./api/agent-profiles')).default;
  app.use('/api/agent-profiles', agentProfileRoutes);
  console.log('Agent profile routes registered at /api/agent-profiles');

  // Bot runner routes
  const botRunnerRoutes = (await import('./api/bot-runner')).default;
  app.use('/api/bot-runner', botRunnerRoutes);
  console.log('Bot runner routes registered at /api/bot-runner');

  // Serve bot client page
  app.get('/bot-client', (req, res) => {
    res.sendFile(path.join(process.cwd(), 'client/public/bot-client.html'));
  });

  // Interview orchestration routes
  const interviewsV2Routes = (await import('./api/interviews-v2')).default;
  app.use('/api/interviews-v2', interviewsV2Routes);
  console.log('Interviews V2 routes registered at /api/interviews-v2');

  // ElevenLabs routes
  const elevenlabsRoutes = (await import('./api/elevenlabs')).default;
  app.use('/api/elevenlabs', elevenlabsRoutes);
  console.log('ElevenLabs routes registered at /api/elevenlabs');

  // Transcription routes
  const transcriptionRoutes = (await import('./api/transcription')).default;
  app.use('/api/transcription', transcriptionRoutes);
  console.log('Transcription routes registered at /api/transcription');

  // Multi-tenant organization routes
  const organizationRoutes = (await import('./api/organizations')).default;
  app.use('/api/organizations', organizationRoutes);
  console.log('Organization management routes registered at /api/organizations');

  // Organization search routes (for registration)
  const organizationSearchRoutes = (await import('./api/organization-search')).default;
  app.use('/api/organizations', organizationSearchRoutes);
  console.log('Organization search routes registered at /api/organizations');

  // User management routes
  const userRoutes = (await import('./api/users')).default;
  app.use('/api/users', userRoutes);
  console.log('User management routes registered at /api/users');

  // Enhanced user management routes
  const userManagementRoutes = (await import('./user-management-routes')).default;
  app.use('/api/users', userManagementRoutes);
  console.log('Enhanced user management routes registered at /api/users');

  // SSO routes
  const ssoRoutes = (await import('./sso-routes')).default;
  app.use('/api/sso', ssoRoutes);
  console.log('SSO routes registered at /api/sso');

  // Organization routes
  const organizationRoutes2 = (await import('./organization-routes')).default;
  app.use('/api/organizations', organizationRoutes2);
  console.log('Enhanced organization routes registered at /api/organizations');

  // Super admin routes
  const superAdminRoutes = (await import('./api/super-admin')).default;
  app.use('/api/super-admin', superAdminRoutes);
  console.log('Super admin routes registered at /api/super-admin');

  // Admin dashboard routes (Phase 2)
  const adminDashboardRoutes = (await import('./api/admin-dashboard')).default;
  app.use('/api/admin-dashboard', adminDashboardRoutes);
  console.log('Admin dashboard routes registered at /api/admin-dashboard');

  // Subscription management routes
  const subscriptionRoutes = (await import('./api/subscription')).default;
  app.use('/api/subscription', subscriptionRoutes);
  console.log('Subscription management routes registered at /api/subscription');

  // Voice agent routes
  const voiceAgentRoutes = (await import('./api/voice-agent')).default;
  app.use('/api/voice-agent', voiceAgentRoutes);
  console.log('Voice agent routes registered at /api/voice-agent');

  // Call summaries routes
  const callSummariesRoutes = (await import('./api/call-summaries')).default;
  app.use('/api/call-summaries', callSummariesRoutes);
  console.log('Call summaries routes registered at /api/call-summaries');

  // Manual trigger summary route (for debugging)
  const { manualTriggerSummary } = await import('./api/manual-trigger-summary');
  app.post('/api/manual-trigger-summary', manualTriggerSummary);
  console.log('Manual trigger summary route registered at /api/manual-trigger-summary');

  // Voice providers routes
  const voiceProvidersRoutes = (await import('./api/voice-providers')).default;
  app.use('/api/voice-providers', voiceProvidersRoutes);
  console.log('Voice providers routes registered at /api/voice-providers');

  // Twilio setup routes
  const twilioSetupRoutes = (await import('./api/twilio-setup')).default;
  app.use('/api/twilio-setup', twilioSetupRoutes);
  console.log('Twilio setup routes registered at /api/twilio-setup');

  // Add session-based auth endpoint for collaboration
  app.get('/api/auth/user', authenticateToken, (req: any, res) => {
    try {
      res.json({ user: req.user });
    } catch (error) {
      console.error('Error getting current user:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  // Annotations routes
  const annotationsRoutes = (await import('./api/annotations')).default;
  app.use('/api/annotations', annotationsRoutes);
  console.log('Annotations routes registered at /api/annotations');

  const filesRoutes = (await import('./api/files')).default;
  app.use('/api/files', filesRoutes);
  console.log('Files routes registered at /api/files');

  // Email cleanup routes
  const emailCleanupRoutes = (await import('./api/email-cleanup')).default;
  app.use('/api/email-cleanup', emailCleanupRoutes);
  console.log('Email cleanup routes registered at /api/email-cleanup');

  // Gmail admin routes for comprehensive email management
  const gmailAdminRoutes = (await import('./api/gmail-admin')).default;
  app.use('/api/gmail-admin', gmailAdminRoutes);
  console.log('Gmail admin routes registered at /api/gmail-admin');

  // Test route
  app.get('/api/test', (req, res) => {
    res.json({ message: 'API is working' });
  });
  
  // WebSocket connectivity test routes
  const websocketTestRoutes = (await import('./api/websocket-test')).default;
  app.use('/api/websocket-test', websocketTestRoutes);
  console.log('WebSocket test routes registered at /api/websocket-test');
  
  // TwiML static test routes for step-by-step debugging
  const twimlStaticRoutes = (await import('./api/twiml-static-test')).default;
  app.use('/api', twimlStaticRoutes);
  console.log('TwiML static test routes registered at /api/twiml-static');
  
  // Simple WebSocket test routes
  const websocketSimpleRoutes = (await import('./api/websocket-simple-test')).default;
  app.use('/api', websocketSimpleRoutes);
  console.log('Simple WebSocket test routes registered at /api/test-simple-ws');
  
  // Serve test WebSocket client
  app.get('/test-websocket-client', (req, res) => {
    res.sendFile(path.join(process.cwd(), 'test-websocket-client.html'));
  });

  // 🎯 ElevenLabs webhook routes (NO AUTHENTICATION - external service calls)
  const elevenLabsWebhookRoutes = (await import('./api/elevenlabs-webhooks')).default;
  app.use('/api/elevenlabs', elevenLabsWebhookRoutes);
  console.log('ElevenLabs webhook routes registered at /api/elevenlabs');

  const httpServer = createServer(app);

  // DISABLED: Multiple WebSocket servers cause conflicts
  // We now use a unified WebSocket server in index.ts
  console.log('Legacy WebSocket servers disabled - using unified server');

  return httpServer;
}
