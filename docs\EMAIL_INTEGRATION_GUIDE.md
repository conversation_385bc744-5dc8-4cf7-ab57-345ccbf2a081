# Email Integration Options for HR Workflow System

## Option 1: Gmail API Integration (Recommended for Development)

### Setup Steps:

1. **Create Google Cloud Project**
   ```
   - Go to https://console.cloud.google.com/
   - Create new project or select existing one
   - Enable Gmail API
   - Create credentials (OAuth 2.0 Client ID)
   ```

2. **Configure OAuth Consent Screen**
   ```
   - Set application name: "HR Workflow System"
   - Add your email as test user
   - Add scopes: gmail.readonly, gmail.send
   ```

3. **Get Credentials**
   ```
   - Download client_secret.json
   - Add to project as GOOGLE_CREDENTIALS environment variable
   ```

4. **Install Dependencies**
   ```bash
   npm install googleapis nodemailer
   ```

5. **Implementation**
   - Monitor specific Gmail folder for responses
   - Parse emails for candidate responses
   - Update database automatically

### Webhook URL:
`https://your-replit-domain.replit.app/api/webhooks/gmail-webhook`

---

## Option 2: Email Forwarding + Webhook (Simplest)

### Setup Steps:

1. **Use Your Personal Gmail**
   - Create filter to forward emails containing "Candidate ID:"
   - Forward to a webhook service

2. **Webhook Service Options:**
   - **Zapier**: Connect Gmail → Webhook
   - **n8n.io**: Free automation platform
   - **Pipedream**: Email trigger → HTTP request

3. **Configuration Example (Zapier):**
   ```
   Trigger: New Email in Gmail (with filter)
   Action: Webhook POST to your endpoint
   URL: https://your-replit-domain.replit.app/api/webhooks/email-response
   ```

4. **Email Filter Setup:**
   ```
   Gmail → Settings → Filters and Blocked Addresses
   Filter: subject:("Interview Availability" OR "Candidate ID:")
   Action: Forward to zapier email or webhook service
   ```

---

## Option 3: IMAP Email Polling (Self-contained)

### Setup Steps:

1. **Use App-specific Password (Gmail)**
   ```
   - Enable 2FA on your Gmail account
   - Generate app-specific password
   - Use this password for IMAP access
   ```

2. **Install Dependencies**
   ```bash
   npm install imap mailparser
   ```

3. **Environment Variables**
   ```
   EMAIL_HOST=imap.gmail.com
   EMAIL_PORT=993
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-specific-password
   ```

4. **Implementation**
   - Poll IMAP folder every 30 seconds
   - Parse new emails for candidate responses
   - Mark emails as read after processing

---

## Option 4: Dedicated Email Service (Production Ready)

### Using SendGrid Inbound Parse

1. **Setup SendGrid Account**
   - Sign up at sendgrid.com
   - Verify domain ownership

2. **Configure Inbound Parse**
   ```
   - Add webhook URL: https://your-domain.com/api/webhooks/sendgrid
   - Set subdomain: hr-responses.yourdomain.com
   - Enable spam check and send raw email
   ```

3. **DNS Configuration**
   ```
   Add MX record:
   Name: hr-responses
   Value: mx.sendgrid.net
   Priority: 10
   ```

4. **Update Email Templates**
   ```
   Reply-to: <EMAIL>
   ```

---

## Recommended Implementation for Your Use Case

Given your requirements, I recommend **Option 2 (Email Forwarding + Webhook)** for immediate implementation:

### Quick Setup Steps:

1. **Create Zapier Account** (free tier available)

2. **Create Gmail Filter:**
   ```
   - Go to Gmail Settings → Filters
   - Create filter: subject:("Interview Availability Request")
   - Action: Forward to Zapier email
   ```

3. **Configure Zapier Webhook:**
   ```
   Trigger: Gmail (New Email)
   Filter: Subject contains "Interview Availability Request"
   Action: Webhooks (POST)
   URL: https://your-replit-domain.replit.app/api/webhooks/email-response
   Payload: 
   {
     "from": "{{From}}",
     "subject": "{{Subject}}",
     "text": "{{Body Plain}}",
     "html": "{{Body HTML}}"
   }
   ```

4. **Test the Integration:**
   - Send availability request email
   - Reply with candidate information
   - Check if response appears in scheduling tab

### Current Webhook Endpoint

Your system already has the webhook endpoint ready:
```
POST /api/webhooks/email-response
```

This endpoint parses:
- Candidate ID from email content
- Preferred day and time
- Additional notes
- Automatically updates database

### Next Steps

1. Choose your preferred option above
2. Set up the email forwarding/webhook service
3. Test with a real email response
4. Monitor the scheduling tab for automatic updates

Would you like me to implement any specific option or help you set up the webhook integration?