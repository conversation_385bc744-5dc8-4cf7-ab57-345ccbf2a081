import { WebSocket } from "ws";
import { IncomingMessage } from "http";
import { db } from "../db";
import {
  voiceCalls,
  voiceCallNotes,
  candidates,
  jobPostings,
  organizations,
} from "@shared/schema";
import { eq } from "drizzle-orm";
import { elevenLabsService } from "../services/elevenlabsService";
import twilio from 'twilio';

// 🔒 SECURITY: PII Sanitization Utilities
const isDevelopment = process.env.NODE_ENV === 'development';

// Sanitize sensitive data from logs
function sanitizePII(data: any): any {
  if (typeof data === 'string') {
    return data
      // Remove phone numbers (various formats)
      .replace(/\+?1?[-\s\.]?\(?[0-9]{3}\)?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4}/g, '[PHONE_REDACTED]')
      // Remove email addresses
      .replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, '[EMAIL_REDACTED]')
      // Remove potential account SIDs/tokens
      .replace(/AC[a-z0-9]{32}/gi, '[ACCOUNT_SID_REDACTED]')
      .replace(/SK[a-z0-9]{32}/gi, '[TOKEN_REDACTED]')
      // Remove other sensitive patterns
      .replace(/\b[A-Z]{2}[a-z0-9]{32}\b/g, '[SENSITIVE_ID_REDACTED]');
  }
  
  if (typeof data === 'object' && data !== null) {
    const sanitized: any = Array.isArray(data) ? [] : {};
    for (const key in data) {
      if (key.toLowerCase().includes('phone') || 
          key.toLowerCase().includes('number') ||
          key.toLowerCase().includes('caller') ||
          key.toLowerCase().includes('from') ||
          key.toLowerCase().includes('to')) {
        sanitized[key] = '[PII_REDACTED]';
      } else {
        sanitized[key] = sanitizePII(data[key]);
      }
    }
    return sanitized;
  }
  
  return data;
}

// Secure logging function that only logs in development
function secureLog(level: 'log' | 'error' | 'warn', message: string, data?: any) {
  if (isDevelopment) {
    const sanitizedData = data ? sanitizePII(data) : undefined;
    console[level](message, sanitizedData);
  } else {
    // In production, only log critical errors without sensitive data
    if (level === 'error') {
      console.error(message.replace(/[📨🚀🔍✅❌⚠️🎯💾🎤🤖]/g, ''));
    }
  }
}

// Safe JSON stringify that sanitizes PII
function safeStringify(obj: any, replacer?: any, space?: number): string {
  try {
    const sanitized = sanitizePII(obj);
    return JSON.stringify(sanitized, replacer, space);
  } catch (error) {
    return '[STRINGIFY_ERROR]';
  }
}

// Initialize Twilio client for call management
const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

// Audio conversion utilities
function mulawToLinear(mulaw: number): number {
  mulaw = ~mulaw;
  let t = ((mulaw & 0x0f) << 3) + 0x84;
  t = t << ((mulaw & 0x70) >> 4);
  return mulaw & 0x80 ? 0x84 - t : t - 0x84;
}

function convertMulawToPCM(mulawData: Buffer): Buffer {
  const pcmData = Buffer.alloc(mulawData.length * 2);
  for (let i = 0; i < mulawData.length; i++) {
    const linear = mulawToLinear(mulawData[i]);
    pcmData.writeInt16LE(linear, i * 2);
  }
  return pcmData;
}

// Upsample from 8kHz to 16kHz by duplicating samples
function upsample8kTo16k(pcm8k: Buffer): Buffer {
  const pcm16k = Buffer.alloc(pcm8k.length * 2);
  for (let i = 0; i < pcm8k.length; i += 2) {
    const sample = pcm8k.readInt16LE(i);
    // Write each sample twice to double the sample rate
    pcm16k.writeInt16LE(sample, i * 2);
    pcm16k.writeInt16LE(sample, i * 2 + 2);
  }
  return pcm16k;
}

export function handleElevenLabsStreamFinal(
  ws: WebSocket,
  req: IncomingMessage,
) {
  let frames = 0;
  let callSid = "unknown";
  let callId: string | null = null; // Database call ID
  let streamSid: string | null = null;
  let elevenLabsWs: WebSocket | null = null;
  let lastSpeechDetected = false;
  let silenceFrames = 0;
  let connectionStartTime = Date.now();

  // Variables to store context from Twilio parameters - WILL BE POPULATED FROM DATABASE
  let candidateName = "candidate";
  let jobTitle = "the position";
  let companyName = "our company";
  let callPurpose = "interview_scheduling";
  let customParams: any = {}; // Store custom parameters for access in sendConfiguration

  // 🎯 IDLE TIMER FOR NATURAL CALL ENDING
  let endTimer: NodeJS.Timeout | null = null;
  const END_IDLE_MS = 4000; // 4s of no speech after agent finishes (increased to give candidate more thinking time)
  
  // 🎯 CONNECTION STATE TRACKER TO PREVENT MULTIPLE CONNECTIONS
  let isConnecting = false;
  let hasConnectedOnce = false;
  let hasReconnected = false;
  
  function scheduleEnd() {
    if (endTimer) clearTimeout(endTimer);
    endTimer = setTimeout(() => hangupNow(callSid), END_IDLE_MS);
  }
  
  function cancelEnd() {
    if (endTimer) { 
      clearTimeout(endTimer); 
      endTimer = null; 
    }
  }

  async function hangupNow(callSid: string) {
    secureLog('log', "🎬 Hanging up call naturally", isDevelopment ? { callSid } : undefined);
    try {
      // 🎯 COORDINATION FIX: Let enhanced voice manager handle call finalization first
      // This prevents double-processing and ensures proper transcription aggregation
      if (callId) {
        try {
          const { enhancedVoiceCallManager } = await import('../services/enhancedVoiceCallManager');
          await enhancedVoiceCallManager.finalizeCall(callId, {
            status: 'completed',
            endReason: 'natural_hangup_from_stream_handler'
          });
          secureLog('log', "✅ Call finalized via enhanced voice manager", isDevelopment ? { callId } : undefined);
        } catch (managerError) {
          secureLog('error', "⚠️ Enhanced voice manager finalization failed, using fallback", isDevelopment ? managerError : undefined);
          // Continue to fallback Twilio termination
        }
      } else {
        secureLog('log', "⚠️ No callId available for enhanced voice manager finalization");
      }
      
      // 🔄 DEFINITIVE CALL TERMINATION: Use Twilio REST API to end the call
      await twilioClient.calls(callSid).update({ status: "completed" });
      secureLog('log', "☎️ Call ended via Twilio API", isDevelopment ? { callSid } : undefined);
      
      // Send hangup message to Twilio WebSocket
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ event: 'hangup', callSid }));
      }
      
      // Close ElevenLabs connection
      if (elevenLabsWs && elevenLabsWs.readyState === WebSocket.OPEN) {
        elevenLabsWs.close();
      }
      
      // Close main WebSocket
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    } catch (error) {
      secureLog('error', "❌ Failed to end call", isDevelopment ? error : undefined);
    }
  }

  // 🎯 CRITICAL FIX: Database context variables
  let candidateInfo: any = null;
  let jobInfo: any = null;
  let orgInfo: any = null;
  let conversationState: any = null; // Full conversation context for AI

  // 💾 SAVE TRANSCRIPT/RESPONSE AS NOTES IN DATABASE
  const saveTranscriptAsNote = async (
    content: string,
    noteType: "user_input" | "agent_response",
  ) => {
    try {
      // 🔒 SECURITY: Only log content preview in development
      secureLog('log', `💾 🚀 ATTEMPTING TO SAVE ${noteType.toUpperCase()} NOTE`, {
        callId,
        contentPreview: isDevelopment ? content?.substring(0, 50) : '[CONTENT_REDACTED]',
        contentLength: content?.length
      });

      if (!callId) {
        secureLog('error', "❌ Cannot save transcript: No callId available");
        return;
      }

      if (!content?.trim()) {
        secureLog('error', "❌ Cannot save transcript: Empty content");
        return;
      }

      // 🎯 CRITICAL DEBUG: Check all organization ID sources (development only)
      secureLog('log', "🔍 DETAILED ORG ID DEBUG", {
        "orgInfo?.id": orgInfo?.id,
        "candidateInfo?.organizationId": candidateInfo?.organizationId,
        "jobInfo?.organizationId": jobInfo?.organizationId,
        "customParams.organization_id": customParams.organization_id,
        "callId": callId,
        "noteType": noteType,
        "contentLength": content?.trim()?.length
      });

      const organizationId = orgInfo?.id || 
                            candidateInfo?.organizationId || 
                            jobInfo?.organizationId ||
                            customParams.organization_id;

      secureLog('log', "🎯 FINAL ORGANIZATION ID SELECTED", { organizationId });

      if (!organizationId) {
        secureLog('error', "❌ No organization ID available for saving transcript", {
          orgInfoExists: !!orgInfo,
          orgInfoId: orgInfo?.id,
          candidateInfoExists: !!candidateInfo,
          candidateOrgId: candidateInfo?.organizationId,
          jobInfoExists: !!jobInfo,
          jobOrgId: jobInfo?.organizationId,
          customParamsExists: !!customParams,
          customOrgId: customParams.organization_id
        });
        return;
      }

      const noteTypeDb = noteType === "user_input" ? "transcription" : "ai_response";
      // 🔒 SECURITY: Only log content preview in development
      secureLog('log', "🎯 ATTEMPTING TO SAVE TRANSCRIPT WITH", {
        callId,
        organizationId,
        noteType: noteTypeDb,
        contentPreview: isDevelopment ? content.substring(0, 30) : '[CONTENT_REDACTED]'
      });

      const result = await db.insert(voiceCallNotes).values({
        callId: callId,
        organizationId: organizationId,
        noteType: noteTypeDb,
        content: content.trim(),
        importance: 3,
        actionRequired: false,
        aiGenerated: noteType === "agent_response",
      });
      
      // 🔒 SECURITY: Only log content in development
      secureLog('log', `💾 ✅ SUCCESSFULLY SAVED ${noteType.toUpperCase()}`, {
        contentPreview: isDevelopment ? `"${content.substring(0, 50)}..."` : '[CONTENT_REDACTED]',
        noteType: noteTypeDb
      });
    } catch (error) {
      secureLog('error', `❌ SAVE TRANSCRIPT ERROR for ${noteType}`, isDevelopment ? error : undefined);
    }
  };

  // DEBUG: Log initial state (development only)
  secureLog('log', "🔧 ElevenLabs Stream Handler initialized", {
    callSid: "to be set",
    callId: "to be set"
  });

  // ElevenLabs WebSocket connected

  // Keep connection alive - CRITICAL for call stability
  const keepAlive = setInterval(() => {
    try {
      if (ws.readyState === WebSocket.OPEN) {
        ws.ping();
        // Removed ping logging to reduce noise
      }
    } catch (error) {
      // Ping failed, connection likely closed
      clearInterval(keepAlive);
    }
  }, 15000);

  ws.on("message", async (buffer: Buffer) => {
    try {
      const msg = JSON.parse(buffer.toString());

      // Message received

      // console.log('📨 STEP 4: Received message type:', msg.event); // Commented to reduce log noise

      // Handle Twilio events

      if (msg.event === "connected") {
        // Twilio WebSocket connected
      }

      if (msg.event === "start") {
        // 🔒 SECURITY: NEVER log full Twilio start message - contains phone numbers and account SIDs
        secureLog('log', "🚀 Call start event received", {
          hasCallSid: !!msg.start?.callSid,
          hasStreamSid: !!msg.start?.streamSid,
          hasCustomParams: !!msg.start?.customParameters
        });

        callSid = msg.start?.callSid || callSid;
        streamSid = msg.start?.streamSid;

        // 🎯 SIMPLIFIED: Extract only job_title from customParameters
        customParams = msg.start?.customParameters || {}; // Store in module-level variable

        // 🔒 SECURITY: Only log parameter values in development
        secureLog('log', "📋 Extracted parameters", {
          job_title: isDevelopment ? customParams.job_title : '[REDACTED]',
          job_title_type: typeof customParams.job_title
        });

        if (customParams.job_title) {
          jobTitle = customParams.job_title;
          secureLog('log', "✅ FOUND job_title parameter", { jobTitle: isDevelopment ? jobTitle : '[REDACTED]' });
        } else {
          secureLog('log', "❌ NO job_title parameter found in start message");
        }

        // 🚨 CRITICAL FIX: FETCH ACTUAL DATABASE DATA
        secureLog('log', "🔍 FETCHING REAL DATA FROM DATABASE", { 
          jobTitle: isDevelopment ? jobTitle : '[REDACTED]' 
        });
        
        try {
          // 🎯 CRITICAL FIX: Lookup call ID from Twilio SID for transcription saving
          if (callSid && callSid !== "unknown") {
            const callResult = await db
              .select({ id: voiceCalls.id })
              .from(voiceCalls)
              .where(eq(voiceCalls.twilioCallSid, callSid))
              .limit(1);
            
            if (callResult.length > 0) {
              callId = callResult[0].id;
              secureLog('log', "✅ FOUND call record", { callId });
            } else {
              secureLog('log', "⚠️ No call record found for Twilio SID", { hasSid: !!callSid });
            }
          } else {
            secureLog('log', "⚠️ No valid callSid available for call lookup");
          }

          // Query database for job posting by title
          const jobResults = await db.select().from(jobPostings)
            .where(eq(jobPostings.title, jobTitle))
            .limit(1);
          
          if (jobResults.length > 0) {
            jobInfo = jobResults[0];
            secureLog('log', "✅ FOUND job posting in database", {
              title: isDevelopment ? jobInfo.title : '[REDACTED]',
              companyOverview: isDevelopment ? jobInfo.companyOverview?.substring(0, 50) : '[REDACTED]',
              experienceLevel: jobInfo.experienceLevel,
              salaryRange: isDevelopment ? jobInfo.salaryRange : '[REDACTED]',
              organizationId: jobInfo.organizationId
            });
            
            // Now fetch organization data
            const orgResults = await db.select().from(organizations)
              .where(eq(organizations.id, jobInfo.organizationId))
              .limit(1);
              
            if (orgResults.length > 0) {
              orgInfo = orgResults[0];
              companyName = orgInfo.name; // Use real company name!
              secureLog('log', "✅ FOUND organization in database", {
                name: isDevelopment ? orgInfo.name : '[REDACTED]',
                id: orgInfo.id
              });
            } else {
              secureLog('log', "❌ Organization not found for job");
              companyName = "Our Company"; // Fallback
            }
          } else {
            secureLog('log', "❌ Job posting not found in database", { 
              title: isDevelopment ? jobTitle : '[REDACTED]' 
            });
            companyName = "Our Company"; // Fallback
          }
          
          // Optional: Fetch candidate data if available
          if (customParams.candidate_id) {
            const candidateResults = await db.select().from(candidates)
              .where(eq(candidates.id, customParams.candidate_id))
              .limit(1);
              
            if (candidateResults.length > 0) {
              candidateInfo = candidateResults[0];
              candidateName = candidateInfo.fullName;
              secureLog('log', "✅ FOUND candidate in database", {
                name: isDevelopment ? candidateInfo.fullName : '[NAME_REDACTED]'
              });
            }
          }
          
        } catch (dbError) {
          secureLog('error', "❌ Database query failed", isDevelopment ? dbError : undefined);
          // Set fallback values
          candidateName = "Candidate";
          companyName = "Our Company";
        }
        
        // Set call purpose 
        callPurpose = "screening";
        
        // 🚨 CRITICAL: Connect to ElevenLabs AFTER database fetch completes
        secureLog('log', "🎯 Database fetch completed, now connecting to ElevenLabs with real data");
        try {
          await connectToElevenLabs();
        } catch (error) {
          secureLog('error', "❌ ElevenLabs connection failed", 
            isDevelopment ? (error as Error)?.message || error : undefined
          );
          sendFallbackAudio();
        }

        // jobTitle should already be set from customParams.job_title above
        if (!jobTitle) {
          jobTitle = "Software Engineer"; // Fallback
          secureLog('log', "⚠️ Using fallback job_title");
        }

        secureLog('log', "📍 COMPLETE DATABASE CONTEXT", {
          candidateName: isDevelopment ? candidateName : '[NAME_REDACTED]',
          jobTitle: isDevelopment ? jobTitle : '[TITLE_REDACTED]',
          companyName: isDevelopment ? companyName : '[COMPANY_REDACTED]',
          callPurpose,
          hasJobInfo: !!jobInfo,
          hasOrgInfo: !!orgInfo,
          hasCandidateInfo: !!candidateInfo
        });
      }

      if (msg.event === "media" && msg.media?.payload) {
        frames++;

        // Audio frame received

        // ANALYZE INCOMING AUDIO FROM TWILIO TO DETECT CANDIDATE SPEECH
        const mulawBuffer = Buffer.from(msg.media.payload, "base64");

        // Detect if candidate is actually speaking by checking audio amplitude
        let speechDetected = false;
        let maxAmplitude = 0;

        // Convert μ-law samples to linear values to check amplitude
        for (let i = 0; i < mulawBuffer.length; i++) {
          const linear = mulawToLinear(mulawBuffer[i]);
          const amplitude = Math.abs(linear);
          if (amplitude > maxAmplitude) maxAmplitude = amplitude;
        }

        // Consider speech if amplitude is above threshold - OPTIMIZED for better candidate detection
        speechDetected = maxAmplitude > 300; // Increased threshold to reduce false positives and interruptions

        // 🎯 CANCEL END TIMER WHEN USER SPEAKS
        if (speechDetected) {
          cancelEnd(); // user is talking again; don't hang up
        }

        // Track silence frames for conversation management
        if (speechDetected) {
          silenceFrames = 0;
        } else {
          silenceFrames++;
        }

        // Handle conversation flow - OPTIMIZED to prevent interruptions
        if (speechDetected !== lastSpeechDetected) {
          if (
            silenceFrames > 500 && // Increased from 200 to 500 frames (~10 seconds) to allow candidate thinking time
            silenceFrames === 501 &&
            elevenLabsWs?.readyState === WebSocket.OPEN
          ) {
            const continuePrompt = {
              type: "input_text",
              text: "[silence - continue conversation]",
            };
            try {
              elevenLabsWs.send(JSON.stringify(continuePrompt));
            } catch (promptError) {
              secureLog('error', "❌ Failed to send continuation prompt", 
                isDevelopment ? promptError : undefined
              );
            }
          }
          lastSpeechDetected = speechDetected;
        }

        // Connect on FIRST media frame if not connected (FALLBACK - should already be connected from start message)
        if (
          frames === 1 &&
          (!elevenLabsWs || elevenLabsWs.readyState !== WebSocket.OPEN)
        ) {
          secureLog('log', "⚠️ FALLBACK: Connecting to ElevenLabs from media frame (start message may have failed)");
          try {
            if (!callSid || callSid === "unknown") {
              callSid = "CALL_FROM_MEDIA";
            }
            if (!streamSid) {
              streamSid = msg.streamSid || "UNKNOWN_STREAM";
            }
            await connectToElevenLabs();
          } catch (error) {
            secureLog('error', "❌ ElevenLabs connection failed", isDevelopment ? error : undefined);
          }
        }

        // Track frames silently

        // Forward audio to ElevenLabs if connected
        if (elevenLabsWs?.readyState === WebSocket.OPEN) {
          try {
            // CRITICAL FIX: Convert μ-law to PCM and upsample to 16kHz
            const mulawBuffer = Buffer.from(msg.media.payload, "base64");
            const pcm8kBuffer = convertMulawToPCM(mulawBuffer);
            const pcm16kBuffer = upsample8kTo16k(pcm8kBuffer); // Upsample 8kHz → 16kHz
            const pcmBase64 = pcm16kBuffer.toString("base64");

            // 🔍 AUDIO QUALITY CHECK: Verify we have valid audio data
            const audioHasContent = pcm16kBuffer.some(
              (sample) => Math.abs(sample) > 100,
            );
            if (!audioHasContent && frames % 1000 === 0) {
              secureLog('log', `⚠️ POTENTIAL SILENCE - Frame ${frames}: No meaningful audio detected`);
            }

            const audioMessage = {
              user_audio_chunk: pcmBase64, // CORRECT FORMAT: Direct user audio chunk for ElevenLabs
            };

            elevenLabsWs.send(JSON.stringify(audioMessage));

            // Audio forwarded to ElevenLabs

            // Audio forwarded to ElevenLabs
          } catch (audioError) {
            secureLog('error', "❌ Audio conversion error", isDevelopment ? audioError : undefined);
          }
        } else {
          // 🔍 DEBUG: Log when ElevenLabs is not connected - this is CRITICAL!
          if (frames % 500 === 0) {
            secureLog('log', `❌ ELEVENLABS NOT CONNECTED! Frame ${frames}`, {
              webSocketState: elevenLabsWs?.readyState
            });
            secureLog('log', `❌ User audio is being lost - ElevenLabs cannot hear candidate!`);
          }

          // 🎯 REMOVED: Duplicate reconnection logic that was causing multiple connections
          // The main reconnection logic in the close handler is sufficient
        }
      }

      if (msg.event === "stop") {
        if (elevenLabsWs) {
          elevenLabsWs.close();
        }
        ws.close();
      }
    } catch (error) {
      secureLog('error', "❌ WebSocket message error", isDevelopment ? error : undefined);
    }
  });

  function sendFallbackAudio() {
    if (ws.readyState === WebSocket.OPEN && streamSid) {
      const silencePayload = Buffer.alloc(160).toString("base64");
      ws.send(
        JSON.stringify({
          event: "media",
          streamSid: streamSid,
          media: {
            payload: silencePayload,
          },
        }),
      );
    }
  }

  function sendSilenceToTwilio() {
    if (ws.readyState === WebSocket.OPEN && streamSid) {
      // Generate μ-law silence (value 255 = 0x FF for μ-law silence)
      const silenceBuffer = Buffer.alloc(160, 0xff); // 160 bytes of μ-law silence
      const silencePayload = silenceBuffer.toString("base64");

      ws.send(
        JSON.stringify({
          event: "media",
          streamSid: streamSid,
          media: {
            payload: silencePayload,
          },
        }),
      );
      // Silence sent to prevent audio corruption
    }
  }

  function validateAndCleanAudio(audioBase64: string): string | null {
    try {
      // Validate base64 format
      if (!audioBase64 || typeof audioBase64 !== "string") {
        secureLog('log', "🔍 Audio validation: Invalid base64 string");
        return null;
      }

      // Decode and validate buffer
      const audioBuffer = Buffer.from(audioBase64, "base64");

      // Check buffer size (should be reasonable for μ-law chunks)
      if (audioBuffer.length === 0) {
        secureLog('log', "🔍 Audio validation: Empty buffer");
        return null;
      }

      if (audioBuffer.length > 8000) {
        // Max ~1 second at 8kHz
        secureLog('log', "🔍 Audio validation: Buffer too large", {
          bufferLength: audioBuffer.length
        });
        return null;
      }

      // CRITICAL: Filter out the same 0x7F noise pattern from ElevenLabs
      const sampleBytes = audioBuffer.slice(
        0,
        Math.min(16, audioBuffer.length),
      );
      const uniqueValues = new Set(sampleBytes);

      // Check for the problematic 0x7F pattern that causes noise
      if (uniqueValues.size === 1 && uniqueValues.has(0x7f)) {
        secureLog('log', "🚫 Audio validation: Filtered ElevenLabs noise pattern (0x7F)");
        return null; // Filter out this corrupted audio
      }

      // Check for reasonable μ-law distribution (avoid all-zero or single-value patterns)
      if (uniqueValues.size === 1 && audioBuffer.length > 10) {
        const singleValue = Array.from(uniqueValues)[0];
        if (singleValue !== 0xff) {
          // 0xFF is valid μ-law silence
          secureLog('log', "🔍 Audio validation: Suspicious single-value pattern", {
            singleValue
          });
          return null;
        }
      }

      secureLog('log', "✅ Audio validation: Clean μ-law audio", {
        audioLength: audioBuffer.length
      });
      return audioBase64; // Return original if validation passes
    } catch (error) {
      secureLog('error', "❌ Audio validation error", isDevelopment ? error : undefined);
      return null;
    }
  }

  function validateTwilioAudio(audioBase64: string): string | null {
    try {
      // Validate incoming Twilio audio format
      if (!audioBase64 || typeof audioBase64 !== "string") {
        return null;
      }

      const audioBuffer = Buffer.from(audioBase64, "base64");

      // Twilio sends ~20ms chunks (160 bytes for 8kHz μ-law)
      if (audioBuffer.length === 0 || audioBuffer.length > 1000) {
        return null;
      }

      // CRITICAL FIX: Detect and filter out the f39 pattern (0x7F repeating)
      // This pattern indicates corrupted/test audio that causes noise
      if (audioBuffer.length >= 8) {
        const sampleBytes = audioBuffer.slice(0, 8);
        const uniqueValues = new Set(sampleBytes);

        // Check for the problematic 0x7F pattern (shows as f39 in base64)
        if (uniqueValues.size === 1 && uniqueValues.has(0x7f)) {
          secureLog('log', "🚫 STEP 4: Filtered out noise pattern (0x7F repeating)");
          return null; // Filter out this corrupted audio
        }

        // Skip chunks with no audio activity (all silence)
        if (uniqueValues.size === 1 && !uniqueValues.has(0xff)) {
          // Not valid silence, likely corrupted
          return null;
        }
      }

      return audioBase64;
    } catch (error) {
      secureLog('error', "❌ Twilio audio validation error", isDevelopment ? error : undefined);
      return null;
    }
  }

  async function connectToElevenLabs() {
    // 🎯 PREVENT MULTIPLE SIMULTANEOUS CONNECTIONS
    if (isConnecting) {
      secureLog('log', "⚠️ Connection already in progress, skipping duplicate attempt");
      return;
    }
    
    if (elevenLabsWs && elevenLabsWs.readyState === WebSocket.OPEN) {
      secureLog('log', "⚠️ ElevenLabs already connected, skipping duplicate attempt");
      return;
    }
    
    isConnecting = true;
    secureLog('log', "🚀 ATTEMPTING ELEVENLABS CONNECTION");
    secureLog('log', "🔍 DEBUG: Connection attempt started");
    secureLog('log', "🔍 DEBUG: Current call context", {
      callSid: isDevelopment ? callSid : '[SID_REDACTED]',
      candidateName: isDevelopment ? candidateName : '[NAME_REDACTED]',
      jobTitle: isDevelopment ? jobTitle : '[TITLE_REDACTED]',
      companyName: isDevelopment ? companyName : '[COMPANY_REDACTED]',
    });

    if (elevenLabsWs && elevenLabsWs.readyState === WebSocket.OPEN) {
      secureLog('log', "✅ ElevenLabs already connected");
      return;
    }

    const apiKey = process.env.ELEVENLABS_API_KEY;
    const agentId = process.env.ELEVENLABS_AGENT_ID;

    if (!apiKey || !agentId) {
      const errorMsg = `ElevenLabs credentials missing - API Key: ${!!apiKey}, Agent ID: ${!!agentId}`;
      secureLog('error', "❌ CRITICAL ERROR: ElevenLabs credentials missing", {
        hasApiKey: !!apiKey,
        hasAgentId: !!agentId
      });
      throw new Error(errorMsg);
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    try {
      const response = await fetch(
        `https://api.elevenlabs.io/v1/convai/conversation/get_signed_url?agent_id=${agentId}`,
        {
          method: "GET",
          headers: {
            "xi-api-key": apiKey,
          },
          signal: controller.signal,
        },
      );

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        secureLog('error', "❌ ElevenLabs API error", isDevelopment ? {
          status: response.status,
          statusText: response.statusText,
          errorText
        } : { status: response.status });
        throw new Error(
          `ElevenLabs API error: ${response.status} ${response.statusText}`,
        );
      }

      const responseData = await response.json();
      const signed_url = responseData.signed_url;

      elevenLabsWs = new WebSocket(signed_url);

      // Add connection timeout fallback
      let configSent = false;

      const sendConfiguration = () => {
        secureLog('log', "🎯 SEND CONFIG CALLED - Starting configuration process");
        secureLog('log', "🔍 Current state", {
          configSent,
          webSocketState: elevenLabsWs?.readyState,
        });

        if (configSent) {
          secureLog('log', "⚠️ Config already sent, skipping duplicate");
          return;
        }

        if (!elevenLabsWs || elevenLabsWs.readyState !== WebSocket.OPEN) {
          secureLog('error', "❌ Cannot send config - WebSocket not open!", {
            webSocketExists: !!elevenLabsWs,
            webSocketState: elevenLabsWs?.readyState
          });
          return;
        }

        configSent = true;
        secureLog('log', "✅ WebSocket ready, proceeding with configuration send");

        secureLog('log', "🚀 SENDING ELEVENLABS CONFIG - job_title test");
        secureLog('log', "🔍 Current variable values before config creation", {
          jobTitle: isDevelopment ? jobTitle : '[TITLE_REDACTED]',
          candidateName: isDevelopment ? candidateName : '[NAME_REDACTED]',
          companyName: isDevelopment ? companyName : '[COMPANY_REDACTED]',
          jobInfoTitle: isDevelopment ? jobInfo?.title : '[TITLE_REDACTED]'
        });

        try {
          // 🎯 SIMPLIFIED TEST: Only job_title dynamic variable with comprehensive error handling
          console.log("🔍 TRACING DYNAMIC VARIABLE FLOW:");
          console.log("📍 Step 1 - Database values extracted:");
          console.log("  jobInfo:", !!jobInfo, jobInfo?.title || "NONE");
          console.log("  orgInfo:", !!orgInfo, orgInfo?.name || "NONE");
          console.log(
            "  candidateInfo:",
            !!candidateInfo,
            candidateInfo?.fullName || "NONE",
          );

          console.log("📍 Step 2 - Variables prepared for ElevenLabs:");
          console.log("  jobTitle variable:", jobTitle);
          console.log("  candidateName variable:", candidateName);
          console.log("  companyName variable:", companyName);

          // 🚨 CRITICAL FIX: Ensure jobTitle always has a value for ElevenLabs
          if (
            !jobTitle ||
            jobTitle === "the position" ||
            jobTitle === "Available Position"
          ) {
            jobTitle = "Software Engineer"; // Set fallback instead of just logging error
            console.log(
              "🔧 FALLBACK: Setting job_title to default for ElevenLabs:",
              jobTitle,
            );
          }

          // 🚨 COMPLETE DYNAMIC VARIABLES: All variables from ElevenLabs system prompt
          const config = {
            type: "conversation_initiation_client_data",
            dynamic_variables: {
              job_title: jobTitle || "Software Engineer",
              company_name: companyName || "the company",
              company_overview: orgInfo?.companyOverview || "a leading technology company focused on innovation and growth",
              experience_level: jobInfo?.experienceLevel || "mid-level",
              salary_range: jobInfo?.salaryRange
                || "competitive salary range",
              key_responsibilities: jobInfo?.description || "key responsibilities for this role",
              job_requirements: jobInfo?.skillsRequired?.join(", ") || jobInfo?.requirements || "relevant experience and qualifications"
            },
            // 🎯 CRITICAL FIX: Add conversational configuration to prevent interruptions
            conversational_config: {
              // VAD (Voice Activity Detection) settings to prevent interruptions
              voice_activity_detection: {
                threshold: 0.7, // Higher threshold (0-1) means more confidence required to detect speech
                silence_duration_ms: 1500, // Wait 1.5 seconds of silence before agent responds
                end_of_speech_detection: "conservative" // Use conservative detection to avoid interrupting
              },
              // Turnaround timing settings
              turnaround_timing: {
                response_delay_ms: 800, // Wait 800ms after candidate stops speaking before responding
                interruption_threshold: 0.8, // Higher threshold to prevent accidental interruptions
                allow_interruption: false // Disable interruption capability during initial response
              },
              // Audio processing settings
              audio_processing: {
                noise_gate_threshold: -40, // dB threshold to filter background noise
                speech_enhancement: true, // Enhance speech clarity for better detection
                echo_cancellation: true // Prevent audio feedback
              }
            },
          };

          console.log("📍 SENDING TO ELEVENLABS (ALL DYNAMIC VARIABLES + CONVERSATIONAL CONFIG):");
          console.log("  ✅ job_title:", config.dynamic_variables.job_title);
          console.log("  ✅ company_name:", config.dynamic_variables.company_name);
          console.log("  ✅ company_overview:", config.dynamic_variables.company_overview);
          console.log("  ✅ experience_level:", config.dynamic_variables.experience_level);
          console.log("  ✅ salary_range:", config.dynamic_variables.salary_range);
          console.log("  ✅ key_responsibilities:", config.dynamic_variables.key_responsibilities);
          console.log("  ✅ job_requirements:", config.dynamic_variables.job_requirements);
          console.log("  🎯 INTERRUPTION PREVENTION CONFIG:");
          console.log("    VAD threshold:", config.conversational_config.voice_activity_detection.threshold);
          console.log("    Silence duration:", config.conversational_config.voice_activity_detection.silence_duration_ms + "ms");
          console.log("    Response delay:", config.conversational_config.turnaround_timing.response_delay_ms + "ms");
          console.log("    Allow interruption:", config.conversational_config.turnaround_timing.allow_interruption);
          console.log("  ✅ Full config:", JSON.stringify(config, null, 2));

          try {
            elevenLabsWs?.send(JSON.stringify(config));
            console.log(
              "✅ ElevenLabs config sent with dynamic variables + VAD/interruption prevention settings"
            );
          } catch (sendError) {
            console.error("❌ Failed to send config to ElevenLabs:", sendError);
            throw sendError;
          }
        } catch (configError) {
          console.error("❌ Config send error:", configError);
        }
      };

      elevenLabsWs.on("open", () => {
        // 🎯 CONNECTION STATE MANAGEMENT
        isConnecting = false;
        hasConnectedOnce = true;
        
        console.log("🔗 ELEVENLABS WEBSOCKET OPENED!");

        // 🚨 CRITICAL FIX: Ensure job_title is available and send config immediately
        if (!jobTitle) {
          jobTitle = "Software Engineer"; // Immediate fallback
          console.log(
            "🔧 IMMEDIATE FALLBACK: job_title set to default on WebSocket open",
          );
        }

        console.log(
          "🔍 DEBUG: About to send configuration with job_title:",
          jobTitle,
        );

        // 🚨 IMMEDIATE SEND: No delay - send configuration right away
        try {
          sendConfiguration();
          console.log(
            "✅ Configuration sending attempted immediately on WebSocket open",
          );
        } catch (error) {
          console.error(
            "❌ Failed to send configuration on WebSocket open:",
            error,
          );
        }
      });

      // Remove timeout fallback to prevent double-sending

      elevenLabsWs.on("message", (data) => {
        const dataString = data.toString();

        try {
          const response = JSON.parse(data.toString());
          // 🎯 LOG REQUESTED ELEVENLABS EVENTS: conversation_initiation_metadata, agent_response, user_transcript
          if (response.type === "conversation_initiation_metadata") {
            console.log(
              "🎯 ELEVENLABS EVENT: conversation_initiation_metadata",
            );
            // 🔒 SECURITY: Only log metadata in development
            secureLog('log', "📋 CONVERSATION METADATA", isDevelopment ? response : { type: response.type });
          } else if (response.type === "agent_response") {
            console.log("🎯 ELEVENLABS EVENT: agent_response");
            // 🔒 SECURITY: Only log response details in development  
            secureLog('log', "🤖 AGENT RESPONSE", isDevelopment ? response : { type: response.type });
          } else if (response.type === "user_transcript") {
            console.log("🎯 ELEVENLABS EVENT: user_transcript");
            // 🔒 SECURITY: Only log transcript details in development
            secureLog('log', "👤 USER TRANSCRIPT", isDevelopment ? response : { type: response.type });
          }

          if (
            response.type === "audio" &&
            response.audio_event?.audio_base_64
          ) {
            // 🎯 AGENT STILL SPEAKING - CANCEL END TIMER
            cancelEnd();
            
            console.log(
              "🎵 STEP 4: Received audio from ElevenLabs, length:",
              response.audio_event.audio_base_64.length,
            );

            try {
              // For μ-law format, let's be less aggressive with validation
              // and add more detailed logging to understand the format
              const audioBuffer = Buffer.from(
                response.audio_event.audio_base_64,
                "base64",
              );
              console.log(
                "🔍 EL Audio format - Buffer length:",
                audioBuffer.length,
              );

              if (audioBuffer.length > 0) {
                // Check first 8 bytes to understand the format
                const sampleBytes = audioBuffer.slice(
                  0,
                  Math.min(8, audioBuffer.length),
                );
                const hexSample = sampleBytes.toString("hex");
                const byteValues = Array.from(sampleBytes);

                // Only filter out the known problematic 0x7F pattern
                const uniqueValues = new Set(sampleBytes);
                const isNoisePattern =
                  uniqueValues.size === 1 &&
                  uniqueValues.has(0x7f) &&
                  audioBuffer.length > 50;

                if (
                  !isNoisePattern &&
                  ws.readyState === WebSocket.OPEN &&
                  streamSid
                ) {
                  ws.send(
                    JSON.stringify({
                      event: "media",
                      streamSid: streamSid,
                      media: {
                        payload: response.audio_event.audio_base_64,
                      },
                    }),
                  );
                  console.log(
                    "✅ STEP 4: ElevenLabs audio forwarded to Twilio (validated)",
                  );
                } else if (isNoisePattern) {
                  console.log(
                    "🚫 STEP 4: Filtered noise pattern from ElevenLabs",
                  );
                } else {
                  console.error(
                    "❌ STEP 4: Cannot forward audio - Twilio WebSocket not ready",
                  );
                }
              } else {
                console.log("⚠️ STEP 4: Empty audio buffer from ElevenLabs");
              }
            } catch (audioError) {
              console.error("❌ STEP 4: Audio processing error:", audioError);
              // Try to forward anyway in case validation is too strict
              if (ws.readyState === WebSocket.OPEN && streamSid) {
                ws.send(
                  JSON.stringify({
                    event: "media",
                    streamSid: streamSid,
                    media: {
                      payload: response.audio_event.audio_base_64,
                    },
                  }),
                );
                console.log("🔄 STEP 4: Fallback audio forwarded to Twilio");
              }
            }
          }

          if (response.type === "user_transcript") {
            // 🎯 USER SPOKE - CANCEL END TIMER
            cancelEnd();
            
            const transcriptText =
              response.user_transcription_event?.user_transcript ||
              response.user_transcript?.text ||
              response.transcript?.text ||
              "No text";
            // 🔒 SECURITY: CRITICAL - Never log actual conversation content in production
            secureLog('log', `🎤🎤🎤 USER INPUT DETECTED`, {
              transcriptLength: transcriptText?.length || 0,
              transcriptPreview: isDevelopment ? `"${transcriptText}"` : '[CONVERSATION_CONTENT_REDACTED]',
              hasContent: !!transcriptText
            });
            secureLog('log', "🔍 USER TRANSCRIPT EVENT", isDevelopment ? response : {
              type: response.type,
              hasTranscript: !!response.user_transcription_event?.user_transcript
            });

            if (
              transcriptText &&
              transcriptText !== "No text" &&
              transcriptText.trim()
            ) {
              console.log(
                "✅ VALID USER SPEECH DETECTED - Agent should respond within 2-3 seconds...",
              );

              // 💾 SAVE USER TRANSCRIPT AS NOTE
              saveTranscriptAsNote(transcriptText, "user_input");

              // Set a timeout to check if agent responds
              setTimeout(() => {
                console.log(
                  "⏰ 2.5 seconds elapsed since user speech - checking if agent responded...",
                );
              }, 2500);
            } else {
              console.log(
                "⚠️ Empty or invalid transcript - user may not have spoken clearly",
              );
            }
          }

          if (response.type === "agent_response") {
            // FIX: Use correct field path for agent response
            const agentText =
              response.agent_response_event?.agent_response ||
              response.agent_response?.text ||
              "";
            // 🔒 SECURITY: CRITICAL - Never log actual agent responses in production
            secureLog('log', `🤖 AGENT RESPONDED`, {
              responseLength: agentText?.length || 0,
              responsePreview: isDevelopment ? `"${agentText}"` : '[AGENT_RESPONSE_REDACTED]',
              hasContent: !!agentText
            });
            secureLog('log', "🔍 AGENT RESPONSE EVENT", isDevelopment ? response : {
              type: response.type,
              hasResponse: !!response.agent_response_event?.agent_response
            });

            // 💾 SAVE AGENT RESPONSE AS NOTE
            if (agentText && agentText.trim()) {
              saveTranscriptAsNote(agentText, "agent_response");
            }

            // 🎯 CHECK FOR CLOSING PHRASES
            const txt = agentText.toLowerCase();
            if (/\b(have a wonderful day|goodbye|bye|take care|talk to you later)\b/.test(txt)) {
              console.log("💬 Detected closing phrase → schedule hangup");
              scheduleEnd(); // gives user a beat to add anything
            }

            console.log(
              "🎯 Agent finished speaking - now listening for user response...",
            );
          }

          if (response.type === "conversation_initiation_metadata") {
            console.log(
              "🔧 STEP 4: ElevenLabs conversation initialized successfully!",
            );
            // 🔒 SECURITY: Only log metadata in development
            secureLog('log', "🔧 STEP 4: Conversation metadata", isDevelopment ? response : {
              type: response.type
            });
            console.log(
              "🎯 STEP 4: Agent should now speak the first_message automatically",
            );
          }

          if (response.type === "agent_response_finished") {
            // 🎯 AGENT FINISHED SPEAKING - START IDLE END TIMER
            console.log("✅ agent_response_finished → starting idle end timer");
            scheduleEnd();
            
            console.log(
              "🎤 STEP 4: Agent finished speaking - KEEP LISTENING for user",
            );
            // DO NOT CLOSE - Keep conversation active!
            // The agent just finished one response, but conversation continues
          }

          if (response.type === "conversation_end") {
            console.log("🎬 EL says conversation_end → hanging up");
            hangupNow(callSid);
            return;
          }

          if (response.type === "ping" || response.type === "ping_event") {
            // Respond to ping to keep connection alive
            if (elevenLabsWs?.readyState === WebSocket.OPEN) {
              const pongResponse = {
                type: "pong",
                event_id: response.ping_event?.event_id || response.event_id,
              };
              elevenLabsWs.send(JSON.stringify(pongResponse));
              console.log(
                "🏓 STEP 4: Responded to ElevenLabs ping with pong (event_id:",
                response.ping_event?.event_id || response.event_id,
                ")",
              );
            }
          }

          if (response.type === "error") {
            console.error(
              "❌ STEP 4: ElevenLabs reported error:",
              JSON.stringify(response, null, 2),
            );
          }
        } catch (parseError) {
          secureLog('error', "❌ EL message parse error (non-fatal)", isDevelopment ? parseError : undefined);
        }
      });

      elevenLabsWs.on("error", (error) => {
        secureLog('error', "❌ STEP 4: ElevenLabs WebSocket error", isDevelopment ? error : undefined);
        secureLog('error', "❌ STEP 4: Error details", isDevelopment ? {
          message: error.message,
          code: (error as any).code,
          type: (error as any).type,
          stack: error.stack,
        } : { hasError: true });
      });

      elevenLabsWs.on("close", (code, reason) => {
        console.log("📞 STEP 4: ElevenLabs connection closed");
        console.log(
          "📞 STEP 4: Close code:",
          code,
          "Reason:",
          reason?.toString(),
        );

        // 🎯 NATURAL CALL ENDING: If ElevenLabs closes normally (code 1000), end the Twilio call
        if (code === 1000) {
          console.log("🎬 ElevenLabs closed normally - ending Twilio call automatically");
          // Cancel any pending end timer since we're ending immediately
          cancelEnd();
          // Terminate the Twilio call immediately
          setTimeout(() => hangupNow(callSid), 500); // Brief delay to allow final audio
          return;
        }

        // 🎯 SINGLE RECONNECTION: Only reconnect once if needed and call is still active  
        if (ws.readyState === WebSocket.OPEN && code !== 1000 && hasConnectedOnce && !hasReconnected) {
          console.log("🔄 STEP 4: Attempting single reconnection to ElevenLabs...");
          hasReconnected = true; // Prevent multiple reconnection attempts
          
          setTimeout(async () => {
            try {
              await connectToElevenLabs();
              console.log("✅ STEP 4: Successfully reconnected to ElevenLabs");
            } catch (reconnectError) {
              console.error("❌ STEP 4: Reconnection failed:", reconnectError);
            }
          }, 2000);
        }
      });
    } catch (fetchError) {
      clearTimeout(timeoutId);
      console.error("❌ STEP 4: ElevenLabs connection failed:", fetchError);
      if (fetchError instanceof Error) {
        console.error("❌ STEP 4: Error details:", {
          message: fetchError.message,
          stack: fetchError.stack,
          name: fetchError.name,
        });
      }
      throw fetchError;
    }
  }

  ws.on("close", () => {
    clearInterval(keepAlive);
    const duration = Date.now() - connectionStartTime;
    console.log("📞 STEP 4: Twilio WebSocket CLOSED for call:", callSid);
    console.log(
      "📊 Final stats - Frames:",
      frames,
      "Duration:",
      duration + "ms",
    );

    if (elevenLabsWs) {
      elevenLabsWs.close();
    }
  });

  ws.on("error", (error) => {
    console.error("❌ STEP 4: Twilio WebSocket ERROR:", error);
  });

  // Send immediate acknowledgment
  try {
    ws.send(
      JSON.stringify({
        event: "connected",
        message: "ElevenLabs WebSocket ready for streaming",
      }),
    );
    console.log("✅ STEP 4: Sent initial acknowledgment");
  } catch (ackError) {
    console.error("❌ Failed to send acknowledgment:", ackError);
  }

  console.log("✅ STEP 4: WebSocket handler initialized and ready");
}
