# ElevenLabs Conversational AI Troubleshooting Guide

## Current Issue
Calls are being initiated but not connecting to ElevenLabs Conversational AI WebSocket.

## Diagnostic Steps

### 1. Check ElevenLabs Dashboard
- Verify your agent is published and active
- Confirm the agent has a phone number assigned
- Check if WebSocket connections are enabled for your account

### 2. WebSocket URL Options
Try these different WebSocket endpoints:

**Option A: Standard Conversation**
```
wss://api.elevenlabs.io/v1/convai/conversation
```

**Option B: Phone-Specific Endpoint**
```
wss://api.elevenlabs.io/v1/convai/conversation/phone
```

**Option C: Agent-Specific Connection**
```
wss://api.elevenlabs.io/v1/convai/agents/{agent_id}/conversation
```

### 3. Required Parameters
Ensure these parameters are correctly set:

```xml
<Parameter name="agent_id" value="YOUR_AGENT_ID" />
<Parameter name="xi-api-key" value="YOUR_API_KEY" />
```

### 4. Account Requirements
ElevenLabs Conversational AI may require:
- Professional/Enterprise subscription
- Phone integration enabled
- Specific geographic region settings

### 5. Alternative Integration Methods

**Method 1: Direct Agent Call**
Some accounts may need to initiate conversations differently:
```javascript
// Instead of WebSocket, use ElevenLabs phone call API
const response = await axios.post('https://api.elevenlabs.io/v1/convai/phone/call', {
  agent_id: process.env.ELEVENLABS_AGENT_ID,
  phone_number: candidate.phoneNumber,
  context: conversationMetadata
});
```

**Method 2: Webhook-First Approach**
Register webhooks first, then establish connection:
```javascript
// Pre-register the conversation
const conversation = await axios.post('https://api.elevenlabs.io/v1/convai/conversations', {
  agent_id: process.env.ELEVENLABS_AGENT_ID,
  metadata: conversationMetadata
});
```

## Next Steps to Try

1. **Check Account Limits**: Verify your ElevenLabs account has phone/WebSocket access
2. **Test Different Endpoints**: Try the phone-specific WebSocket URL
3. **Direct API Integration**: Use ElevenLabs phone call API instead of WebSocket
4. **Contact ElevenLabs Support**: They may need to enable specific features for your account

## Fallback Options

If WebSocket integration continues to fail:
1. Use ElevenLabs voice synthesis with Twilio speech recognition
2. Implement conversation flow with multiple TTS calls
3. Use alternative conversational AI platforms (OpenAI + Twilio)