import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Phone, Mail, Building2, User } from 'lucide-react';

export default function BookDemoPage() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    organization: '',
    phone: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <CardTitle className="text-2xl text-gray-900">Demo Request Submitted!</CardTitle>
            <CardDescription className="text-gray-600">
              Thank you for your interest in Steorra. Our team will contact you within 24 hours to schedule your personalized demo.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate('/')} className="w-full">
              Return to Home
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50">
      {/* Navbar */}
      <div className="w-full bg-white/95 backdrop-blur-sm shadow-sm border-b border-gray-100 px-8 py-6 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-3xl font-bold text-gray-900 tracking-tight">steorra</span>
        </div>
        <div className="flex items-center space-x-10 ml-8">
          <button className="text-gray-600 text-base font-medium hover:text-gray-900 transition">Product</button>
          <button className="text-gray-600 text-base font-medium hover:text-gray-900 transition">Pricing</button>
          <button className="text-gray-600 text-base font-medium hover:text-gray-900 transition">Resources</button>
        </div>
        <div className="flex items-center space-x-6">
          <button 
            onClick={() => navigate('/auth')}
            className="text-gray-600 text-base font-medium hover:text-gray-900 transition"
          >
            Login
          </button>
          <Button 
            onClick={() => navigate('/')}
            variant="outline"
            size="lg"
            className="text-gray-600 border-gray-300 hover:bg-gray-50 px-6 py-3"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back
          </Button>
        </div>
      </div>

      <div className="container mx-auto px-8 py-16 text-justify pt-[0px] pb-[0px] pl-[0px] pr-[0px] ml-[109px] mr-[109px] mt-[0px] mb-[0px]">
        <div className="max-w-5xl mx-auto">
          {/* Header */}
          <header className="text-center mb-16">
            
          </header>

          {/* Centered Form */}
          <div className="flex justify-center">
            {/* Demo Form */}
            <section aria-labelledby="demo-form-title" className="w-full max-w-2xl">
              <Card className="shadow-2xl bg-white/90 backdrop-blur-sm border-0">
                <CardHeader className="pb-8 pt-10 px-10">
                  <CardTitle id="demo-form-title" className="text-3xl font-bold text-gray-900 text-center mb-4">Request Your Free Demo</CardTitle>
                  
                </CardHeader>
              <CardContent className="px-10 pb-10">
                <form onSubmit={handleSubmit} className="space-y-8 mt-[0px] mb-[0px] pt-[3px] pb-[3px] ml-[63px] mr-[63px]" role="form" aria-label="Demo request form">
                  <div>
                    <Label htmlFor="name" className="text-lg font-semibold text-gray-800 flex items-center mb-3">
                      <User className="w-5 h-5 mr-3 text-blue-600" aria-hidden="true" />
                      Full Name *
                    </Label>
                    <Input
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="mt-2 h-14 text-lg px-4 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                      placeholder="Enter your full name"
                      aria-describedby="name-help"
                      autoComplete="name"
                    />
                    <span id="name-help" className="sr-only">Enter your full name for the demo request</span>
                  </div>

                  <div>
                    <Label htmlFor="email" className="text-lg font-semibold text-gray-800 flex items-center mb-3">
                      <Mail className="w-5 h-5 mr-3 text-green-600" aria-hidden="true" />
                      Work Email Address *
                    </Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      required
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="mt-2 h-14 text-lg px-4 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                      placeholder="Enter your work email address"
                      aria-describedby="email-help"
                      autoComplete="email"
                    />
                    <span id="email-help" className="sr-only">Enter your business email address to receive demo scheduling information</span>
                  </div>

                  <div>
                    <Label htmlFor="organization" className="text-lg font-semibold text-gray-800 flex items-center mb-3">
                      <Building2 className="w-5 h-5 mr-3 text-purple-600" aria-hidden="true" />
                      Organization Name *
                    </Label>
                    <Input
                      id="organization"
                      name="organization"
                      required
                      value={formData.organization}
                      onChange={(e) => handleInputChange('organization', e.target.value)}
                      className="mt-2 h-14 text-lg px-4 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                      placeholder="Enter your company or organization name"
                      aria-describedby="organization-help"
                      autoComplete="organization"
                    />
                    <span id="organization-help" className="sr-only">Enter your company or organization name for demo customization</span>
                  </div>

                  <div>
                    <Label htmlFor="phone" className="text-lg font-semibold text-gray-800 flex items-center mb-3">
                      <Phone className="w-5 h-5 mr-3 text-orange-600" aria-hidden="true" />
                      Phone Number *
                    </Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      required
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="mt-2 h-14 text-lg px-4 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                      placeholder="Enter your phone number with country code"
                      aria-describedby="phone-help"
                      autoComplete="tel"
                    />
                    <span id="phone-help" className="sr-only">Enter your phone number for demo scheduling confirmation</span>
                  </div>

                  {/* Submit Button */}
                  <div className="pt-8">
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full h-16 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white text-xl font-bold rounded-xl shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300 pt-[11px] pb-[11px] pl-[17px] pr-[17px] mt-[-27px] mb-[-27px]"
                      aria-describedby="submit-help"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin w-6 h-6 border-3 border-white border-t-transparent rounded-full mr-4" aria-hidden="true" />
                          Submitting Demo Request...
                        </>
                      ) : (
                        'Book My Free Demo Now'
                      )}
                    </Button>
                    <p id="submit-help" className="text-base text-gray-600 text-center leading-relaxed pt-[3px] pb-[3px] mt-[26px] mb-[26px]">
                      Our AI recruitment specialists will contact you within 24 hours to schedule your free, personalized Steorra demo
                    </p>
                  </div>
                </form>
              </CardContent>
            </Card>
            </section>
          </div>

          
        </div>
      </div>
    </div>
  );
}