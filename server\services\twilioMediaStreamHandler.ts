import WebSocket from 'ws';
import { googleSpeechService, TranscriptionResult } from './googleSpeechService';
import { db } from '../db';
import { voiceCalls } from '../../shared/schema';
import { eq } from 'drizzle-orm';
// VoiceCallManager will be injected to avoid circular dependencies

export interface MediaStreamMessage {
  event: 'connected' | 'start' | 'media' | 'stop' | 'closed';
  streamSid?: string;
  accountSid?: string;
  callSid?: string;
  media?: {
    track: 'inbound' | 'outbound';
    chunk: string;
    timestamp: string;
    payload: string;
  };
}

export class TwilioMediaStreamHandler {
  private voiceCallManager: any;
  private activeStreams: Map<string, {
    ws: WebSocket;
    callSid: string;
    fullTranscript: string;
    interimTranscript: string;
  }> = new Map();

  constructor(voiceCallManager: any) {
    this.voiceCallManager = voiceCallManager;
  }

  /**
   * Handle new WebSocket connection for media streaming
   */
  handleConnection(ws: WebSocket): void {
    console.log('New media stream WebSocket connection');

    ws.on('message', (message: string) => {
      try {
        const data: MediaStreamMessage = JSON.parse(message);
        this.handleStreamMessage(ws, data);
      } catch (error) {
        console.error('Error parsing media stream message:', error);
      }
    });

    ws.on('close', () => {
      console.log('Media stream WebSocket connection closed');
      this.cleanup(ws);
    });

    ws.on('error', (error) => {
      console.error('Media stream WebSocket error:', error);
      this.cleanup(ws);
    });
  }

  /**
   * Handle different types of stream messages
   */
  private handleStreamMessage(ws: WebSocket, data: MediaStreamMessage): void {
    switch (data.event) {
      case 'connected':
        console.log('Media stream connected');
        break;

      case 'start':
        this.handleStreamStart(ws, data);
        break;

      case 'media':
        this.handleMediaData(ws, data);
        break;

      case 'stop':
        this.handleStreamStop(ws, data);
        break;

      case 'closed':
        this.handleStreamClosed(ws, data);
        break;

      default:
        console.log('Unknown media stream event:', data.event);
    }
  }

  /**
   * Handle stream start event
   */
  private handleStreamStart(ws: WebSocket, data: MediaStreamMessage): void {
    if (!data.streamSid || !data.callSid) {
      console.error('Missing streamSid or callSid in start event');
      return;
    }

    console.log(`Starting media stream for call ${data.callSid}`);

    // Store stream info
    this.activeStreams.set(data.streamSid, {
      ws,
      callSid: data.callSid,
      fullTranscript: '',
      interimTranscript: ''
    });

    // Start Google Speech recognition for this stream
    googleSpeechService.startRecognition(
      (result: TranscriptionResult) => this.handleTranscription(data.streamSid!, result),
      (error: Error) => this.handleTranscriptionError(data.streamSid!, error)
    );
  }

  /**
   * Handle incoming audio data
   */
  private handleMediaData(ws: WebSocket, data: MediaStreamMessage): void {
    if (!data.media || !data.media.payload) {
      return;
    }

    try {
      // Convert base64 audio payload to buffer
      const audioBuffer = Buffer.from(data.media.payload, 'base64');
      
      // Send audio to Google Speech for real-time transcription
      googleSpeechService.sendAudio(audioBuffer);
    } catch (error) {
      console.error('Error processing media data:', error);
    }
  }

  /**
   * Handle transcription results from Google Speech
   */
  private async handleTranscription(streamSid: string, result: TranscriptionResult): Promise<void> {
    const streamInfo = this.activeStreams.get(streamSid);
    if (!streamInfo) {
      return;
    }

    console.log(`Transcription (${result.isFinal ? 'FINAL' : 'INTERIM'}): ${result.transcript}`);

    if (result.isFinal) {
      // Final transcript - process with AI and respond
      streamInfo.fullTranscript += result.transcript + ' ';
      streamInfo.interimTranscript = '';

      // Get call ID from Twilio SID first
      const callId = await this.getCallIdFromSid(streamInfo.callSid);
      if (callId) {
        // Process final transcript with voice call manager using correct call ID
        await this.voiceCallManager.processTranscription(
          callId,
          result.transcript.trim(),
          'google_speech'
        );
      } else {
        console.error(`Could not find call ID for SID: ${streamInfo.callSid}`);
      }
    } else {
      // Interim transcript - just store for now
      streamInfo.interimTranscript = result.transcript;
    }
  }

  /**
   * Get call ID from Twilio SID
   */
  private async getCallIdFromSid(twilioCallSid: string): Promise<string | null> {
    try {
      const call = await db.select()
        .from(voiceCalls)
        .where(eq(voiceCalls.twilioCallSid, twilioCallSid))
        .limit(1);
      
      return call.length > 0 ? call[0].id : null;
    } catch (error) {
      console.error('Error getting call ID from SID:', error);
      return null;
    }
  }

  /**
   * Handle transcription errors
   */
  private handleTranscriptionError(streamSid: string, error: Error): void {
    console.error(`Transcription error for stream ${streamSid}:`, error);
    
    // Fallback to Twilio speech recognition if Google Speech fails
    const streamInfo = this.activeStreams.get(streamSid);
    if (streamInfo) {
      console.log('Falling back to Twilio speech recognition...');
      // The voice call will continue with Twilio's built-in speech recognition
    }
  }

  /**
   * Handle stream stop event
   */
  private async handleStreamStop(ws: WebSocket, data: MediaStreamMessage): Promise<void> {
    if (data.streamSid) {
      console.log(`Media stream stopped: ${data.streamSid}`);
      
      const streamInfo = this.activeStreams.get(data.streamSid);
      if (streamInfo) {
        // Get call ID from Twilio SID first
        const callId = await this.getCallIdFromSid(streamInfo.callSid);
        if (callId) {
          // Save final transcript to voice call notes
          await this.voiceCallManager.addCallNote(
            callId,
            `Final transcript: ${streamInfo.fullTranscript}`,
            'google_speech_final'
          );
        }
      }

      this.cleanup(ws, data.streamSid);
    }
  }

  /**
   * Handle stream closed event
   */
  private handleStreamClosed(ws: WebSocket, data: MediaStreamMessage): void {
    console.log('Media stream closed');
    this.cleanup(ws, data.streamSid);
  }

  /**
   * Clean up resources for a stream or WebSocket
   */
  private cleanup(ws: WebSocket, streamSid?: string): void {
    if (streamSid) {
      this.activeStreams.delete(streamSid);
    } else {
      // Find and remove stream by WebSocket  
      const streamIds = Array.from(this.activeStreams.keys());
      for (const sid of streamIds) {
        const info = this.activeStreams.get(sid);
        if (info && info.ws === ws) {
          this.activeStreams.delete(sid);
          break;
        }
      }
    }

    // Stop Google Speech recognition
    googleSpeechService.stopRecognition();
  }

  /**
   * Get active stream count
   */
  getActiveStreamCount(): number {
    return this.activeStreams.size;
  }

  /**
   * Get stream info for debugging
   */
  getStreamInfo(streamSid: string) {
    return this.activeStreams.get(streamSid);
  }
}