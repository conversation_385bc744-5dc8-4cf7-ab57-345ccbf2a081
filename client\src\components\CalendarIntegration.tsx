import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  Clock, 
  User, 
  Mail,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Edit,
  Trash2,
  Plus
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Interview {
  id: string;
  candidateId: string;
  scheduledAt: string;
  status: string;
  calendarEventId?: string;
  notes?: string;
  candidate: {
    fullName: string;
    email: string;
    phone?: string;
  };
}

interface CalendarEvent {
  id: string;
  summary: string;
  start: {
    dateTime: string;
  };
  end: {
    dateTime: string;
  };
  attendees?: Array<{
    email: string;
    displayName?: string;
  }>;
}

export default function CalendarIntegration() {
  const [interviews, setInterviews] = useState<Interview[]>([]);
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [calendarStatus, setCalendarStatus] = useState({ authenticated: false, message: '' });
  const { toast } = useToast();

  useEffect(() => {
    fetchCalendarStatus();
    fetchUpcomingInterviews();
  }, []);

  const fetchCalendarStatus = async () => {
    try {
      const response = await fetch('/api/calendar/status');
      if (response.ok) {
        const status = await response.json();
        setCalendarStatus(status);
      }
    } catch (error) {
      console.error('Failed to fetch calendar status:', error);
    }
  };

  const fetchUpcomingInterviews = async () => {
    try {
      const response = await fetch('/api/calendar/upcoming');
      if (response.ok) {
        const data = await response.json();
        setInterviews(data.interviews || []);
        setCalendarEvents(data.calendarEvents || []);
      }
    } catch (error) {
      console.error('Failed to fetch upcoming interviews:', error);
      toast({
        title: "Error",
        description: "Failed to load interviews",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const cancelInterview = async (interviewId: string, candidateName: string) => {
    if (!window.confirm(`Are you sure you want to cancel the interview with ${candidateName}?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/calendar/interview/${interviewId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Interview cancelled successfully",
        });
        await fetchUpcomingInterviews();
      } else {
        throw new Error('Failed to cancel interview');
      }
    } catch (error) {
      console.error('Failed to cancel interview:', error);
      toast({
        title: "Error",
        description: "Failed to cancel interview",
        variant: "destructive",
      });
    }
  };

  const formatDateTime = (dateTimeStr: string) => {
    const date = new Date(dateTimeStr);
    return {
      date: date.toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return (
          <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100">
            <Calendar className="w-3 h-3 mr-1" />
            Scheduled
          </Badge>
        );
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
            <CheckCircle className="w-3 h-3 mr-1" />
            Completed
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100">
            <AlertCircle className="w-3 h-3 mr-1" />
            Cancelled
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <Clock className="w-3 h-3 mr-1" />
            {status}
          </Badge>
        );
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading calendar data...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen gradient-bg-primary">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-3xl font-bold text-white mb-2 drop-shadow-lg">
              Calendar Integration
            </h1>
            <p className="text-white/90 drop-shadow">
              Manage scheduled interviews and calendar events
            </p>
          </div>

          {/* Calendar Status */}
          <Card className="enhanced-card glass-effect">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="gradient-text">Calendar Status</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-300 mt-1">
                    {calendarStatus.message}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  {calendarStatus.authenticated ? (
                    <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Connected
                    </Badge>
                  ) : (
                    <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100">
                      <AlertCircle className="w-3 h-3 mr-1" />
                      Not Connected
                    </Badge>
                  )}
                  <Button
                    onClick={fetchCalendarStatus}
                    variant="outline"
                    size="sm"
                  >
                    <RefreshCw className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Upcoming Interviews */}
          <Card className="enhanced-card glass-effect">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="gradient-text">Upcoming Interviews</CardTitle>
                <Button
                  onClick={fetchUpcomingInterviews}
                  variant="outline"
                  size="sm"
                  className="glass-effect text-gray-700 dark:text-gray-300"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {interviews.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    No upcoming interviews
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Schedule interviews from the Interview Scheduling page
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {interviews.map((interview) => {
                    const datetime = formatDateTime(interview.scheduledAt);
                    return (
                      <div
                        key={interview.id}
                        className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <User className="w-5 h-5 text-gray-500" />
                              <h4 className="font-semibold text-gray-900 dark:text-white">
                                {interview.candidate.fullName}
                              </h4>
                              {getStatusBadge(interview.status)}
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                              <div className="space-y-2">
                                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                                  <Calendar className="w-4 h-4" />
                                  <span>{datetime.date}</span>
                                </div>
                                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                                  <Clock className="w-4 h-4" />
                                  <span>{datetime.time}</span>
                                </div>
                              </div>
                              
                              <div className="space-y-2">
                                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                                  <Mail className="w-4 h-4" />
                                  <span>{interview.candidate.email}</span>
                                </div>
                                {interview.candidate.phone && (
                                  <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                                    <span className="text-xs">📞</span>
                                    <span>{interview.candidate.phone}</span>
                                  </div>
                                )}
                              </div>
                            </div>

                            {interview.notes && (
                              <div className="mt-3 p-2 bg-gray-50 dark:bg-gray-700 rounded text-sm">
                                <span className="font-medium">Notes: </span>
                                {interview.notes}
                              </div>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-2 ml-4">
                            {interview.status === 'scheduled' && (
                              <Button
                                onClick={() => cancelInterview(interview.id, interview.candidate.fullName)}
                                variant="outline"
                                size="sm"
                                className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Calendar Events Summary */}
          {calendarEvents.length > 0 && (
            <Card className="enhanced-card glass-effect">
              <CardHeader>
                <CardTitle className="gradient-text">Recent Calendar Events</CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-300">
                  Events from your Google Calendar
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {calendarEvents.slice(0, 5).map((event) => {
                    const datetime = formatDateTime(event.start.dateTime);
                    return (
                      <div
                        key={event.id}
                        className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-700"
                      >
                        <div>
                          <h5 className="font-medium text-gray-900 dark:text-white">
                            {event.summary}
                          </h5>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {datetime.date} at {datetime.time}
                          </p>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          Calendar
                        </Badge>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}