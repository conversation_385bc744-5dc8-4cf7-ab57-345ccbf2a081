-- Create interview automation tables
-- This script creates the missing tables for the interview automation system

-- <PERSON><PERSON> enums first (ignore errors if they already exist)
CREATE TYPE interview_status AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled', 'rescheduled');
CREATE TYPE sdk_type AS ENUM ('video', 'meeting');
CREATE TYPE interview_run_status AS ENUM ('pending', 'starting', 'in_progress', 'completed', 'failed', 'cancelled');
CREATE TYPE artifact_type AS ENUM ('recording', 'transcript', 'summary', 'analysis');

-- Create agent_profiles table
CREATE TABLE IF NOT EXISTS agent_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name TEXT NOT NULL,
    script_version TEXT DEFAULT 'v1.0',
    rubric_json JSONB,
    safety_json JSONB,
    prompt_template TEXT,
    voice_settings JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> interviews_v2 table
CREATE TABLE IF NOT EXISTS interviews_v2 (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    candidate_id UUID NOT NULL REFERENCES candidates(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    role TEXT NOT NULL,
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_min INTEGER DEFAULT 60,
    status interview_status DEFAULT 'scheduled',
    sdk_type sdk_type DEFAULT 'video',
    room_or_meeting_id TEXT,
    join_urls JSONB,
    host_token_scope TEXT,
    agent_profile_id UUID REFERENCES agent_profiles(id),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create interview_runs table
CREATE TABLE IF NOT EXISTS interview_runs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interview_id UUID NOT NULL REFERENCES interviews_v2(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    started_at TIMESTAMP WITH TIME ZONE,
    ended_at TIMESTAMP WITH TIME ZONE,
    status interview_run_status DEFAULT 'pending',
    metrics_json JSONB,
    bot_session_id TEXT,
    zoom_session_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create interview_artifacts table
CREATE TABLE IF NOT EXISTS interview_artifacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interview_id UUID NOT NULL REFERENCES interviews_v2(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    type artifact_type NOT NULL,
    uri TEXT NOT NULL,
    meta_json JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_agent_profiles_org_id ON agent_profiles(organization_id);
CREATE INDEX IF NOT EXISTS idx_agent_profiles_active ON agent_profiles(is_active);

CREATE INDEX IF NOT EXISTS idx_interviews_v2_org_id ON interviews_v2(organization_id);
CREATE INDEX IF NOT EXISTS idx_interviews_v2_candidate_id ON interviews_v2(candidate_id);
CREATE INDEX IF NOT EXISTS idx_interviews_v2_status ON interviews_v2(status);
CREATE INDEX IF NOT EXISTS idx_interviews_v2_scheduled_at ON interviews_v2(scheduled_at);

CREATE INDEX IF NOT EXISTS idx_interview_runs_interview_id ON interview_runs(interview_id);
CREATE INDEX IF NOT EXISTS idx_interview_runs_org_id ON interview_runs(organization_id);
CREATE INDEX IF NOT EXISTS idx_interview_runs_status ON interview_runs(status);

CREATE INDEX IF NOT EXISTS idx_interview_artifacts_interview_id ON interview_artifacts(interview_id);
CREATE INDEX IF NOT EXISTS idx_interview_artifacts_org_id ON interview_artifacts(organization_id);
CREATE INDEX IF NOT EXISTS idx_interview_artifacts_type ON interview_artifacts(type);

-- Insert default agent profile for each organization
INSERT INTO agent_profiles (organization_id, name, script_version, prompt_template, voice_settings, is_active)
SELECT
    id as organization_id,
    'Default Interview Agent' as name,
    'v1.0' as script_version,
    'You are a professional AI interviewer. Conduct a structured interview by asking relevant questions about the candidate''s experience, skills, and qualifications. Be friendly but professional, and adapt your questions based on the role and candidate responses.' as prompt_template,
    '{"voiceId": "21m00Tcm4TlvDq8ikWAM", "stability": 0.6, "similarityBoost": 0.8, "style": 0.3, "useSpeakerBoost": true}' as voice_settings,
    true as is_active
FROM organizations
WHERE NOT EXISTS (
    SELECT 1 FROM agent_profiles WHERE agent_profiles.organization_id = organizations.id
);
