📁 1. Resume Screening (Module: resume-screening)
Frontend Features:

Upload support for .pdf, .docx, and .txt formats via drag-and-drop

Manual or dropdown selection of Job ID (fetched from backend)

Colorful tile view: one tile per candidate showing:

Name, Location, Match Score

Buttons: ✅ Approve, ❌ Reject

Optional: Preview parsed resume data in modal

Backend Tasks:

/api/resume/upload

Accept resume + Job ID

Parse resume using Python libs (e.g. pdfplumber, python-docx)

Extract summary using OpenAI GPT-4 (resume_summary_prompt)

/api/resume/compare

Match resume against job description (from job table)

Use resume_vs_jd_prompt to calculate score and extract skill gaps

Store structured output in candidates table (linked to job_id)

Avoid duplicate OpenAI calls by caching resume hash summaries

DB Tables:

sql
Copy
Edit
Table: jobs
- job_id (PK)
- title
- department
- job_description (TEXT)
- created_at

Table: candidates
- candidate_id (PK)
- job_id (FK)
- name
- email
- phone
- location
- resume_text (TEXT)
- resume_summary (JSONB)
- match_score (INT)
- matched_skills (TEXT[])
- missing_skills (TEXT[])
- status (ENUM: Pending, Approved, Rejected)
- created_at
📋 2. Candidate Tracker (Module: tracker)
Frontend Features:

Table/grid view of all candidates

Filters: Job ID, Status

Columns: Name, Location, Score, Status

Action buttons: ✅ Approve, ❌ Delete (rejected only)

Optional: View detailed summary popup or side panel

Backend Tasks:

/api/candidates?job_id=&status=

Return paginated list of candidate summaries

/api/candidate/update_status

Accept: { candidate_id, status }

Update candidate state

📅 3. Scheduling (Module: interview-scheduler)
Frontend Features:

Display only candidates with status = Approved

“Send Interview Email” button opens editable email template

Show response from candidate once available

Display manager availability from Google Calendar API

Confirm slot → auto-create Google Meet event

Backend Tasks:

/api/interview/send_invite

Trigger email via Sendgrid/Mailgun with custom message

Include response link (webhook or magic link)

/api/interview/capture_response

Capture time preferences from candidate

/api/interview/schedule_meeting

Use Google Calendar API to book meeting

External Integration:

Google Calendar API (OAuth scopes: calendar.readonly, calendar.events.write)

Optional: Integrate Calendly

✅ 4. Onboarding (Module: onboarding)
Frontend Features:

Move candidates with “Hired” status to onboarding

Display profile data fetched from Workday API

Upload signed offer letter and onboarding docs

Backend Tasks:

/api/onboarding/initiate

Push new hire data to Workday (or simulate via mock API)

Fetch onboarding checklist

/api/onboarding/status/:candidate_id

Return real-time status from Workday

Workday Integration:

Requires secure API keys (token-based or OAuth)

Simulated mock mode for demo with sample data

🧠 Memory & Token Optimization
Approach:

Use tiktoken (Python) to measure prompt size

Cache OpenAI inputs/outputs (use SHA256 hash of resume text)

Store all prompts and summaries in resume_summary column (JSONB)

Only call OpenAI if not already cached or if job description changes

🗃️ Memory Store & Vectors (Optional but recommended)
Use case: Enable resume semantic search or duplicate detection

Vector DB: Supabase vector store or Pinecone

Store embeddings of resume and JD using text-embedding-3-small

Use cosine similarity to surface closest candidates