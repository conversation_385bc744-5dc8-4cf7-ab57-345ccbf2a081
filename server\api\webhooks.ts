import { Router } from 'express';
import { db } from '../db';
import { candidateAvailability, candidates } from '@shared/schema';
import { eq } from 'drizzle-orm';

const router = Router();

/**
 * @swagger
 * /webhooks/email-response:
 *   post:
 *     summary: Process email responses
 *     description: Webhook endpoint to process candidate email responses for availability
 *     tags: [Webhooks]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               from:
 *                 type: string
 *                 format: email
 *               subject:
 *                 type: string
 *               text:
 *                 type: string
 *               html:
 *                 type: string
 *     responses:
 *       200:
 *         description: Email processed successfully
 *       400:
 *         description: Invalid email format or missing candidate ID
 *       404:
 *         description: Candidate not found
 *       500:
 *         description: Processing failed
 */
router.post('/email-response', async (req, res) => {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('Received email webhook from:', req.body.from);
    }
    
    // Parse the incoming email data
    const { from, subject, text, html } = req.body;
    
    // Extract candidate ID from email content
    const candidateIdMatch = text?.match(/Candidate ID:\s*([a-f0-9-]+)/i) || 
                            html?.match(/Candidate ID:\s*([a-f0-9-]+)/i);
    
    if (!candidateIdMatch) {
      console.log('No candidate ID found in email');
      return res.status(400).json({ error: 'Candidate ID not found' });
    }
    
    const candidateId = candidateIdMatch[1];
    
    // Verify candidate exists
    const candidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);
      
    if (candidate.length === 0) {
      console.log('Candidate not found:', candidateId);
      return res.status(404).json({ error: 'Candidate not found' });
    }
    
    // Parse availability information from email
    const dayMatch = text?.match(/Preferred Day:\s*([A-Za-z]+)/i) || 
                    html?.match(/Preferred Day:\s*([A-Za-z]+)/i);
    const timeMatch = text?.match(/Preferred Time:\s*([^\\n\\r]+)/i) || 
                     html?.match(/Preferred Time:\s*([^<]+)/i);
    const notesMatch = text?.match(/Additional Notes:\s*([^\\n\\r]+)/i) || 
                      html?.match(/Additional Notes:\s*([^<]+)/i);
    
    const preferredDay = dayMatch ? dayMatch[1].trim() : 'Not specified';
    const preferredTime = timeMatch ? timeMatch[1].trim() : 'Not specified';
    const notes = notesMatch ? notesMatch[1].trim() : '';
    
    // Create or update availability response
    const existingAvailability = await db
      .select()
      .from(candidateAvailability)
      .where(eq(candidateAvailability.candidateId, candidateId))
      .limit(1);
    
    const availabilityData = {
      respondedAt: new Date(),
      status: 'received' as const,
      availableSlots: JSON.stringify([
        `${preferredDay} 9:00 AM - 10:00 AM`,
        `${preferredDay} 10:00 AM - 11:00 AM`,
        `${preferredDay} 2:00 PM - 3:00 PM`,
        `${preferredDay} 3:00 PM - 4:00 PM`
      ]),
      selectedSlot: JSON.stringify({
        day: preferredDay,
        time: preferredTime,
        notes: notes || 'Available for interview',
        duration: '1 hour'
      }),
      updatedAt: new Date()
    };
    
    if (existingAvailability.length > 0) {
      // Update existing record
      await db
        .update(candidateAvailability)
        .set(availabilityData)
        .where(eq(candidateAvailability.id, existingAvailability[0].id));
    } else {
      // Create new record
      await db
        .insert(candidateAvailability)
        .values({
          candidateId,
          ...availabilityData
        });
    }
    
    console.log(`Updated availability for candidate ${candidateId}: ${preferredDay} ${preferredTime}`);
    
    res.json({ 
      success: true, 
      message: 'Email response processed successfully',
      candidateId,
      preferredDay,
      preferredTime
    });
    
  } catch (error) {
    console.error('Error processing email webhook:', error);
    res.status(500).json({ error: 'Failed to process email response' });
  }
});

// Manual email response entry (for testing)
router.post('/manual-response', async (req, res) => {
  try {
    const { candidateId, day, time, notes } = req.body;
    
    if (!candidateId || !day || !time) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Verify candidate exists
    const candidate = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);
      
    if (candidate.length === 0) {
      return res.status(404).json({ error: 'Candidate not found' });
    }
    
    const availabilityData = {
      candidateId,
      respondedAt: new Date(),
      status: 'received' as const,
      availableSlots: JSON.stringify([
        `${day} 9:00 AM - 10:00 AM`,
        `${day} 10:00 AM - 11:00 AM`,
        `${day} 2:00 PM - 3:00 PM`,
        `${day} 3:00 PM - 4:00 PM`
      ]),
      selectedSlot: JSON.stringify({
        day,
        time,
        notes: notes || 'Available for interview',
        duration: '1 hour'
      })
    };
    
    // Check if availability already exists
    const existing = await db
      .select()
      .from(candidateAvailability)
      .where(eq(candidateAvailability.candidateId, candidateId))
      .limit(1);
    
    if (existing.length > 0) {
      await db
        .update(candidateAvailability)
        .set({
          ...availabilityData,
          updatedAt: new Date()
        })
        .where(eq(candidateAvailability.id, existing[0].id));
    } else {
      await db
        .insert(candidateAvailability)
        .values(availabilityData);
    }
    
    res.json({ success: true, message: 'Response recorded successfully' });
    
  } catch (error) {
    console.error('Error recording manual response:', error);
    res.status(500).json({ error: 'Failed to record response' });
  }
});

export default router;