import express from 'express';
import { z } from 'zod';
import { db } from './db';
import { users, organizations } from '@shared/schema';
import { eq, and, desc } from 'drizzle-orm';
import { authenticateToken, requireRole, hashPassword } from './auth';
import type { AuthenticatedRequest } from './auth';

const router = express.Router();

// Schema for user invitation
const userInviteSchema = z.object({
  email: z.string().email(),
  full_name: z.string().min(1),
  role: z.enum(['admin', 'member', 'viewer']).default('member'),
  department: z.string().optional(),
});

// Schema for user updates
const userUpdateSchema = z.object({
  full_name: z.string().optional(),
  role: z.enum(['admin', 'member', 'viewer']).optional(),
  is_active: z.boolean().optional(),
  is_approved: z.boolean().optional(),
  department: z.string().optional(),
});

/**
 * @swagger
 * /users/me:
 *   get:
 *     summary: Get current user profile
 *     description: Retrieve the authenticated user's profile information
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.get('/me', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const user = await db
      .select()
      .from(users)
      .where(eq(users.id, req.user!.id))
      .limit(1);

    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(user[0]);
  } catch (error) {
    console.error('Error fetching current user:', error);
    res.status(500).json({ error: 'Failed to fetch user profile' });
  }
});

// Get all users in organization
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const organizationUsers = await db
      .select({
        id: users.id,
        email: users.email,
        full_name: users.fullName,
        role: users.role,
        organization_id: users.organizationId,
        is_active: users.isActive,
        is_approved: users.isApproved,
        created_at: users.createdAt,
        updated_at: users.updatedAt,
      })
      .from(users)
      .where(eq(users.organizationId, req.user!.organizationId))
      .orderBy(desc(users.createdAt));

    res.json(organizationUsers);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Get specific user by ID
router.get('/:userId', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const { userId } = req.params;

    const user = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.id, userId),
          eq(users.organizationId, req.user!.organizationId)
        )
      )
      .limit(1);

    if (!user.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(user[0]);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ error: 'Failed to fetch user' });
  }
});

// Update user (Admin only)
router.put('/:userId', authenticateToken, requireRole(['admin']), async (req: AuthenticatedRequest, res) => {
  try {
    const { userId } = req.params;
    const validation = userUpdateSchema.safeParse(req.body);
    
    if (!validation.success) {
      return res.status(400).json({ error: 'Invalid user data', details: validation.error.errors });
    }

    const updateData = validation.data;

    // Check if user exists and belongs to the same organization
    const existingUser = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.id, userId),
          eq(users.organizationId, req.user!.organizationId)
        )
      )
      .limit(1);

    if (!existingUser.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Prevent self-deactivation
    if (userId === req.user!.id && updateData.is_active === false) {
      return res.status(400).json({ error: 'Cannot deactivate your own account' });
    }

    // Update user
    const updatedUser = await db
      .update(users)
      .set({
        fullName: updateData.full_name,
        role: updateData.role,
        isActive: updateData.is_active,
        isApproved: updateData.is_approved,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(users.id, userId),
          eq(users.organizationId, req.user!.organizationId)
        )
      )
      .returning();

    res.json(updatedUser[0]);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Invite user (Admin only)
router.post('/invite', authenticateToken, requireRole(['admin']), async (req: AuthenticatedRequest, res) => {
  try {
    const validation = userInviteSchema.safeParse(req.body);
    
    if (!validation.success) {
      return res.status(400).json({ error: 'Invalid invitation data', details: validation.error.errors });
    }

    const inviteData = validation.data;

    // Check if user already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, inviteData.email))
      .limit(1);

    if (existingUser.length) {
      return res.status(400).json({ error: 'User with this email already exists' });
    }

    // In a real implementation, you would:
    // 1. Create an invitation record
    // 2. Send an invitation email
    // 3. Store the invitation token
    
    console.log('User invitation sent:', inviteData);

    // Create a mock invitation response
    const invitation = {
      id: `inv_${Date.now()}`,
      email: inviteData.email,
      full_name: inviteData.full_name,
      role: inviteData.role,
      created_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      invited_by: req.user!.id,
    };

    res.json(invitation);
  } catch (error) {
    console.error('Error sending invitation:', error);
    res.status(500).json({ error: 'Failed to send invitation' });
  }
});

// Get pending invitations
router.get('/invitations', authenticateToken, requireRole(['admin']), async (req: AuthenticatedRequest, res) => {
  try {
    // In a real implementation, you would fetch from a user_invitations table
    const invitations = [
      {
        id: 'inv_1',
        email: '<EMAIL>',
        full_name: 'Jane Doe',
        role: 'member',
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        invited_by: req.user!.id,
      },
      {
        id: 'inv_2',
        email: '<EMAIL>',
        full_name: 'John Smith',
        role: 'viewer',
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
        invited_by: req.user!.id,
      }
    ];

    res.json(invitations);
  } catch (error) {
    console.error('Error fetching invitations:', error);
    res.status(500).json({ error: 'Failed to fetch invitations' });
  }
});

// Deactivate user (Admin only)
router.delete('/:userId', authenticateToken, requireRole(['admin']), async (req: AuthenticatedRequest, res) => {
  try {
    const { userId } = req.params;

    // Prevent self-deactivation
    if (userId === req.user!.id) {
      return res.status(400).json({ error: 'Cannot deactivate your own account' });
    }

    // Check if user exists and belongs to the same organization
    const existingUser = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.id, userId),
          eq(users.organizationId, req.user!.organizationId)
        )
      )
      .limit(1);

    if (!existingUser.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Deactivate user (soft delete)
    await db
      .update(users)
      .set({
        isActive: false,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(users.id, userId),
          eq(users.organizationId, req.user!.organizationId)
        )
      );

    res.json({ success: true });
  } catch (error) {
    console.error('Error deactivating user:', error);
    res.status(500).json({ error: 'Failed to deactivate user' });
  }
});

export default router;