
import { useState } from 'react';
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { EnhancedAnalysisResult } from "@/types/resumeAnalysis";

export const useResumeAnalysis = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<EnhancedAnalysisResult | null>(null);
  const { toast } = useToast();

  const saveCandidateData = async (analysisResult: EnhancedAnalysisResult, resumeText: string) => {
    try {
      // Check if candidate already exists by email
      const { data: existingCandidate } = await supabase
        .from('candidates')
        .select('id')
        .eq('email', analysisResult.email || '')
        .maybeSingle();

      // Ensure experience_years is a proper integer
      let experienceYears = 0;
      if (typeof analysisResult.experience_years === 'number') {
        experienceYears = analysisResult.experience_years;
      } else if (typeof analysisResult.experience_years === 'string') {
        const parsed = parseInt(analysisResult.experience_years);
        experienceYears = isNaN(parsed) ? 0 : parsed;
      }

      const candidateData = {
        full_name: analysisResult.name || 'Unknown',
        current_position: analysisResult.recent_roles?.[0]?.title || null,
        current_company: analysisResult.recent_roles?.[0]?.company || null,
        experience_years: experienceYears,
        education: analysisResult.education || null,
        skills: analysisResult.skill_analysis?.matched_skills || [],
        resume_text: resumeText,
        phone: analysisResult.phone || null,
        location: analysisResult.location || null,
        linkedin_url: analysisResult.linkedin || null,
        updated_at: new Date().toISOString()
      };

      if (existingCandidate) {
        console.log('Candidate already exists, updating record');
        const { error } = await supabase
          .from('candidates')
          .update(candidateData)
          .eq('id', existingCandidate.id);

        if (error) throw error;
        return existingCandidate.id;
      } else {
        // Create new candidate
        const { data, error } = await supabase
          .from('candidates')
          .insert({
            ...candidateData,
            email: analysisResult.email || ''
          })
          .select('id')
          .single();

        if (error) throw error;
        console.log('New candidate created:', data.id);
        return data.id;
      }
    } catch (error) {
      console.error('Error saving candidate data:', error);
      return null;
    }
  };

  const analyzeResumeData = async (
    resumeText: string,
    jobDescription: string,
    extractedEmail?: string,
    extractedName?: string,
    extractedPhone?: string,
    extractedLocation?: string,
    extractedLinkedin?: string,
    workExperience?: any[],
    education?: any[],
    skills?: string[]
  ) => {
    setIsAnalyzing(true);
    
    try {
      const response = await fetch(`https://zlovwuxwlhpppuazviya.supabase.co/functions/v1/analyze-resume`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpsb3Z3dXh3bGhwcHB1YXp2aXlhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyMTM5MjEsImV4cCI6MjA2NTc4OTkyMX0.0Jxaq2YtkMBJRM_9UO2kYFzpvfrccdb7M3Q3Ui4xcwo`,
        },
        body: JSON.stringify({
          resumeText,
          jobDescription,
          extractedEmail,
          extractedName,
          extractedPhone,
          extractedLocation,
          extractedLinkedin,
          workExperience,
          education,
          skills
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API Response Error:", errorText);
        throw new Error(`Analysis failed: ${response.status} - ${errorText}`);
      }

      const analysis: EnhancedAnalysisResult = await response.json();
      
      // Use extracted data if available and analysis is missing it, but avoid fake data
      const enhancedAnalysis = {
        ...analysis,
        email: analysis.email && !analysis.email.includes('example.com') ? analysis.email : (extractedEmail || ''),
        name: analysis.name && analysis.name !== 'Not Available' ? analysis.name : (extractedName || 'Unknown Candidate'),
        phone: analysis.phone && analysis.phone !== '************' ? analysis.phone : extractedPhone,
        location: analysis.location && analysis.location !== 'Not Available' ? analysis.location : extractedLocation,
        linkedin: analysis.linkedin && !analysis.linkedin.includes('example') ? analysis.linkedin : extractedLinkedin
      };
      
      console.log("Enhanced analysis result:", enhancedAnalysis);
      
      // Save candidate data to Supabase
      const candidateId = await saveCandidateData(enhancedAnalysis, resumeText);
      if (candidateId) {
        enhancedAnalysis.candidate_id = candidateId;
      }
      
      setAnalysisResult(enhancedAnalysis);
      
      toast({
        title: "Resume analyzed successfully",
        description: `${enhancedAnalysis.name} - Match Score: ${enhancedAnalysis.match_score}%`,
      });

      return enhancedAnalysis;
    } catch (error) {
      console.error("Error analyzing resume:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to analyze resume";
      
      toast({
        title: "Analysis failed",
        description: errorMessage,
        variant: "destructive",
      });
      
      throw error;
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleDecision = async (decision: 'approve' | 'reject', selectedJobId?: string): Promise<void> => {
    if (!analysisResult) return;

    try {
      console.log(`Processing ${decision} decision for ${analysisResult.name}`);
      
      // Store the application result in database
      const { error } = await supabase
        .from('applications')
        .insert({
          candidate_id: analysisResult.candidate_id || null,
          job_posting_id: selectedJobId || null,
          match_score: analysisResult.match_score,
          status: decision === 'approve' ? 'hired' : 'rejected',
          resume_analysis: analysisResult as any,
          screening_notes: `${decision.toUpperCase()} - Overall Score: ${analysisResult.overall_score}/10, Match: ${analysisResult.match_score}%`
        });

      if (error) {
        console.error('Error saving application:', error);
      }

      toast({
        title: `Candidate ${decision}d`,
        description: `${analysisResult.name} has been ${decision}d and ${decision === 'approve' ? 'moved to next stage' : 'archived'}`,
        variant: decision === 'approve' ? 'default' : 'destructive',
      });
    } catch (error) {
      console.error('Error processing decision:', error);
    }
  };

  return {
    isAnalyzing,
    analysisResult,
    analyzeResumeData,
    handleDecision,
    setAnalysisResult
  };
};
