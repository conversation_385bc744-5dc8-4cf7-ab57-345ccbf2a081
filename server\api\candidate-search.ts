import { Router } from 'express';
import { db } from '../db';
import { candidates, jobPostings } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { linkedinService } from '../services/linkedinService';
import OpenAI from 'openai';

const router = Router();

// Initialize OpenAI client
const openai = new OpenAI({ 
  apiKey: process.env.OPENAI_API_KEY 
});

// Function to generate candidate profiles using OpenAI
async function generateCandidateProfilesWithAI(jobDescription: string, jobTitle: string, filters: SearchFilters, maxResults: number = 10): Promise<CandidateProfile[]> {
  try {
    const prompt = `Generate ${maxResults} realistic candidate profiles for this specific job posting:

Job Title: ${jobTitle}
Job Description: ${jobDescription}

CRITICAL REQUIREMENTS:
1. ALL candidates must be located in the United States (use real US cities and states)
2. ALL candidates must have skills directly relevant to this specific job
3. ALL candidates must have experience levels that match the job requirements
4. Create realistic professional backgrounds that would actually apply for this role
5. Use diverse but realistic names representing various backgrounds
6. Companies should be real or realistic US companies in the relevant industry

Generate candidates with:
- Locations: Major US cities only (New York NY, San Francisco CA, Austin TX, Seattle WA, Chicago IL, Boston MA, etc.)
- Skills: Must directly match the job requirements from the description
- Experience: Should align with what the job actually requires
- Companies: Real or realistic companies in the relevant industry sector
- Education: Relevant degrees from real US universities
- Match scores: 70-95% (only high-quality matches)

Filters to strictly apply:
- Location requirement: USA only (${filters.location || 'USA cities only'})
- Experience level: ${filters.experience || 'match job requirements'}
- Required skills: ${filters.skills?.join(', ') || 'extract from job description'}

Return the data as a JSON object with this EXACT structure (no comments, no extra text):
{
  "candidates": [
    {
      "id": "candidate_1_timestamp",
      "name": "Full Name",
      "title": "Current Job Title", 
      "company": "Current Company",
      "location": "City, State/Country",
      "experience": "number_as_string",
      "skills": ["skill1", "skill2", "skill3"],
      "education": "University Name, Degree",
      "profileUrl": "https://linkedin.com/in/profilename",
      "summary": "Professional summary paragraph",
      "matchScore": 85,
      "availability": "active",
      "contactInfo": {
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "linkedin": "https://linkedin.com/in/profilename"
      },
      "source": "openai_generated"
    }
  ]
}

Focus on creating profiles that would realistically apply for and qualify for this specific job. Each candidate should be someone who would genuinely be interested in and qualified for this exact role. Ensure diversity in gender, ethnicity, and backgrounds while maintaining USA locations and job relevance.`;

    console.log('Generating candidate profiles with OpenAI...');
    
    // Try different models in order of preference
    const modelsToTry = ["gpt-4o", "gpt-4", "gpt-3.5-turbo"];
    let response;
    let modelUsed = "";
    
    for (const model of modelsToTry) {
      try {
        console.log(`Trying model: ${model}`);
        
        response = await openai.chat.completions.create({
          model: model,
          messages: [
            {
              role: "system",
              content: "You are an expert recruiter and HR professional. Generate realistic, diverse candidate profiles that accurately match job requirements. Return ONLY valid JSON - no comments, no explanations, no markdown formatting. The response must be a valid JSON object with a 'candidates' array."
            },
            {
              role: "user", 
              content: prompt + "\n\nIMPORTANT: Return ONLY valid JSON in this exact format: {\"candidates\": [your_candidate_array]}. No markdown formatting, no comments, no additional text."
            }
          ],
          response_format: model.includes("gpt-4") ? { type: "json_object" } : undefined,
          temperature: 0.7,
          max_tokens: 4000
        });
        
        modelUsed = model;
        console.log(`Successfully using model: ${model}`);
        break;
        
      } catch (modelError: any) {
        console.log(`Model ${model} failed:`, modelError.message);
        if (model === modelsToTry[modelsToTry.length - 1]) {
          throw modelError; // Throw if last model also fails
        }
        continue;
      }
    }

    const aiResponse = response.choices[0].message.content;
    
    if (!aiResponse) {
      throw new Error('No response from OpenAI');
    }

    let candidates;
    try {
      // Clean the response by removing any markdown formatting or extra text
      let cleanedResponse = aiResponse.trim();
      
      // Remove markdown code blocks if present
      cleanedResponse = cleanedResponse.replace(/```json\s*/g, '').replace(/```\s*/g, '');
      
      // Remove any leading/trailing text that's not JSON
      const jsonStart = cleanedResponse.indexOf('{');
      const jsonEnd = cleanedResponse.lastIndexOf('}') + 1;
      if (jsonStart !== -1 && jsonEnd > jsonStart) {
        cleanedResponse = cleanedResponse.substring(jsonStart, jsonEnd);
      }
      
      console.log('Cleaned AI response:', cleanedResponse.substring(0, 200) + '...');
      
      const parsed = JSON.parse(cleanedResponse);
      // Handle both array and object responses
      candidates = Array.isArray(parsed) ? parsed : (parsed.candidates || []);
      
      if (!candidates || candidates.length === 0) {
        throw new Error('No candidates found in AI response');
      }
      
    } catch (parseError) {
      console.error('Failed to parse OpenAI response:', parseError);
      console.log('Raw AI response (first 500 chars):', aiResponse.substring(0, 500));
      throw new Error('Invalid JSON response from AI');
    }

    // Add timestamps and ensure proper structure
    const timestamp = Date.now();
    const processedCandidates = candidates.map((candidate: any, index: number) => ({
      ...candidate,
      id: candidate.id || `candidate_${index}_${timestamp}`,
      source: 'openai_generated'
    }));

    console.log(`Generated ${processedCandidates.length} AI-powered candidate profiles using ${modelUsed}`);
    return processedCandidates;

  } catch (error: any) {
    console.error('OpenAI candidate generation failed:', error);
    
    // If OpenAI fails, fall back to enhanced mock data
    console.log('Falling back to enhanced mock data...');
    return generateMockCandidates(jobDescription, jobTitle, filters);
  }
}

interface CandidateProfile {
  id: string;
  name: string;
  title: string;
  company: string;
  location: string;
  experience: string;
  skills: string[];
  education: string;
  profileUrl: string;
  summary: string;
  matchScore: number;
  availability: 'active' | 'passive' | 'unknown';
  contactInfo?: {
    email?: string;
    phone?: string;
    linkedin?: string;
  };
  source: 'linkedin' | 'github' | 'indeed' | 'glassdoor' | 'internal';
}

interface SearchFilters {
  location?: string;
  experience?: string;
  skills?: string[];
  salary?: string;
  availability?: string;
  source?: string;
}

// Mock candidate data for demonstration (in production, this would integrate with real APIs)
const generateMockCandidates = (jobDescription: string, jobTitle: string, filters: SearchFilters): CandidateProfile[] => {
  const candidateTemplates = [
    {
      name: "Sarah Chen",
      title: "Senior Software Engineer",
      company: "Google",
      location: "San Francisco, CA",
      experience: "7",
      skills: ["React", "TypeScript", "Node.js", "AWS", "Python", "GraphQL"],
      education: "MS Computer Science, Stanford University",
      summary: "Full-stack engineer with expertise in scalable web applications and cloud architecture. Led teams of 5+ engineers on high-impact products.",
      source: "linkedin" as const,
      availability: "passive" as const,
      contactInfo: {
        email: "<EMAIL>",
        linkedin: "https://linkedin.com/in/sarahchen"
      }
    },
    {
      name: "Marcus Rodriguez",
      title: "Principal Engineering Manager",
      company: "Microsoft",
      location: "Seattle, WA",
      experience: "12",
      skills: ["Engineering Leadership", "System Design", "C#", ".NET", "Azure", "DevOps"],
      education: "BS Computer Engineering, MIT",
      summary: "Engineering leader with 12+ years building enterprise software. Managed teams of 20+ engineers across multiple products.",
      source: "linkedin" as const,
      availability: "active" as const,
      contactInfo: {
        email: "<EMAIL>",
        linkedin: "https://linkedin.com/in/marcusrodriguez"
      }
    },
    {
      name: "Jennifer Kim",
      title: "Product Manager",
      company: "Stripe",
      location: "Remote",
      experience: "5",
      skills: ["Product Strategy", "Data Analysis", "User Research", "Agile", "SQL", "Figma"],
      education: "MBA, Harvard Business School",
      summary: "Product manager focused on fintech and payments. Launched 3 major features that increased user engagement by 40%.",
      source: "linkedin" as const,
      availability: "passive" as const,
      contactInfo: {
        email: "<EMAIL>",
        linkedin: "https://linkedin.com/in/jenniferkim"
      }
    },
    {
      name: "David Thompson",
      title: "DevOps Engineer",
      company: "Netflix",
      location: "Los Angeles, CA",
      experience: "6",
      skills: ["Kubernetes", "Docker", "AWS", "Terraform", "Python", "Monitoring"],
      education: "BS Software Engineering, UC Berkeley",
      summary: "DevOps specialist with expertise in containerization and cloud infrastructure. Reduced deployment time by 75% through automation.",
      source: "github" as const,
      availability: "active" as const,
      contactInfo: {
        email: "<EMAIL>",
        linkedin: "https://linkedin.com/in/davidthompson"
      }
    },
    {
      name: "Lisa Wang",
      title: "UX Design Lead",
      company: "Airbnb",
      location: "San Francisco, CA",
      experience: "8",
      skills: ["User Experience", "Design Systems", "Prototyping", "User Research", "Figma", "Adobe Creative Suite"],
      education: "MFA Interaction Design, Art Center",
      summary: "Design leader passionate about creating intuitive user experiences. Led design for products used by 50M+ users globally.",
      source: "linkedin" as const,
      availability: "passive" as const,
      contactInfo: {
        email: "<EMAIL>",
        linkedin: "https://linkedin.com/in/lisawang"
      }
    },
    {
      name: "Ahmed Hassan",
      title: "Data Scientist",
      company: "Uber",
      location: "New York, NY",
      experience: "4",
      skills: ["Machine Learning", "Python", "SQL", "TensorFlow", "Statistics", "Big Data"],
      education: "PhD Data Science, NYU",
      summary: "Data scientist specializing in machine learning and predictive analytics. Built ML models that improved business efficiency by 30%.",
      source: "github" as const,
      availability: "active" as const,
      contactInfo: {
        email: "<EMAIL>",
        linkedin: "https://linkedin.com/in/ahmedhassan"
      }
    },
    {
      name: "Rachel Green",
      title: "Marketing Director",
      company: "HubSpot",
      location: "Boston, MA",
      experience: "9",
      skills: ["Digital Marketing", "Content Strategy", "SEO", "Analytics", "Team Leadership", "Growth Hacking"],
      education: "MBA Marketing, Wharton",
      summary: "Marketing leader with proven track record in B2B SaaS growth. Scaled marketing team from 5 to 25 people, 300% revenue growth.",
      source: "linkedin" as const,
      availability: "passive" as const,
      contactInfo: {
        email: "<EMAIL>",
        linkedin: "https://linkedin.com/in/rachelgreen"
      }
    },
    {
      name: "Carlos Mendoza",
      title: "Cybersecurity Architect",
      company: "Palantir",
      location: "Denver, CO",
      experience: "10",
      skills: ["Information Security", "Penetration Testing", "Risk Assessment", "Compliance", "AWS Security", "Zero Trust"],
      education: "MS Cybersecurity, Carnegie Mellon",
      summary: "Security expert with expertise in enterprise security architecture. Led security initiatives for Fortune 500 companies.",
      source: "linkedin" as const,
      availability: "active" as const,
      contactInfo: {
        email: "<EMAIL>",
        linkedin: "https://linkedin.com/in/carlosmendoza"
      }
    }
  ];

  // Calculate match scores based on job description
  const candidates = candidateTemplates.map((template, index) => {
    let matchScore = 50; // Base score
    
    // Boost score based on skill overlap
    const jobDescLower = jobDescription.toLowerCase();
    const titleLower = jobTitle.toLowerCase();
    
    template.skills.forEach(skill => {
      if (jobDescLower.includes(skill.toLowerCase()) || titleLower.includes(skill.toLowerCase())) {
        matchScore += 10;
      }
    });
    
    // Boost score based on title relevance
    if (titleLower.includes(template.title.toLowerCase().split(' ')[0])) {
      matchScore += 15;
    }
    
    // Apply location filter (only if location is specified)
    if (filters.location && filters.location.trim() && !template.location.toLowerCase().includes(filters.location.toLowerCase())) {
      matchScore -= 10;
    }
    
    // Apply experience filter (don't filter if 'any' is selected)
    const expYears = parseInt(template.experience);
    if (filters.experience && filters.experience !== 'any') {
      switch (filters.experience) {
        case 'entry':
          if (expYears > 2) matchScore -= 15;
          break;
        case 'mid':
          if (expYears < 3 || expYears > 5) matchScore -= 10;
          break;
        case 'senior':
          if (expYears < 6 || expYears > 10) matchScore -= 10;
          break;
        case 'lead':
          if (expYears < 10) matchScore -= 15;
          break;
      }
    }
    
    // Apply source filter (don't filter if 'all' is selected)
    if (filters.source && filters.source !== 'all' && template.source !== filters.source) {
      return null; // Filter out non-matching sources
    }
    
    // Cap the score at 95
    matchScore = Math.min(matchScore, 95);
    
    return {
      ...template,
      id: `candidate_${index}_${Date.now()}`,
      profileUrl: `https://${template.source}.com/in/${template.name.toLowerCase().replace(' ', '')}`,
      matchScore
    };
  }).filter(candidate => candidate !== null) as CandidateProfile[];

  // Sort by match score descending
  return candidates.sort((a, b) => b.matchScore - a.matchScore);
};

// Search for candidates based on job description
router.post('/search', async (req, res) => {
  try {
    const { jobDescription, jobTitle, filters = {}, maxResults = 50 } = req.body;
    
    console.log('Candidate search request:', {
      jobTitle,
      jobDescriptionLength: jobDescription?.length,
      filters,
      maxResults,
      linkedinConfigured: linkedinService.isConfigured()
    });
    
    if (!jobDescription && !jobTitle) {
      return res.status(400).json({ error: 'Job description or title is required' });
    }

    let candidates: any[] = [];
    let searchSource = 'openai';

    // Use OpenAI to generate realistic candidate profiles
    if (process.env.OPENAI_API_KEY) {
      try {
        console.log('Using OpenAI to generate candidate profiles...');
        candidates = await generateCandidateProfilesWithAI(jobDescription, jobTitle, filters, maxResults);
        searchSource = 'openai_generated';
        
        console.log(`Generated ${candidates.length} AI-powered candidate profiles`);
      } catch (openaiError) {
        console.error('OpenAI generation failed, trying LinkedIn fallback:', openaiError);
        
        // Try LinkedIn as secondary option
        if (linkedinService.isConfigured()) {
          try {
            console.log('Using LinkedIn API as fallback...');
            
            const linkedinProfiles = await linkedinService.searchPeople({
              keywords: `${jobTitle} ${jobDescription.slice(0, 100)}`,
              title: jobTitle,
              location: filters.location,
              count: Math.min(maxResults, 25)
            });

            candidates = linkedinProfiles.map(profile => 
              linkedinService.transformToCandidate(profile, 75)
            );
            searchSource = 'linkedin_fallback';
            
          } catch (linkedinError) {
            console.error('LinkedIn fallback failed, using enhanced mock data:', linkedinError);
            candidates = generateMockCandidates(jobDescription || '', jobTitle || '', filters);
            searchSource = 'mock_fallback';
          }
        } else {
          console.log('LinkedIn not configured, using enhanced mock data');
          candidates = generateMockCandidates(jobDescription || '', jobTitle || '', filters);
          searchSource = 'mock_fallback';
        }
      }
    } else {
      console.log('OpenAI API key not configured, using enhanced mock data');
      candidates = generateMockCandidates(jobDescription || '', jobTitle || '', filters);
      searchSource = 'mock_fallback';
    }
    
    const limitedResults = candidates.slice(0, maxResults);
    
    console.log(`Returning ${limitedResults.length} candidate profiles from ${searchSource}`);
    
    res.json({
      candidates: limitedResults,
      totalFound: limitedResults.length,
      searchMetadata: {
        jobTitle,
        source: searchSource,
        filtersApplied: Object.keys(filters).filter(key => filters[key]),
        searchTimestamp: new Date().toISOString(),
        openaiConfigured: !!process.env.OPENAI_API_KEY,
        linkedinConfigured: linkedinService.isConfigured()
      }
    });
  } catch (error) {
    console.error('Candidate search error:', error);
    res.status(500).json({ error: 'Failed to search candidates' });
  }
});

// Get candidate suggestions based on existing job postings
router.get('/suggestions/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;
    
    // Get job posting details
    const jobPosting = await db.select()
      .from(jobPostings)
      .where(eq(jobPostings.id, jobId))
      .limit(1);
    
    if (jobPosting.length === 0) {
      return res.status(404).json({ error: 'Job posting not found' });
    }
    
    const job = jobPosting[0];
    const candidates = generateMockCandidates(job.description, job.title, {});
    
    res.json({
      candidates: candidates.slice(0, 10), // Top 10 suggestions
      jobPosting: job
    });
    
  } catch (error) {
    console.error('Error getting candidate suggestions:', error);
    res.status(500).json({ error: 'Failed to get candidate suggestions' });
  }
});

export default router;