import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Calendar, Clock, User, Bot, MoreHorizontal, Edit, Trash2, RefreshCw, ExternalLink, Copy, Play } from 'lucide-react';
import { toast } from 'sonner';

interface Interview {
  id: string;
  candidateId: string;
  role: string;
  scheduledAt: string;
  durationMin: number;
  status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';
  sdkType: 'video' | 'meeting';
  roomOrMeetingId?: string;
  joinUrls?: {
    candidate: string;
    host: string;
  };
  agentProfileId?: string;
  notes?: string;
  candidate?: {
    id: string;
    fullName: string;
    email: string;
    phone?: string;
  };
  agentProfile?: {
    id: string;
    name: string;
    scriptVersion: string;
  };
}

interface InterviewListProps {
  interviews: Interview[];
  onInterviewUpdated: (interview: Interview) => void;
  onRefresh: () => void;
}

const InterviewList: React.FC<InterviewListProps> = ({
  interviews,
  onInterviewUpdated,
  onRefresh
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [startingBot, setStartingBot] = useState<string | null>(null);
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(null);
  const [isRescheduleDialogOpen, setIsRescheduleDialogOpen] = useState(false);
  const [rescheduleData, setRescheduleData] = useState({
    newScheduledAt: '',
    reason: ''
  });
  const [loading, setLoading] = useState(false);

  const filteredInterviews = interviews.filter(interview => {
    const matchesSearch = !searchTerm || 
      interview.candidate?.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      interview.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
      interview.candidate?.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || interview.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'rescheduled': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleCancelInterview = async (interviewId: string) => {
    const reason = prompt('Please provide a reason for cancellation (optional):');
    
    if (reason === null) return; // User cancelled the prompt

    setLoading(true);

    try {
      const response = await fetch(`/api/interviews-v2/${interviewId}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({ reason })
      });

      if (response.ok) {
        const data = await response.json();
        onInterviewUpdated(data.interview);
        toast.success('Interview cancelled successfully');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to cancel interview');
      }
    } catch (error) {
      console.error('Error cancelling interview:', error);
      toast.error('Failed to cancel interview');
    } finally {
      setLoading(false);
    }
  };

  const startBotForInterview = async (interviewId: string) => {
    setStartingBot(interviewId);
    try {
      console.log('🤖 Starting bot for interview:', interviewId);

      const response = await fetch(`/api/bot-runner/start/${interviewId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Bot started successfully:', data);
        toast.success('AI Agent has joined the interview session');
        onRefresh(); // Refresh the interview list
      } else {
        const errorData = await response.json();
        console.error('❌ Failed to start bot:', errorData);
        toast.error(errorData.error || 'Failed to start AI Agent');
      }
    } catch (error) {
      console.error('❌ Error starting bot:', error);
      toast.error('Failed to start AI Agent. Please try again.');
    } finally {
      setStartingBot(null);
    }
  };

  const handleRescheduleInterview = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedInterview || !rescheduleData.newScheduledAt) {
      toast.error('Please select a new date and time');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/interviews-v2/${selectedInterview.id}/reschedule`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify(rescheduleData)
      });

      if (response.ok) {
        const data = await response.json();
        onInterviewUpdated(data.interview);
        setIsRescheduleDialogOpen(false);
        setSelectedInterview(null);
        setRescheduleData({ newScheduledAt: '', reason: '' });
        toast.success('Interview rescheduled successfully');
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to reschedule interview');
      }
    } catch (error) {
      console.error('Error rescheduling interview:', error);
      toast.error('Failed to reschedule interview');
    } finally {
      setLoading(false);
    }
  };

  const copyJoinUrl = (url: string, type: 'candidate' | 'host') => {
    navigator.clipboard.writeText(url);
    toast.success(`${type === 'candidate' ? 'Candidate' : 'Host'} join URL copied to clipboard`);
  };

  const openRescheduleDialog = (interview: Interview) => {
    setSelectedInterview(interview);
    setRescheduleData({
      newScheduledAt: new Date(interview.scheduledAt).toISOString().slice(0, 16),
      reason: ''
    });
    setIsRescheduleDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search by candidate name, role, or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="scheduled">Scheduled</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
              <SelectItem value="rescheduled">Rescheduled</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={onRefresh}>
            <RefreshCw className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Interview Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {filteredInterviews.map(interview => (
          <Card key={interview.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div>
                    <CardTitle className="text-lg">{interview.role}</CardTitle>
                    <CardDescription className="flex items-center space-x-2">
                      <User className="w-4 h-4" />
                      <span>{interview.candidate?.fullName}</span>
                    </CardDescription>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge className={getStatusColor(interview.status)}>
                    {interview.status}
                  </Badge>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {interview.status === 'scheduled' && (
                        <>
                          <DropdownMenuItem
                            onClick={() => startBotForInterview(interview.id)}
                            disabled={startingBot === interview.id}
                            className="text-green-600"
                          >
                            {startingBot === interview.id ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-2"></div>
                                Starting Agent...
                              </>
                            ) : (
                              <>
                                <Play className="w-4 h-4 mr-2" />
                                Start AI Agent
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => openRescheduleDialog(interview)}>
                            <Edit className="w-4 h-4 mr-2" />
                            Reschedule
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleCancelInterview(interview.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Cancel
                          </DropdownMenuItem>
                        </>
                      )}
                      {interview.joinUrls?.candidate && (
                        <DropdownMenuItem onClick={() => copyJoinUrl(interview.joinUrls!.candidate, 'candidate')}>
                          <Copy className="w-4 h-4 mr-2" />
                          Copy Candidate URL
                        </DropdownMenuItem>
                      )}
                      {interview.joinUrls?.host && (
                        <DropdownMenuItem onClick={() => copyJoinUrl(interview.joinUrls!.host, 'host')}>
                          <Copy className="w-4 h-4 mr-2" />
                          Copy Host URL
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Calendar className="w-4 h-4" />
                  <span>{new Date(interview.scheduledAt).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <Clock className="w-4 h-4" />
                  <span>{new Date(interview.scheduledAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                </div>
              </div>

              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Bot className="w-4 h-4" />
                <span>{interview.agentProfile?.name || 'Default Agent'}</span>
                {interview.agentProfile?.scriptVersion && (
                  <Badge variant="outline" className="text-xs">
                    {interview.agentProfile.scriptVersion}
                  </Badge>
                )}
              </div>

              <div className="text-sm text-gray-600">
                <p><strong>Email:</strong> {interview.candidate?.email}</p>
                <p><strong>Duration:</strong> {interview.durationMin} minutes</p>
              </div>

              {interview.notes && (
                <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                  <p className="line-clamp-2">{interview.notes}</p>
                </div>
              )}

              {interview.joinUrls && interview.status === 'scheduled' && (
                <div className="flex space-x-2 pt-2">
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => window.open(interview.joinUrls!.candidate, '_blank')}
                  >
                    <ExternalLink className="w-3 h-3 mr-1" />
                    Candidate Link
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => window.open(interview.joinUrls!.host, '_blank')}
                  >
                    <ExternalLink className="w-3 h-3 mr-1" />
                    Host Link
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredInterviews.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Interviews Found</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Schedule your first interview to get started'
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Reschedule Dialog */}
      <Dialog open={isRescheduleDialogOpen} onOpenChange={setIsRescheduleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reschedule Interview</DialogTitle>
            <DialogDescription>
              Change the interview time for {selectedInterview?.candidate?.fullName}
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleRescheduleInterview} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="newScheduledAt" className="text-sm font-medium">
                New Date & Time
              </label>
              <Input
                id="newScheduledAt"
                type="datetime-local"
                value={rescheduleData.newScheduledAt}
                onChange={(e) => setRescheduleData(prev => ({ ...prev, newScheduledAt: e.target.value }))}
                min={new Date().toISOString().slice(0, 16)}
                required
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="reason" className="text-sm font-medium">
                Reason (optional)
              </label>
              <Input
                id="reason"
                value={rescheduleData.reason}
                onChange={(e) => setRescheduleData(prev => ({ ...prev, reason: e.target.value }))}
                placeholder="e.g., Candidate requested different time"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsRescheduleDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Rescheduling...' : 'Reschedule'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InterviewList;
