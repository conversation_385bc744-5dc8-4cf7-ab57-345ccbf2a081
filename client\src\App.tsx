
import React from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useNavigate, useLocation } from "react-router-dom";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import { CandidateProvider } from "@/contexts/CandidateContext";
import Index from "./pages/Index";
import AuthPage from "./pages/AuthPage";
import NotFound from "./pages/NotFound";
import ResumeAnalysisApp from "./components/ResumeAnalysisApp";
import EnhancedResumeScreening from "./components/EnhancedResumeScreening";
import EnhancedJobPostings from "./components/EnhancedJobPostings";
import InterviewScheduling from "./components/InterviewScheduling";
import CandidateTracker from "./components/CandidateTracker";
import CandidateSearchAgent from "./components/CandidateSearchAgent";
import TileJobSearch from "./components/TileJobSearch";
import SuperAdminPage from "./pages/SuperAdminPage";
import UserManagementPage from "./pages/UserManagementPage";
import Sidebar from "./components/Sidebar";
import UserManagement from "./components/UserManagement";
import SSOSetup from "./components/SSOSetup";
import JobPostingCollaboration from "./pages/JobPostingCollaboration";
import TwilioSetupPage from "./pages/TwilioSetupPage";
import ConversationalAIDemo from "./components/ConversationalAIDemo";
import InterviewAutomation from "./components/InterviewAutomation";
import CandidateJoinPage from "./components/CandidateJoinPage";

import SubscriptionManager from "./components/SubscriptionManager";
import HomePage from "./pages/HomePage";
import BookDemoPage from "./pages/BookDemoPage";
import PublicJobPortal from "./pages/PublicJobPortal";
import Inbox from "./components/Inbox";
import Dashboard from "./components/Dashboard";
import AccessibilityProvider from "./components/AccessibilityProvider";

const queryClient = new QueryClient();

function AppContent() {
  const { user, isLoading, isAuthenticated, error } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();


  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Handle public routes first (accessible when not logged in)
  if (location.pathname === '/book-demo') {
    return <BookDemoPage />;
  }

  if (location.pathname === '/jobs') {
    return <PublicJobPortal />;
  }

  // Handle candidate interview join page (public route)
  if (location.pathname.startsWith('/interview-join')) {
    const urlParams = new URLSearchParams(location.search);
    const interviewId = urlParams.get('interviewId');

    if (!interviewId) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Invalid Interview Link</h2>
            <p className="text-gray-600">The interview link is missing required information.</p>
          </div>
        </div>
      );
    }

    return <CandidateJoinPage interviewId={interviewId} />;
  }

  // Handle authentication - redirect to dashboard if already authenticated
  if (location.pathname === '/auth') {
    if (isAuthenticated && user) {
      // User is already authenticated, redirect to dashboard
      navigate('/dashboard');
      return null; // Return null while redirecting
    }
    // 🎯 PASS SESSION ERROR TO AUTH PAGE
    return <AuthPage sessionError={error} />;
  }

  // Show home page if not authenticated - but pass session error if available
  if (!isAuthenticated || !user) {
    if (error && error.includes('Session expired')) {
      // 🎯 REDIRECT TO AUTH WITH SESSION ERROR
      navigate('/auth', { replace: true, state: { sessionError: error } });
      return null;
    }
    return <HomePage />;
  }

  // Show pending approval message - only if user data is fully loaded and definitely not approved
  if (!user.isApproved && !isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center p-8">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto">
            <h2 className="text-xl font-semibold text-yellow-800 mb-2">Account Pending Approval</h2>
            <p className="text-yellow-700">
              Your account is awaiting admin approval. Please contact your organization administrator.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const getCurrentPage = () => {
    const path = location.pathname;
    if (path === '/') return 'dashboard';
    if (path === '/resume-screening') return 'screening';
    if (path === '/candidate-search') return 'career-site';
    if (path === '/job-postings') return 'job-postings';
    if (path === '/tracker') return 'candidates';
    if (path === '/scheduling') return 'calendar';
    if (path === '/interview-automation') return 'interview-automation';
    if (path === '/users') return 'users';
    if (path === '/sso') return 'sso';
    if (path === '/twilio-setup') return 'twilio-setup';
    if (path === '/sourcing') return 'sourcing';
    if (path === '/onboarding') return 'onboarding';
    if (path === '/super-admin') return 'super-admin';
    if (path === '/settings') return 'settings';
    if (path === '/reports') return 'reports';
    if (path === '/dashboard') return 'dashboard';
    if (path === '/subscription') return 'subscription';
    return 'dashboard';
  };

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
      <Sidebar 
        currentPage={getCurrentPage()} 
        onNavigate={handleNavigation} 
      />
      <div className="flex-1 overflow-hidden">
        <main id="main-content" tabIndex={-1} role="main" aria-label="Main content">
          <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/resume-screening" element={<EnhancedResumeScreening />} />
          <Route path="/candidate-search" element={<TileJobSearch />} />
          <Route path="/job-postings" element={<EnhancedJobPostings />} />
          <Route path="/job-postings/:id/collaborate" element={<JobPostingCollaboration />} />
          <Route path="/scheduling" element={<InterviewScheduling />} />
          <Route path="/interview-automation" element={<InterviewAutomation />} />
          <Route path="/tracker" element={<CandidateTracker />} />
          <Route path="/sourcing" element={<div className="container mx-auto px-4 py-8"><h1 className="text-2xl font-bold">Job Sourcing - Coming Soon</h1></div>} />
          <Route path="/users" element={<UserManagement />} />
          <Route path="/sso" element={<SSOSetup />} />
          <Route path="/twilio-setup" element={<TwilioSetupPage />} />
          <Route path="/onboarding" element={<div className="container mx-auto px-4 py-8"><h1 className="text-2xl font-bold">Onboarding - Coming Soon</h1></div>} />
          <Route path="/super-admin" element={<SuperAdminPage />} />


          <Route path="/reports" element={<div className="container mx-auto px-4 py-8"><h1 className="text-2xl font-bold">Reports - Coming Soon</h1></div>} />
          <Route path="/subscription" element={<SubscriptionManager />} />
          <Route path="/conversational-ai-demo" element={<ConversationalAIDemo />} />

          <Route path="/settings" element={<div className="container mx-auto px-4 py-8"><h1 className="text-2xl font-bold">Settings - Coming Soon</h1></div>} />
          <Route path="*" element={<NotFound />} />
          </Routes>
        </main>
      </div>
    </div>
  );
}

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <BrowserRouter>
          <AuthProvider>
            <CandidateProvider>
              <AccessibilityProvider>
                <AppContent />
                <Toaster />
                <Sonner />
              </AccessibilityProvider>
            </CandidateProvider>
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
