import { Router } from 'express';
import { db } from '../db';
import { authUsers, organizations } from '@shared/schema';
import { eq, and, sql } from 'drizzle-orm';
import { authenticateToken, AuthenticatedRequest } from '../auth';

const router = Router();

/**
 * @swagger
 * /users:
 *   get:
 *     summary: Get organization users
 *     description: Retrieve all users in the current user's organization (super admins see all users)
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of users
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     format: uuid
 *                   email:
 *                     type: string
 *                     format: email
 *                   fullName:
 *                     type: string
 *                   role:
 *                     type: string
 *                     enum: [admin, member, viewer, super_admin]
 *                   isActive:
 *                     type: boolean
 *                   isApproved:
 *                     type: boolean
 *                   organizationId:
 *                     type: string
 *                     format: uuid
 *                   organizationName:
 *                     type: string
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const currentUser = req.user;
    
    if (!currentUser) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Super admins can see all users across all organizations
    let query;
    if (currentUser.role === 'super_admin') {
      query = db
        .select({
          id: authUsers.id,
          email: authUsers.email,
          fullName: authUsers.fullName,
          role: authUsers.role,
          isActive: authUsers.isActive,
          isApproved: authUsers.isApproved,
          organizationId: authUsers.organizationId,
          organizationName: organizations.name,
          createdAt: authUsers.createdAt,
        })
        .from(authUsers)
        .leftJoin(organizations, eq(authUsers.organizationId, organizations.id))
        .orderBy(authUsers.createdAt);
    } else {
      // Regular users see only users in their organization
      query = db
        .select({
          id: authUsers.id,
          email: authUsers.email,
          fullName: authUsers.fullName,
          role: authUsers.role,
          isActive: authUsers.isActive,
          isApproved: authUsers.isApproved,
          organizationId: authUsers.organizationId,
          organizationName: organizations.name,
          createdAt: authUsers.createdAt,
        })
        .from(authUsers)
        .leftJoin(organizations, eq(authUsers.organizationId, organizations.id))
        .where(eq(authUsers.organizationId, currentUser.organizationId))
        .orderBy(authUsers.createdAt);
    }

    const users = await query;

    // Transform the response to match frontend expectations
    const transformedUsers = users.map(user => ({
      id: user.id,
      email: user.email,
      full_name: user.fullName,
      role: user.role,
      is_active: user.isActive,
      is_approved: user.isApproved,
      organization_id: user.organizationId,
      organization_name: user.organizationName,
      created_at: user.createdAt,
    }));

    res.json(transformedUsers);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Get user statistics
router.get('/stats', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const currentUser = req.user;
    
    if (!currentUser) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Build the where clause based on user role
    const whereClause = currentUser.role === 'super_admin' 
      ? undefined 
      : eq(authUsers.organizationId, currentUser.organizationId);

    // Get basic stats
    const totalUsers = await db
      .select({ count: sql<number>`count(*)` })
      .from(authUsers)
      .where(whereClause);

    const activeUsers = await db
      .select({ count: sql<number>`count(*)` })
      .from(authUsers)
      .where(whereClause ? and(whereClause, eq(authUsers.isActive, true)) : eq(authUsers.isActive, true));

    const pendingApprovals = await db
      .select({ count: sql<number>`count(*)` })
      .from(authUsers)
      .where(whereClause ? and(whereClause, eq(authUsers.isApproved, false)) : eq(authUsers.isApproved, false));

    // Get role-based counts
    const roleStats = await db
      .select({
        role: authUsers.role,
        count: sql<number>`count(*)`
      })
      .from(authUsers)
      .where(whereClause)
      .groupBy(authUsers.role);

    const roleCounts = {
      admins: 0,
      members: 0,
      viewers: 0,
      super_admins: 0
    };

    roleStats.forEach(stat => {
      switch (stat.role) {
        case 'admin':
          roleCounts.admins = Number(stat.count);
          break;
        case 'hr_manager':
        case 'recruiter':
        case 'hiring_manager':
          roleCounts.members = Number(stat.count);
          break;
        case 'super_admin':
          roleCounts.super_admins = Number(stat.count);
          break;
      }
    });

    res.json({
      total_users: Number(totalUsers[0]?.count || 0),
      active_users: Number(activeUsers[0]?.count || 0),
      pending_approvals: Number(pendingApprovals[0]?.count || 0),
      admins: roleCounts.admins,
      members: roleCounts.members,
      viewers: roleCounts.viewers,
      super_admins: roleCounts.super_admins
    });
  } catch (error) {
    console.error('Error fetching user stats:', error);
    res.status(500).json({ error: 'Failed to fetch user statistics' });
  }
});

// Get invitations (admin only) - must come before /:userId routes
router.get('/invitations', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const currentUser = req.user;
    
    if (!currentUser || currentUser.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    // For now, return empty array as invitations feature is not fully implemented
    // This prevents the database error while keeping the UI functional
    res.json([]);
  } catch (error) {
    console.error('Error fetching invitations:', error);
    res.status(500).json({ error: 'Failed to fetch invitations' });
  }
});

// Approve a user (admin only)
router.patch('/:userId/approve', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const currentUser = req.user;
    const { userId } = req.params;
    
    if (!currentUser || currentUser.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    // Update user approval status
    const result = await db
      .update(authUsers)
      .set({ 
        isApproved: true,
        updatedAt: new Date()
      })
      .where(and(
        eq(authUsers.id, userId),
        eq(authUsers.organizationId, currentUser.organizationId)
      ))
      .returning();

    if (!result.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ message: 'User approved successfully' });
  } catch (error) {
    console.error('Error approving user:', error);
    res.status(500).json({ error: 'Failed to approve user' });
  }
});

// Update user status (admin only)
router.patch('/:userId', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const currentUser = req.user;
    const { userId } = req.params;
    const { is_active } = req.body;
    
    if (!currentUser || currentUser.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    // Prevent admin from deactivating themselves
    if (currentUser.id === userId) {
      return res.status(400).json({ error: 'Cannot modify your own account status' });
    }

    // Update user status
    const result = await db
      .update(authUsers)
      .set({ 
        isActive: is_active,
        updatedAt: new Date()
      })
      .where(and(
        eq(authUsers.id, userId),
        eq(authUsers.organizationId, currentUser.organizationId)
      ))
      .returning();

    if (!result.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ message: 'User status updated successfully' });
  } catch (error) {
    console.error('Error updating user status:', error);
    res.status(500).json({ error: 'Failed to update user status' });
  }
});

// Deactivate user (admin only)
router.delete('/:userId', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const currentUser = req.user;
    const { userId } = req.params;
    
    if (!currentUser || currentUser.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }

    // Prevent admin from deactivating themselves
    if (currentUser.id === userId) {
      return res.status(400).json({ error: 'Cannot deactivate your own account' });
    }

    // Deactivate user (we don't actually delete, just set inactive)
    const result = await db
      .update(authUsers)
      .set({ 
        isActive: false,
        updatedAt: new Date()
      })
      .where(and(
        eq(authUsers.id, userId),
        eq(authUsers.organizationId, currentUser.organizationId)
      ))
      .returning();

    if (!result.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ message: 'User deactivated successfully' });
  } catch (error) {
    console.error('Error deactivating user:', error);
    res.status(500).json({ error: 'Failed to deactivate user' });
  }
});

export default router;