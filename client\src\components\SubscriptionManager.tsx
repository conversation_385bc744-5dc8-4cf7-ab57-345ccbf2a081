import { useState, useEffect } from "react";
import { useAuth } from "../contexts/AuthContext";
import { 
  CreditCard, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Crown,
  Star,
  Users,
  Database,
  Shield,
  Clock,
  Calendar
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  billingCycle: 'monthly' | 'yearly';
  features: string[];
  popular?: boolean;
  current?: boolean;
}

interface UserSubscription {
  id: string;
  planId: string;
  status: 'active' | 'cancelled' | 'expired' | 'past_due';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
}

export default function SubscriptionManager() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<string | null>(null);

  const plans: SubscriptionPlan[] = [
    {
      id: 'starter',
      name: 'Starter',
      price: 29,
      billingCycle: 'monthly',
      features: [
        'Up to 50 candidates per month',
        'Basic resume screening',
        'Email notifications',
        'Standard support',
        '1 user account'
      ]
    },
    {
      id: 'professional',
      name: 'Professional',
      price: 99,
      billingCycle: 'monthly',
      popular: true,
      features: [
        'Up to 500 candidates per month',
        'AI-powered resume analysis',
        'Advanced candidate matching',
        'Email & SMS notifications',
        'Calendar integration',
        'Priority support',
        'Up to 5 user accounts',
        'Custom workflows'
      ]
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      price: 299,
      billingCycle: 'monthly',
      features: [
        'Unlimited candidates',
        'Advanced AI analytics',
        'Custom integrations',
        'Dedicated account manager',
        'White-label options',
        'SAML/SSO authentication',
        'Unlimited user accounts',
        'Custom reporting',
        'API access'
      ]
    }
  ];

  useEffect(() => {
    fetchSubscriptionData();
  }, []);

  const fetchSubscriptionData = async () => {
    try {
      const response = await fetch('/api/subscription/current', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setCurrentSubscription(data.subscription);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (planId: string) => {
    setProcessing(planId);
    
    try {
      const response = await fetch('/api/subscription/upgrade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({ planId })
      });

      if (response.ok) {
        const data = await response.json();
        
        if (data.checkoutUrl) {
          // Redirect to Stripe checkout
          window.location.href = data.checkoutUrl;
        } else {
          toast({
            title: "Success",
            description: "Subscription upgraded successfully!"
          });
          fetchSubscriptionData();
        }
      } else {
        throw new Error('Upgrade failed');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to upgrade subscription. Please try again.",
        variant: "destructive"
      });
    } finally {
      setProcessing(null);
    }
  };

  const handleCancelSubscription = async () => {
    if (!currentSubscription) return;
    
    setProcessing('cancel');
    
    try {
      const response = await fetch('/api/subscription/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        toast({
          title: "Subscription Cancelled",
          description: "Your subscription will end at the current billing period."
        });
        fetchSubscriptionData();
      } else {
        throw new Error('Cancellation failed');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to cancel subscription. Please try again.",
        variant: "destructive"
      });
    } finally {
      setProcessing(null);
    }
  };

  const handleReactivateSubscription = async () => {
    if (!currentSubscription) return;
    
    setProcessing('reactivate');
    
    try {
      const response = await fetch('/api/subscription/reactivate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        toast({
          title: "Subscription Reactivated",
          description: "Your subscription has been reactivated successfully."
        });
        fetchSubscriptionData();
      } else {
        throw new Error('Reactivation failed');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reactivate subscription. Please try again.",
        variant: "destructive"
      });
    } finally {
      setProcessing(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'past_due':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      default:
        return <XCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'past_due':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'starter':
        return <Star className="w-5 h-5 text-blue-500" />;
      case 'professional':
        return <Crown className="w-5 h-5 text-purple-500" />;
      case 'enterprise':
        return <Shield className="w-5 h-5 text-gold-500" />;
      default:
        return <Star className="w-5 h-5 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  const currentPlan = plans.find(p => p.id === currentSubscription?.planId) || plans[0];

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Subscription Management</h1>
        <p className="text-gray-600">Manage your HireFlow subscription and billing</p>
      </div>

      {/* Current Subscription Status */}
      {currentSubscription && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getPlanIcon(currentSubscription.planId)}
              Current Subscription
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-lg font-semibold">{currentPlan.name}</h3>
                  <Badge className={getStatusColor(currentSubscription.status)}>
                    {getStatusIcon(currentSubscription.status)}
                    <span className="ml-1 capitalize">{currentSubscription.status}</span>
                  </Badge>
                </div>
                <p className="text-gray-600">${currentPlan.price}/month</p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500">
                  <Calendar className="w-4 h-4 inline mr-1" />
                  Next billing: {new Date(currentSubscription.currentPeriodEnd).toLocaleDateString()}
                </p>
                {currentSubscription.cancelAtPeriodEnd && (
                  <p className="text-sm text-red-600 mt-1">
                    <Clock className="w-4 h-4 inline mr-1" />
                    Ends: {new Date(currentSubscription.currentPeriodEnd).toLocaleDateString()}
                  </p>
                )}
              </div>
            </div>

            <div className="flex gap-3">
              {currentSubscription.cancelAtPeriodEnd ? (
                <Button 
                  onClick={handleReactivateSubscription}
                  disabled={processing === 'reactivate'}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {processing === 'reactivate' ? 'Processing...' : 'Reactivate Subscription'}
                </Button>
              ) : (
                <Button 
                  variant="destructive"
                  onClick={handleCancelSubscription}
                  disabled={processing === 'cancel'}
                >
                  {processing === 'cancel' ? 'Processing...' : 'Cancel Subscription'}
                </Button>
              )}
              <Button variant="outline">
                <CreditCard className="w-4 h-4 mr-2" />
                Update Payment Method
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Available Plans */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Available Plans</h2>
        <div className="grid md:grid-cols-3 gap-6">
          {plans.map((plan) => (
            <Card key={plan.id} className={`relative ${plan.popular ? 'border-purple-500 border-2' : ''}`}>
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-purple-500 text-white">Most Popular</Badge>
                </div>
              )}
              {currentSubscription?.planId === plan.id && (
                <div className="absolute -top-3 right-4">
                  <Badge className="bg-green-500 text-white">Current Plan</Badge>
                </div>
              )}
              
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getPlanIcon(plan.id)}
                  {plan.name}
                </CardTitle>
                <CardDescription>
                  <span className="text-3xl font-bold text-gray-900">${plan.price}</span>
                  <span className="text-gray-600">/{plan.billingCycle}</span>
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                {currentSubscription?.planId === plan.id ? (
                  <Button disabled className="w-full">
                    Current Plan
                  </Button>
                ) : (
                  <Button 
                    onClick={() => handleUpgrade(plan.id)}
                    disabled={processing === plan.id}
                    className="w-full"
                  >
                    {processing === plan.id ? 'Processing...' : 'Upgrade to ' + plan.name}
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Billing Information */}
      <Card>
        <CardHeader>
          <CardTitle>Billing Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span>Organization</span>
              <span className="font-medium">{user?.organization?.name || 'N/A'}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span>Billing Email</span>
              <span className="font-medium">{user?.email}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span>Payment Method</span>
              <div className="flex items-center gap-2">
                <CreditCard className="w-4 h-4" />
                <span className="font-medium">**** **** **** 4242</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}