
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, Brain, Clock, CheckCircle, AlertCircle, FileText, Zap } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useJobPostings } from "@/hooks/useJobPostings";
import { performSimpleAIParsing, performFullResumeParsing } from "@/utils/resumeParsingService";
import CandidateOverview from "./resume/CandidateOverview";

const EnhancedResumeScreeningAgent = () => {
  const [file, setFile] = useState<File | null>(null);
  const [jobDescription, setJobDescription] = useState('');
  const [selectedJobId, setSelectedJobId] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [extractedData, setExtractedData] = useState<any>(null);
  const [processingStep, setProcessingStep] = useState('');
  const [parseMode, setParseMode] = useState<'contact' | 'full'>('contact');
  const { jobPostings, loading: jobsLoading } = useJobPostings();
  const { toast } = useToast();

  // Get selected job description
  const selectedJob = jobPostings.find(job => job.id === selectedJobId);
  const effectiveJobDescription = selectedJob ? 
    `${selectedJob.title}\n\n${selectedJob.description}\n\nRequirements:\n${selectedJob.requirements}` : 
    jobDescription;

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      // File size validation
      if (selectedFile.size > 10 * 1024 * 1024) { // 10MB limit
        toast({
          title: "File too large",
          description: "Please use files smaller than 10MB for faster processing",
          variant: "destructive",
        });
        return;
      }
      
      setFile(selectedFile);
      setExtractedData(null); // Clear previous data
      console.log("File uploaded:", selectedFile.name, "Size:", selectedFile.size);
      toast({
        title: "File uploaded",
        description: `${selectedFile.name} ready for processing`,
      });
    }
  };

  const extractContactInfo = async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please upload a resume first",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setProcessingStep('Preparing file...');
    
    try {
      console.log("Starting fast contact info extraction for:", file.name);
      
      setProcessingStep('Extracting contact information...');
      
      // Parse resume to extract contact information
      const parseResult = await performSimpleAIParsing(file);
      
      if (!parseResult.success) {
        throw new Error("Failed to extract contact information");
      }

      console.log("Contact info extracted successfully:", parseResult.contactInfo);
      
      setProcessingStep('Preparing results...');
      
      // Create a mock analysis result for display
      const mockAnalysisResult = {
        name: parseResult.contactInfo.name || 'Name not found',
        email: parseResult.contactInfo.email || 'Email not found',
        phone: parseResult.contactInfo.phone || 'Phone not found',
        location: parseResult.contactInfo.location || 'Location not found',
        linkedin: parseResult.contactInfo.linkedin || '',
        experience_years: 0,
        match_score: 0,
        education: 'Run full analysis to extract education details',
        overall_score: 0,
        strengths: [],
        concerns: [],
        skill_analysis: {
          matched_skills: [],
          missing_skills: [],
          transferable_skills: []
        },
        experience_analysis: {
          relevant_experience: '',
          experience_gap: '',
          career_progression: ''
        },
        recommendation: 'PENDING',
        detailed_feedback: 'Contact information extracted successfully. Run full analysis for complete evaluation.',
        interview_questions: [],
        parseMode: 'contact'
      };

      setExtractedData(mockAnalysisResult);
      
      toast({
        title: "Contact information extracted",
        description: `Found details for ${parseResult.contactInfo.name || 'candidate'}`,
      });

    } catch (error) {
      console.error("Error extracting contact info:", error);
      toast({
        title: "Extraction failed",
        description: error instanceof Error ? error.message : "Failed to extract contact information",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setProcessingStep('');
    }
  };

  const performFullAnalysis = async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please upload a resume first",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setProcessingStep('Starting comprehensive analysis...');
    
    try {
      console.log("Starting full resume parsing for:", file.name);
      
      setProcessingStep('Extracting all resume information...');
      
      // Parse resume to extract all information
      const parseResult = await performFullResumeParsing(file);
      
      if (!parseResult.success) {
        throw new Error("Failed to parse resume");
      }

      console.log("Full resume parsed successfully:", parseResult);
      
      setProcessingStep('Analyzing experience and skills...');
      
      // Calculate years of experience
      const experienceYears = parseResult.experienceInfo.workExperience.length > 0 ? 
        parseResult.experienceInfo.workExperience.length * 3 : 0; // Rough estimate
      
      // Create a comprehensive analysis result
      const fullAnalysisResult = {
        name: parseResult.contactInfo.name || 'Name not found',
        email: parseResult.contactInfo.email || 'Email not found', 
        phone: parseResult.contactInfo.phone || 'Phone not found',
        location: parseResult.contactInfo.location || 'Location not found',
        linkedin: parseResult.contactInfo.linkedin || '',
        experience_years: experienceYears,
        match_score: effectiveJobDescription ? 75 : 0, // Placeholder score
        education: parseResult.experienceInfo.education.map(edu => 
          `${edu.degree} from ${edu.institution} (${edu.year})`
        ).join('; ') || 'No education information found',
        overall_score: effectiveJobDescription ? 75 : 0,
        strengths: parseResult.experienceInfo.skills.slice(0, 5).map(skill => 
          `Strong skills in ${skill}`
        ),
        concerns: [],
        skill_analysis: {
          matched_skills: parseResult.experienceInfo.skills,
          missing_skills: [],
          transferable_skills: []
        },
        experience_analysis: {
          relevant_experience: parseResult.experienceInfo.summary || 
            `${parseResult.experienceInfo.workExperience.length} relevant positions found`,
          experience_gap: '',
          career_progression: parseResult.experienceInfo.workExperience.map(exp => 
            `${exp.title} at ${exp.company}`
          ).join(' → ')
        },
        recommendation: effectiveJobDescription ? 'RECOMMENDED' : 'NEEDS_JOB_DESCRIPTION',
        detailed_feedback: 'Full resume analysis completed successfully.',
        interview_questions: effectiveJobDescription ? [
          'Tell me about your experience as ' + (parseResult.experienceInfo.workExperience[0]?.title || 'a professional'),
          'What are your key achievements in ' + (parseResult.experienceInfo.skills[0] || 'your field') + '?',
          'How do you see yourself contributing to our team?'
        ] : [],
        parseMode: 'full',
        // Store full parsed data
        fullParseData: parseResult
      };

      setExtractedData(fullAnalysisResult);
      
      toast({
        title: "Full analysis completed",
        description: `Extracted ${parseResult.experienceInfo.workExperience.length} work experiences and ${parseResult.experienceInfo.skills.length} skills`,
      });

    } catch (error) {
      console.error("Error in full analysis:", error);
      toast({
        title: "Analysis failed", 
        description: error instanceof Error ? error.message : "Failed to analyze resume",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setProcessingStep('');
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Upload className="w-5 h-5 mr-2" />
            AI-Powered Resume Parser
          </CardTitle>
          <CardDescription>
            Upload a resume to extract information using OpenAI's advanced Vision API.
            Supports PDF, Word documents, and text files.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
            <input
              type="file"
              accept=".pdf,.doc,.docx,.txt"
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload"
            />
            <label htmlFor="file-upload" className="cursor-pointer">
              <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <p className="text-lg font-medium text-gray-700">
                {file ? file.name : "Drop your resume here or click to browse"}
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Supports PDF, Word documents, and text files (max 10MB)
              </p>
              {file && (
                <div className="mt-2 flex items-center justify-center text-sm text-green-600">
                  <CheckCircle className="w-4 h-4 mr-1" />
                  File ready for processing
                </div>
              )}
            </label>
          </div>

          {/* Processing Mode Selection */}
          <div className="grid grid-cols-2 gap-4">
            <Card 
              className={`cursor-pointer transition-all ${parseMode === 'contact' ? 'border-blue-500 bg-blue-50' : 'hover:border-gray-400'}`}
              onClick={() => setParseMode('contact')}
            >
              <CardContent className="p-4">
                <div className="flex items-center mb-2">
                  <Zap className="w-5 h-5 mr-2 text-blue-600" />
                  <h3 className="font-semibold">Quick Contact Extraction</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Extract only contact information (name, email, phone, location, LinkedIn) in under 15 seconds.
                </p>
              </CardContent>
            </Card>

            <Card 
              className={`cursor-pointer transition-all ${parseMode === 'full' ? 'border-blue-500 bg-blue-50' : 'hover:border-gray-400'}`}
              onClick={() => setParseMode('full')}
            >
              <CardContent className="p-4">
                <div className="flex items-center mb-2">
                  <FileText className="w-5 h-5 mr-2 text-blue-600" />
                  <h3 className="font-semibold">Full Resume Analysis</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Extract all information including experience, education, skills, and summary (30-60 seconds).
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Processing Status */}
          {isProcessing && (
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2 animate-spin text-blue-600" />
                <span className="text-blue-800 font-medium">{processingStep}</span>
              </div>
              <div className="mt-2 text-sm text-blue-600">
                {parseMode === 'contact' ? 
                  'Processing time: Usually under 15 seconds' : 
                  'Processing time: 30-60 seconds for comprehensive analysis'}
              </div>
            </div>
          )}

          <Button 
            onClick={parseMode === 'contact' ? extractContactInfo : performFullAnalysis} 
            disabled={!file || isProcessing}
            className="w-full"
          >
            {isProcessing ? (
              <>
                <Clock className="w-4 h-4 mr-2 animate-spin" />
                {processingStep || 'Processing...'}
              </>
            ) : (
              <>
                <Brain className="w-4 h-4 mr-2" />
                {parseMode === 'contact' ? 
                  'Extract Contact Information (Fast)' : 
                  'Perform Full Resume Analysis'}
              </>
            )}
          </Button>

          {/* Tips */}
          <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
            <div className="flex items-start">
              <AlertCircle className="w-4 h-4 mr-2 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <strong>Tips for best results:</strong>
                <ul className="mt-1 space-y-1">
                  <li>• Use clear, well-formatted resumes</li>
                  <li>• PDF files provide the best accuracy</li>
                  <li>• For scanned documents, ensure text is readable</li>
                  <li>• Complex layouts may require full analysis mode</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Job Description Section (Optional) */}
      {extractedData && (
        <Card>
          <CardHeader>
            <CardTitle>Optional: Add Job Description for Matching</CardTitle>
            <CardDescription>
              Select a job posting or enter a job description to analyze candidate fit.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Select value={selectedJobId} onValueChange={setSelectedJobId}>
              <SelectTrigger>
                <SelectValue placeholder="Choose from existing job postings" />
              </SelectTrigger>
              <SelectContent>
                {jobsLoading ? (
                  <SelectItem value="loading" disabled>Loading job postings...</SelectItem>
                ) : (
                  jobPostings.map((job) => (
                    <SelectItem key={job.id} value={job.id}>
                      {job.title} - {job.department}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
            
            <div className="text-center text-sm text-gray-500">or</div>
            
            <Textarea
              placeholder="Paste the job description here..."
              value={jobDescription}
              onChange={(e) => setJobDescription(e.target.value)}
              rows={4}
              className="resize-none"
            />
          </CardContent>
        </Card>
      )}

      {/* Results Display */}
      {extractedData && (
        <>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="w-5 h-5 mr-2 text-green-600" />
                {extractedData.parseMode === 'contact' ? 'Contact Information Extracted' : 'Full Resume Analysis Complete'}
              </CardTitle>
              <CardDescription>
                {extractedData.parseMode === 'contact' ? 
                  'Successfully extracted contact details from the resume' : 
                  'Comprehensive analysis of experience, skills, and qualifications'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CandidateOverview analysisResult={extractedData} />
            </CardContent>
          </Card>

          {/* Show parsed data for full analysis */}
          {extractedData.parseMode === 'full' && extractedData.fullParseData && (
            <Card>
              <CardHeader>
                <CardTitle>Detailed Resume Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Work Experience */}
                {extractedData.fullParseData.experienceInfo.workExperience.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Work Experience</h3>
                    <div className="space-y-3">
                      {extractedData.fullParseData.experienceInfo.workExperience.map((exp, idx) => (
                        <div key={idx} className="border-l-2 border-gray-200 pl-4">
                          <h4 className="font-medium">{exp.title}</h4>
                          <p className="text-sm text-gray-600">{exp.company} • {exp.duration}</p>
                          {exp.description && (
                            <p className="text-sm mt-1">{exp.description}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Education */}
                {extractedData.fullParseData.experienceInfo.education.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Education</h3>
                    <div className="space-y-2">
                      {extractedData.fullParseData.experienceInfo.education.map((edu, idx) => (
                        <div key={idx}>
                          <p className="font-medium">{edu.degree}</p>
                          <p className="text-sm text-gray-600">{edu.institution} • {edu.year}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Skills */}
                {extractedData.fullParseData.experienceInfo.skills.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Skills</h3>
                    <div className="flex flex-wrap gap-2">
                      {extractedData.fullParseData.experienceInfo.skills.map((skill, idx) => (
                        <span key={idx} className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Summary */}
                {extractedData.fullParseData.experienceInfo.summary && (
                  <div>
                    <h3 className="font-semibold mb-2">Professional Summary</h3>
                    <p className="text-sm">{extractedData.fullParseData.experienceInfo.summary}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
};

export default EnhancedResumeScreeningAgent;
