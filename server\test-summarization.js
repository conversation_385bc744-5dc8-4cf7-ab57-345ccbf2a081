// Quick script to manually trigger call summarization
import { processCallSummaryAsync } from './services/conversationSummarizationService.js';

const callId = '2fecc2eb-55ae-4032-b105-96c387504e9d';
const organizationId = '74cbd810-0e90-4a59-ab9e-36c3d32e4ca3';
const transcript = `Agent: Hello, this is <PERSON> from Acme Corp. How are you today? Candidate: <PERSON> <PERSON>, I'm doing well thank you. Agent: Great! I'm calling regarding your application for our Senior Developer position. Do you have a few minutes to discuss your experience? Candidate: Absolutely, I have time now. Agent: Perfect. Can you tell me about your experience with React and Node.js? Candidate: I have 5 years of experience with React, building large-scale applications. I've also worked extensively with Node.js for backend development. Agent: That sounds excellent. What's your availability for an interview next week? Candidate: I'm available Tuesday afternoon or Wednesday morning. Agent: Great! Let's schedule for Tuesday at 2 PM. I'll send you a calendar invite. Candidate: Perfect, I'll be ready. Thank you! Agent: Thank you for your time. Have a great day!`;

console.log('🚀 Manually triggering call summarization...');
console.log('Call ID:', callId);
console.log('Organization ID:', organizationId);
console.log('Transcript length:', transcript.length, 'characters');

processCallSummaryAsync(callId, organizationId, transcript);

console.log('✅ Summarization process triggered!');