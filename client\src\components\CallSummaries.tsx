import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useQuery } from '@tanstack/react-query';
import { 
  MessageSquare, 
  Calendar, 
  Clock, 
  AlertTriangle, 
  HelpCircle, 
  CheckCircle,
  Bot,
  Eye,
  Activity
} from 'lucide-react';

interface CallSummary {
  id: string;
  callId: string;
  summary: string;
  nextActions: Record<string, {
    description: string;
    due_time?: string | null;
    assignee?: string | null;
    metadata?: Record<string, any> | null;
  }>;
  scheduled: boolean;
  scheduledTime: string | null;
  scheduledEvidence: string | null;
  openQuestions: string[];
  riskFlags: string[];
  generatedAt: string;
  modelUsed: string;
  // Joined call data
  candidateId: string;
  phoneNumber: string;
  callPurpose: string;
  startedAt: string;
  endedAt: string;
  durationSeconds: number;
}

interface CallSummariesProps {
  candidateId: string;
  organizationId: string;
}

export default function CallSummaries({ candidateId, organizationId }: CallSummariesProps) {
  const [selectedSummary, setSelectedSummary] = useState<CallSummary | null>(null);
  const [showSummaryDetails, setShowSummaryDetails] = useState(false);

  // Fetch call summaries for the organization (filtered by candidate on frontend)
  const { data: summariesResponse, isLoading } = useQuery({
    queryKey: ['/api/call-summaries/organization', organizationId],
    queryFn: async () => {
      const response = await fetch(`/api/call-summaries/organization/${organizationId}`, {
        credentials: 'include'
      });
      if (!response.ok) throw new Error('Failed to fetch call summaries');
      return response.json();
    }
  });

  const summaries: CallSummary[] = summariesResponse?.summaries?.filter((summary: CallSummary) => 
    summary.candidateId === candidateId
  ) || [];

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getNextActionsBadgeColor = (count: number) => {
    if (count === 0) return 'bg-gray-100 text-gray-800';
    if (count <= 2) return 'bg-blue-100 text-blue-800';
    if (count <= 4) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getRiskFlagsBadgeColor = (count: number) => {
    if (count === 0) return 'bg-green-100 text-green-800';
    if (count <= 2) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  if (isLoading) {
    return (
      <Card className="bg-white border border-gray-200 rounded-lg shadow-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Bot className="w-5 h-5 text-blue-600" />
            AI Call Summaries
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <Activity className="w-6 h-6 animate-spin text-gray-400 mr-2" />
            <span className="text-gray-500">Loading call summaries...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white border border-gray-200 rounded-lg shadow-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Bot className="w-5 h-5 text-blue-600" />
          AI Call Summaries
          {summaries.length > 0 && (
            <Badge variant="secondary" className="ml-2">
              {summaries.length}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {summaries.length === 0 ? (
          <Alert>
            <MessageSquare className="w-4 h-4" />
            <AlertDescription>
              No AI call summaries available yet. Summaries are generated automatically after completed voice calls.
            </AlertDescription>
          </Alert>
        ) : (
          <div className="space-y-3">
            {summaries.map((summary) => (
              <div 
                key={summary.id} 
                className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer"
                onClick={() => {
                  setSelectedSummary(summary);
                  setShowSummaryDetails(true);
                }}
                data-testid={`summary-card-${summary.id}`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-gray-900">
                        {formatDate(summary.startedAt)} 
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {formatDuration(summary.durationSeconds)}
                      </Badge>
                      {summary.scheduled && (
                        <Badge className="bg-green-100 text-green-800 text-xs">
                          <Calendar className="w-3 h-3 mr-1" />
                          Meeting Scheduled
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 line-clamp-2 mb-2">
                      {summary.summary}
                    </p>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    data-testid={`view-summary-${summary.id}`}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                </div>

                <div className="flex items-center gap-2 flex-wrap">
                  <Badge 
                    className={`text-xs ${getNextActionsBadgeColor(Object.keys(summary.nextActions || {}).length)}`}
                    data-testid={`next-actions-badge-${summary.id}`}
                  >
                    {Object.keys(summary.nextActions || {}).length} Actions
                  </Badge>
                  
                  {(summary.openQuestions?.length || 0) > 0 && (
                    <Badge className="bg-blue-100 text-blue-800 text-xs">
                      <HelpCircle className="w-3 h-3 mr-1" />
                      {summary.openQuestions?.length || 0} Questions
                    </Badge>
                  )}
                  
                  {(summary.riskFlags?.length || 0) > 0 && (
                    <Badge 
                      className={`text-xs ${getRiskFlagsBadgeColor(summary.riskFlags?.length || 0)}`}
                    >
                      <AlertTriangle className="w-3 h-3 mr-1" />
                      {summary.riskFlags?.length || 0} Risks
                    </Badge>
                  )}
                  
                  <Badge variant="outline" className="text-xs">
                    {summary.modelUsed}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Detailed Summary Modal */}
      <Dialog open={showSummaryDetails} onOpenChange={setShowSummaryDetails}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto" data-testid="summary-details-modal">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Bot className="w-5 h-5 text-blue-600" />
              AI Call Summary - {selectedSummary && formatDate(selectedSummary.startedAt)}
            </DialogTitle>
          </DialogHeader>
          
          {selectedSummary && (
            <div className="space-y-6">
              {/* Summary */}
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Call Summary</h3>
                <p className="text-gray-700 leading-relaxed">{selectedSummary.summary}</p>
              </div>

              {/* Scheduled Meeting */}
              {selectedSummary.scheduled && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <h3 className="font-medium text-green-900">Meeting Scheduled</h3>
                  </div>
                  {selectedSummary.scheduledTime && (
                    <p className="text-green-800 mb-1">
                      <Clock className="w-4 h-4 inline mr-1" />
                      {formatDate(selectedSummary.scheduledTime)}
                    </p>
                  )}
                  {selectedSummary.scheduledEvidence && (
                    <p className="text-sm text-green-700 italic">
                      "{selectedSummary.scheduledEvidence}"
                    </p>
                  )}
                </div>
              )}

              {/* Next Actions */}
              {Object.keys(selectedSummary.nextActions || {}).length > 0 && (
                <div>
                  <h3 className="font-medium text-gray-900 mb-3">Next Actions</h3>
                  <div className="space-y-3">
                    {Object.entries(selectedSummary.nextActions || {}).map(([actionCode, action]) => (
                      <div key={actionCode} className="border border-gray-200 rounded-lg p-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className="font-medium text-gray-900 mb-1">{action.description}</p>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <code className="bg-gray-100 px-2 py-1 rounded text-xs">{actionCode}</code>
                              {action.assignee && (
                                <Badge variant="outline" className="text-xs">
                                  {action.assignee}
                                </Badge>
                              )}
                            </div>
                          </div>
                          {action.due_time && (
                            <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                              <Clock className="w-3 h-3 mr-1" />
                              {formatDate(action.due_time)}
                            </Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Open Questions */}
              {(selectedSummary.openQuestions?.length || 0) > 0 && (
                <div>
                  <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                    <HelpCircle className="w-4 h-4 text-blue-600" />
                    Open Questions
                  </h3>
                  <div className="space-y-2">
                    {(selectedSummary.openQuestions || []).map((question, index) => (
                      <div key={index} className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <p className="text-blue-900">{question}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Risk Flags */}
              {(selectedSummary.riskFlags?.length || 0) > 0 && (
                <div>
                  <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                    <AlertTriangle className="w-4 h-4 text-red-600" />
                    Risk Flags
                  </h3>
                  <div className="space-y-2">
                    {(selectedSummary.riskFlags || []).map((risk, index) => (
                      <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-3">
                        <p className="text-red-900">{risk}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Call Metadata */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-2">Call Details</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Duration:</span> {formatDuration(selectedSummary.durationSeconds)}
                  </div>
                  <div>
                    <span className="text-gray-600">Phone:</span> {selectedSummary.phoneNumber}
                  </div>
                  <div>
                    <span className="text-gray-600">Purpose:</span> {selectedSummary.callPurpose}
                  </div>
                  <div>
                    <span className="text-gray-600">AI Model:</span> {selectedSummary.modelUsed}
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
}