<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test Client</title>
</head>
<body>
    <h1>WebSocket Connectivity Test</h1>
    <div id="status"></div>
    <div id="logs"></div>

    <script>
        const status = document.getElementById('status');
        const logs = document.getElementById('logs');

        function log(message) {
            logs.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }

        function testWebSocket(url, name) {
            log(`Testing ${name} - connecting to ${url}`);
            
            const ws = new WebSocket(url);
            
            ws.onopen = function() {
                log(`✅ ${name} - Connection opened successfully!`);
                ws.send(JSON.stringify({ event: 'test', message: 'Hello from test client' }));
            };
            
            ws.onmessage = function(event) {
                log(`📨 ${name} - Received: ${event.data}`);
            };
            
            ws.onclose = function(event) {
                log(`❌ ${name} - Connection closed: ${event.code} ${event.reason}`);
            };
            
            ws.onerror = function(error) {
                log(`❌ ${name} - Error: ${error.message || 'WebSocket error'}`);
            };
            
            // Close after 10 seconds
            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    log(`${name} - Closing connection after test`);
                    ws.close();
                }
            }, 10000);
        }

        // Test both WebSocket endpoints
        const host = window.location.host;
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        
        // Test the test WebSocket
        testWebSocket(`${protocol}//${host}/test-websocket`, 'Test WebSocket');
        
        // Wait 2 seconds, then test ElevenLabs WebSocket
        setTimeout(() => {
            testWebSocket(`${protocol}//${host}/elevenlabs-stream`, 'ElevenLabs WebSocket');
        }, 2000);

    </script>
</body>
</html>