🚀🚀🚀 START MESSAGE RECEIVED - This should trigger DB fetch!
🚀 Full start message: {
  "event": "start",
  "sequenceNumber": "1",
  "start": {
    "accountSid": "AC19aeb4eeca5b65a6f0ed19a1b9cd1054",
    "streamSid": "MZd943d0fa83dd5c852c416cc6ccef61b2",
    "callSid": "CA520ae7fa2334c4d7352b254aedc03638",
    "tracks": [
      "inbound"
    ],
    "mediaFormat": {
      "encoding": "audio/x-mulaw",
      "sampleRate": 8000,
      "channels": 1
    },
    "customParameters": {
      "job_title": "Senior Director - ERP",
      "call_id": "CA520ae7fa2334c4d7352b254aedc03638",
      "agent_id": "agent_8901k3kvyy39fyt9jhfq6fhjtxrk"
    }
  },
  "streamSid": "MZd943d0fa83dd5c852c416cc6ccef61b2"
}
🔍 SIMPLIFIED DEBUG - Looking for job_title parameter:
  All parameters: [ 'job_title', 'call_id', 'agent_id' ]
  job_title value: Senior Director - ERP
  job_title type: string
✅ FOUND job_title parameter: Senior Director - ERP
📍 SIMPLIFIED TEST - Skipping database fetch, using job_title from parameter
📍 SIMPLIFIED FINAL CONTEXT: {
  candidateName: 'Candidate',
  jobTitle: 'Senior Director - ERP',
  companyName: 'Our Company',
  callPurpose: 'screening'
}
📤 SIMPLIFIED ElevenLabs Context: { job_title: 'Senior Director - ERP' }
🚀 ATTEMPTING ELEVENLABS CONNECTION
🔍 DEBUG: Connection attempt started
🔍 DEBUG: Current call context: {
  callSid: 'CA520ae7fa2334c4d7352b254aedc03638',
  candidateName: 'Candidate',
  jobTitle: 'Senior Director - ERP',
  companyName: 'Our Company'
}
🚀 ATTEMPTING ELEVENLABS CONNECTION
🔍 DEBUG: Connection attempt started
🔍 DEBUG: Current call context: {
  callSid: 'CA520ae7fa2334c4d7352b254aedc03638',
  candidateName: 'Candidate',
  jobTitle: 'Senior Director - ERP',
  companyName: 'Our Company'
}
🔗 ELEVENLABS WEBSOCKET OPENED!
🎯 ELEVENLABS EVENT: conversation_initiation_metadata
📋 CONVERSATION METADATA: {
  "conversation_initiation_metadata_event": {
    "conversation_id": "conv_1001k4fp8ctseq2av7e9c9rh9drt",
    "agent_output_audio_format": "ulaw_8000",
    "user_input_audio_format": "pcm_16000"
  },
  "type": "conversation_initiation_metadata"
}