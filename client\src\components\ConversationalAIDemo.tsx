import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Phone, Mic, Volume2, User, Bot } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ConversationResponse {
  success: boolean;
  input: string;
  personality: string;
  response: string;
  nextAction: string;
  hasAudio: boolean;
  conversationStage: string;
  features: {
    elevenlabsConfigured: boolean;
    voiceSynthesis: string;
    aiModel: string;
    personalitySupport: boolean;
  };
}

const ConversationalAIDemo: React.FC = () => {
  const [testMessage, setTestMessage] = useState("Hello, I'm very interested in the software engineer position. Could you tell me more about it?");
  const [personality, setPersonality] = useState('friendly');
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<ConversationResponse | null>(null);
  const [conversation, setConversation] = useState<Array<{type: 'user' | 'ai', message: string}>>([]);
  const { toast } = useToast();

  const personalities = [
    { value: 'friendly', label: 'Friendly Sarah', description: 'Warm and welcoming tone' },
    { value: 'professional', label: 'Professional Rachel', description: 'Clear and business-focused' },
    { value: 'warm', label: 'Warm Grace', description: 'Empathetic and encouraging' },
    { value: 'authoritative', label: 'Authoritative Domi', description: 'Confident and direct' }
  ];

  const testConversationalAI = async () => {
    if (!testMessage.trim()) {
      toast({
        title: "Error",
        description: "Please enter a test message",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/voice-agent/test-conversational-ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          message: testMessage,
          personality
        })
      });

      const data = await response.json();
      
      if (data.success) {
        setResponse(data);
        setConversation(prev => [
          ...prev,
          { type: 'user', message: testMessage },
          { type: 'ai', message: data.response }
        ]);
        
        toast({
          title: "AI Response Generated",
          description: `Stage: ${data.conversationStage} | Next: ${data.nextAction}`,
        });
      } else {
        toast({
          title: "Test Failed",
          description: data.error || "Unknown error occurred",
          variant: "destructive"
        });
      }
    } catch (error: any) {
      toast({
        title: "Connection Error",
        description: "Could not connect to AI service",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const clearConversation = () => {
    setConversation([]);
    setResponse(null);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-6 w-6 text-blue-600" />
            ElevenLabs Conversational AI Demo
          </CardTitle>
          <CardDescription>
            Test the AI-powered voice agent with different personalities and conversation scenarios
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Personality Selection */}
          <div className="space-y-2">
            <Label htmlFor="personality">AI Personality</Label>
            <Select value={personality} onValueChange={setPersonality}>
              <SelectTrigger>
                <SelectValue placeholder="Select personality" />
              </SelectTrigger>
              <SelectContent>
                {personalities.map((p) => (
                  <SelectItem key={p.value} value={p.value}>
                    <div>
                      <div className="font-medium">{p.label}</div>
                      <div className="text-sm text-gray-500">{p.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Test Message Input */}
          <div className="space-y-2">
            <Label htmlFor="message">Test Message</Label>
            <Input
              id="message"
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              placeholder="Enter candidate message..."
              className="min-h-[60px]"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  testConversationalAI();
                }
              }}
            />
          </div>

          {/* Actions */}
          <div className="flex gap-3">
            <Button 
              onClick={testConversationalAI} 
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating AI Response...
                </>
              ) : (
                <>
                  <Mic className="mr-2 h-4 w-4" />
                  Test Conversational AI
                </>
              )}
            </Button>
            <Button variant="outline" onClick={clearConversation}>
              Clear Chat
            </Button>
          </div>

          {/* Features Status */}
          {response && (
            <Card className="bg-green-50 border-green-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm text-green-800">AI Features Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex flex-wrap gap-2">
                  <Badge variant={response.features.elevenlabsConfigured ? "default" : "destructive"}>
                    ElevenLabs: {response.features.elevenlabsConfigured ? "✓ Active" : "✗ Not configured"}
                  </Badge>
                  <Badge variant="secondary">
                    Voice: {response.features.voiceSynthesis}
                  </Badge>
                  <Badge variant="secondary">
                    AI: {response.features.aiModel}
                  </Badge>
                  <Badge variant={response.features.personalitySupport ? "default" : "secondary"}>
                    Personalities: {response.features.personalitySupport ? "✓ Supported" : "✗ Limited"}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Conversation Display */}
      {conversation.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5 text-green-600" />
              Conversation Flow
            </CardTitle>
            {response && (
              <div className="flex gap-2">
                <Badge variant="outline">Stage: {response.conversationStage}</Badge>
                <Badge variant="outline">Next: {response.nextAction}</Badge>
                {response.hasAudio && (
                  <Badge variant="default" className="bg-blue-100 text-blue-800">
                    <Volume2 className="h-3 w-3 mr-1" />
                    Audio Ready
                  </Badge>
                )}
              </div>
            )}
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {conversation.map((msg, index) => (
                <div key={index} className={`flex gap-3 ${msg.type === 'ai' ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                    msg.type === 'ai' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {msg.type === 'ai' ? <Bot className="h-4 w-4" /> : <User className="h-4 w-4" />}
                  </div>
                  <div className={`flex-1 max-w-[80%] p-3 rounded-lg ${
                    msg.type === 'ai' 
                      ? 'bg-blue-50 border border-blue-200' 
                      : 'bg-gray-50 border border-gray-200'
                  }`}>
                    <div className="text-sm font-medium mb-1 text-gray-600">
                      {msg.type === 'ai' ? `Sarah (${personality})` : 'Candidate'}
                    </div>
                    <div className="text-gray-800">{msg.message}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Test Scenarios */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Quick Test Scenarios</CardTitle>
          <CardDescription>Try these pre-built conversation starters</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {[
              {
                label: "Initial Interest",
                message: "Hi, I'm very interested in the software engineer position. Can you tell me more about the role?"
              },
              {
                label: "Scheduling Request", 
                message: "Yes, I'd love to schedule an interview. What times work best?"
              },
              {
                label: "Questions About Company",
                message: "I have some questions about the company culture and growth opportunities."
              },
              {
                label: "Availability Discussion",
                message: "I'm available next week for interviews. Tuesday or Wednesday would work well for me."
              }
            ].map((scenario, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => setTestMessage(scenario.message)}
                className="text-left h-auto p-3 justify-start"
              >
                <div>
                  <div className="font-medium text-sm">{scenario.label}</div>
                  <div className="text-xs text-gray-500 mt-1">{scenario.message.substring(0, 50)}...</div>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConversationalAIDemo;