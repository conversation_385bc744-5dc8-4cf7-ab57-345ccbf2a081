
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, FileText, Brain, Clock, Briefcase } from "lucide-react";
import { useJobPostings } from "@/hooks/useJobPostings";

interface ResumeUploadFormProps {
  file: File | null;
  jobDescription: string;
  selectedJobId: string;
  isAnalyzing: boolean;
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onJobDescriptionChange: (description: string) => void;
  onJobSelect: (jobId: string) => void;
  onAnalyze: () => void;
}

const ResumeUploadForm: React.FC<ResumeUploadFormProps> = ({
  file,
  jobDescription,
  selectedJobId,
  isAnalyzing,
  onFileUpload,
  onJobDescriptionChange,
  onJobSelect,
  onAnalyze
}) => {
  const { jobPostings, loading: jobsLoading } = useJobPostings();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Upload className="w-5 h-5 mr-2" />
          Enhanced Resume Analysis with Job Matching
        </CardTitle>
        <CardDescription>
          Upload resumes and compare against specific job postings for detailed AI-powered analysis.
          This agent evaluates candidates against job requirements, identifies skill gaps, and provides 
          hiring recommendations with tailored interview questions.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Job Description Source</label>
          <div className="space-y-3">
            <Select value={selectedJobId} onValueChange={onJobSelect}>
              <SelectTrigger>
                <SelectValue placeholder="Choose from existing job postings" />
              </SelectTrigger>
              <SelectContent>
                {jobsLoading ? (
                  <SelectItem value="loading" disabled>Loading job postings...</SelectItem>
                ) : (
                  jobPostings.map((job) => (
                    <SelectItem key={job.id} value={job.id}>
                      {job.title} - {job.department}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
            
            <div className="text-center text-sm text-gray-500">or</div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Paste Job Description</label>
              <Textarea
                placeholder="Paste the full job description here including requirements, responsibilities, and qualifications..."
                value={jobDescription}
                onChange={(e) => onJobDescriptionChange(e.target.value)}
                rows={6}
                className="resize-none"
              />
            </div>
          </div>
        </div>
        
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
          <input
            type="file"
            accept=".pdf,.doc,.docx"
            onChange={onFileUpload}
            className="hidden"
            id="file-upload"
          />
          <label htmlFor="file-upload" className="cursor-pointer">
            <FileText className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <p className="text-lg font-medium text-gray-700">
              {file ? file.name : "Drop your resume here or click to browse"}
            </p>
            <p className="text-sm text-gray-500 mt-2">Supports PDF and Word documents</p>
          </label>
        </div>

        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h4 className="font-medium text-blue-900 mb-2 flex items-center">
            <Briefcase className="w-4 h-4 mr-2" />
            What This Agent Does
          </h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Analyzes resume content against specific job requirements</li>
            <li>• Calculates job match percentage and overall candidate score</li>
            <li>• Identifies strengths, concerns, and skill gaps</li>
            <li>• Provides hiring recommendations (HIRE/INTERVIEW/REJECT)</li>
            <li>• Generates tailored interview questions based on analysis</li>
            <li>• Evaluates cultural fit and career progression potential</li>
          </ul>
        </div>

        <Button 
          onClick={onAnalyze} 
          disabled={!file || (!selectedJobId && !jobDescription.trim()) || isAnalyzing}
          className="w-full"
        >
          {isAnalyzing ? (
            <>
              <Clock className="w-4 h-4 mr-2 animate-spin" />
              Analyzing Resume Against Job...
            </>
          ) : (
            <>
              <Brain className="w-4 h-4 mr-2" />
              Analyze with Enhanced AI
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};

export default ResumeUploadForm;
