import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { ARIA_LABELS, announceToScreenReader } from '../utils/accessibility';
import OrganizationLookup from '../components/OrganizationLookup';

interface AuthPageProps {
  sessionError?: string | null;
}

export default function AuthPage({ sessionError }: AuthPageProps = {}) {
  const [isLogin, setIsLogin] = useState(true);
  const [isCreateOrg, setIsCreateOrg] = useState(false);
  const { login, register, createOrganization, isLoading, error, isAuthenticated, user } = useAuth();
  const navigate = useNavigate();

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated && user && !isLoading) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, user, isLoading, navigate]);

  const [loginForm, setLoginForm] = useState({
    email: '',
    password: '',
  });

  const [registerForm, setRegisterForm] = useState({
    email: '',
    fullName: '',
    password: '',
    organizationId: '',
  });

  // Handle pre-filled organization ID from URL
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const orgId = urlParams.get('orgId');
    if (orgId) {
      setRegisterForm(prev => ({ ...prev, organizationId: orgId }));
      setIsLogin(false); // Switch to registration form
    }
  }, []);

  const [orgForm, setOrgForm] = useState({
    name: '',
    domain: '',
    adminEmail: '',
    adminName: '',
    adminPassword: '',
  });

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    announceToScreenReader('Signing in, please wait', 'assertive');
    try {
      await login(loginForm.email, loginForm.password);
      announceToScreenReader('Login successful!', 'assertive');
      
      // Navigation will be handled automatically by the App component
      // after the user state is updated
    } catch (error) {
      announceToScreenReader('Login failed. Please check your credentials.', 'assertive');
      console.error('Login failed:', error);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    announceToScreenReader('Creating account, please wait', 'assertive');
    try {
      await register(registerForm);
      announceToScreenReader('Account created successfully!', 'assertive');
    } catch (error) {
      announceToScreenReader('Registration failed. Please try again.', 'assertive');
    }
  };

  const handleCreateOrg = async (e: React.FormEvent) => {
    e.preventDefault();
    announceToScreenReader('Creating organization, please wait', 'assertive');
    try {
      await createOrganization(orgForm);
      announceToScreenReader('Organization created successfully!', 'assertive');
    } catch (error) {
      announceToScreenReader('Organization creation failed. Please try again.', 'assertive');
    }
  };

  if (isCreateOrg) {
    return (
      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden" style={{ backgroundColor: '#A3D3FF' }}>
        {/* Animated background elements */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-white rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-2000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-4000"></div>
        </div>
        
        <div className="max-w-md w-full space-y-8 relative z-10">
          <div className="text-center">
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 backdrop-blur-lg rounded-full mb-4 shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h2 className="text-4xl font-bold text-white mb-2">
                Create Organization
              </h2>
              <p className="text-white/80 text-lg">
                Set up your ATS tenant
              </p>
            </div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-8 shadow-2xl">
            <form className="space-y-6" onSubmit={handleCreateOrg}>
              {/* 🎯 SESSION ERROR BANNER */}
              {sessionError && (
                <div className="bg-red-500/20 backdrop-blur-lg border border-red-300/30 text-red-100 px-4 py-3 rounded-xl" data-testid="status-session-expired">
                  {sessionError}
                </div>
              )}
              
              {error && (
                <div className="bg-red-500/20 backdrop-blur-lg border border-red-300/30 text-red-100 px-4 py-3 rounded-xl">
                  {error}
                </div>
              )}
            
              <div className="space-y-4">
                <div>
                  <label htmlFor="org-name" className="block text-sm font-medium text-white/90 mb-2">
                    Organization Name
                  </label>
                  <input
                    id="org-name"
                    type="text"
                    required
                    className="w-full px-4 py-3 bg-white/20 backdrop-blur-lg border border-white/30 rounded-xl placeholder-white/60 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent transition-all duration-200"
                    placeholder="Enter organization name"
                    value={orgForm.name}
                    onChange={(e) => setOrgForm({...orgForm, name: e.target.value})}
                  />
                </div>
                
                <div>
                  <label htmlFor="org-domain" className="block text-sm font-medium text-white/90 mb-2">
                    Domain (optional)
                  </label>
                  <input
                    id="org-domain"
                    type="text"
                    className="w-full px-4 py-3 bg-white/20 backdrop-blur-lg border border-white/30 rounded-xl placeholder-white/60 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent transition-all duration-200"
                    placeholder="company.com"
                    value={orgForm.domain}
                    onChange={(e) => setOrgForm({...orgForm, domain: e.target.value})}
                  />
                </div>
                
                <div className="border-t border-white/20 pt-4">
                  <h3 className="text-lg font-medium text-white mb-3">Admin User</h3>
                  
                  <div>
                    <label htmlFor="admin-name" className="block text-sm font-medium text-white/90 mb-2">
                      Admin Full Name
                    </label>
                    <input
                      id="admin-name"
                      type="text"
                      required
                      className="w-full px-4 py-3 bg-white/20 backdrop-blur-lg border border-white/30 rounded-xl placeholder-white/60 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent transition-all duration-200"
                      placeholder="Enter admin name"
                      value={orgForm.adminName}
                      onChange={(e) => setOrgForm({...orgForm, adminName: e.target.value})}
                    />
                  </div>
                  
                  <div className="mt-4">
                    <label htmlFor="admin-email" className="block text-sm font-medium text-white/90 mb-2">
                      Admin Email
                    </label>
                    <input
                      id="admin-email"
                      type="email"
                      required
                      className="w-full px-4 py-3 bg-white/20 backdrop-blur-lg border border-white/30 rounded-xl placeholder-white/60 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent transition-all duration-200"
                      placeholder="<EMAIL>"
                      value={orgForm.adminEmail}
                      onChange={(e) => setOrgForm({...orgForm, adminEmail: e.target.value})}
                    />
                  </div>
                  
                  <div className="mt-4">
                    <label htmlFor="admin-password" className="block text-sm font-medium text-white/90 mb-2">
                      Admin Password
                    </label>
                    <input
                      id="admin-password"
                      type="password"
                      required
                      className="w-full px-4 py-3 bg-white/20 backdrop-blur-lg border border-white/30 rounded-xl placeholder-white/60 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent transition-all duration-200"
                      placeholder="Min 8 characters"
                      value={orgForm.adminPassword}
                      onChange={(e) => setOrgForm({...orgForm, adminPassword: e.target.value})}
                    />
                  </div>
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 hover:from-pink-600 hover:via-purple-600 hover:to-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  {isLoading ? 'Creating...' : 'Create Organization'}
                </button>
              </div>

              <div className="text-center">
                <button
                  type="button"
                  onClick={() => setIsCreateOrg(false)}
                  className="text-cyan-300 hover:text-cyan-200 transition-colors"
                >
                  Back to login
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: '#A3D3FF' }}>
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4 shadow-sm">
              <svg className="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-2">
              {isLogin ? 'Welcome Back' : 'Join ATS'}
            </h2>
            <p className="text-gray-600 text-lg">
              {isLogin ? 'Sign in to your account' : 'Create your account'}
            </p>
            
          </div>
        </div>

        {!isLogin && !isCreateOrg && (
          <OrganizationLookup />
        )}
        
        <div className="bg-white border border-gray-200 rounded-2xl p-8 shadow-lg">
          <form className="space-y-6" onSubmit={isLogin ? handleLogin : handleRegister}>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-xl">
                {error}
              </div>
            )}
            
            <div className="space-y-4">
              {!isLogin && (
                <div>
                  <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name
                  </label>
                  <input
                    id="fullName"
                    type="text"
                    required
                    className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="Enter your full name"
                    value={registerForm.fullName}
                    onChange={(e) => setRegisterForm({...registerForm, fullName: e.target.value})}
                  />
                </div>
              )}
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email address
                </label>
                <input
                  id="email"
                  type="email"
                  required
                  className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="Enter your email"
                  value={isLogin ? loginForm.email : registerForm.email}
                  onChange={(e) => {
                    if (isLogin) {
                      setLoginForm({...loginForm, email: e.target.value});
                    } else {
                      setRegisterForm({...registerForm, email: e.target.value});
                    }
                  }}
                />
              </div>
              
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <input
                  id="password"
                  type="password"
                  required
                  className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                  placeholder="Enter your password"
                  value={isLogin ? loginForm.password : registerForm.password}
                  onChange={(e) => {
                    if (isLogin) {
                      setLoginForm({...loginForm, password: e.target.value});
                    } else {
                      setRegisterForm({...registerForm, password: e.target.value});
                    }
                  }}
                />
              </div>
              
              {!isLogin && (
                <div>
                  <label htmlFor="organizationId" className="block text-sm font-medium text-gray-700 mb-2">
                    Organization ID
                  </label>
                  <input
                    id="organizationId"
                    type="text"
                    required
                    className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    placeholder="Contact your admin for org ID"
                    value={registerForm.organizationId}
                    onChange={(e) => setRegisterForm({...registerForm, organizationId: e.target.value})}
                  />
                </div>
              )}
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                {isLoading ? 'Processing...' : (isLogin ? 'Sign in' : 'Create account')}
              </button>
            </div>

            <div className="text-center space-y-2">
              <p className="text-sm text-gray-600">
                {isLogin ? "Don't have an account?" : "Already have an account?"}
                {' '}
                <button
                  type="button"
                  onClick={() => setIsLogin(!isLogin)}
                  className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
                >
                  {isLogin ? 'Sign up' : 'Sign in'}
                </button>
              </p>
              <p className="text-sm text-gray-600">
                Need to create an organization?{' '}
                <button
                  type="button"
                  onClick={() => setIsCreateOrg(true)}
                  className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
                >
                  Create Organization
                </button>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}