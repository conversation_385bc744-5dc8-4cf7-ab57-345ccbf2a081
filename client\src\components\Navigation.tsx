import React from 'react';
import { Button } from '@/components/ui/button';
import {
  FileText,
  Calendar,
  Users,
  UserCheck,
  Home,
  Building2,
  Search,
  LogOut,
  Settings,
  Bot
} from 'lucide-react';
import { useAuth } from "../contexts/AuthContext";

interface NavigationProps {
  currentPage: string;
  onNavigate: (page: string) => void;
}

export default function Navigation({ currentPage, onNavigate }: NavigationProps) {
  const { user, logout } = useAuth();
  const navItems = [
    {
      id: 'screening',
      label: 'AI Resume Screening',
      icon: FileText,
      path: '/'
    },
    {
      id: 'candidate-search',
      label: 'Candidate Search',
      icon: Search,
      path: '/candidate-search'
    },
    {
      id: 'job-postings',
      label: 'Job Postings',
      icon: Building2,
      path: '/job-postings'
    },

    {
      id: 'tracker',
      label: 'Candidate Tracker',
      icon: Users,
      path: '/tracker'
    },
    {
      id: 'scheduling',
      label: 'Scheduling',
      icon: Calendar,
      path: '/scheduling'
    },
    {
      id: 'interview-automation',
      label: 'AI Interviews',
      icon: Bot,
      path: '/interview-automation'
    },
    {
      id: 'users',
      label: 'User Management',
      icon: Users,
      path: '/users'
    },
    {
      id: 'onboarding',
      label: 'Onboarding',
      icon: UserCheck,
      path: '/onboarding'
    }
  ];

  const superAdminNavItems = [
    {
      id: 'super-admin',
      label: 'Platform Management',
      icon: Settings,
      path: '/super-admin'
    },
    {
      id: 'interview-automation',
      label: 'AI Interviews',
      icon: Bot,
      path: '/interview-automation'
    }
  ];

  return (
    <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Home Button */}
          <Button
            variant="ghost"
            onClick={() => onNavigate('/')}
            className="flex items-center gap-2 text-gray-600 hover:text-blue-600"
          >
            <Home className="w-5 h-5" />
            <span className="font-semibold">HR Portal</span>
          </Button>

          {/* Navigation Items */}
          <div className="flex items-center space-x-1">
            {/* Super Admin Navigation */}
            {user?.role === 'super_admin' && superAdminNavItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.id;
              
              return (
                <Button
                  key={item.id}
                  variant={isActive ? "default" : "ghost"}
                  onClick={() => onNavigate(item.path)}
                  className={`flex items-center gap-2 px-4 py-2 ${
                    isActive 
                      ? 'bg-yellow-600 text-white' 
                      : 'text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="hidden sm:inline">{item.label}</span>
                </Button>
              );
            })}

            {/* Regular Navigation (hidden for super admin) */}
            {user?.role !== 'super_admin' && navItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.id;
              
              return (
                <Button
                  key={item.id}
                  variant={isActive ? "default" : "ghost"}
                  onClick={() => onNavigate(item.path)}
                  className={`flex items-center gap-2 px-4 py-2 ${
                    isActive 
                      ? 'bg-blue-600 text-white' 
                      : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="hidden sm:inline">{item.label}</span>
                </Button>
              );
            })}
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            <div className="flex flex-col items-end">
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {user?.fullName || 'User'}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                {user?.role?.replace('_', ' ') || 'Member'}
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={logout}
              className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
            >
              <LogOut className="w-4 h-4" />
              <span className="hidden sm:inline">Sign Out</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}