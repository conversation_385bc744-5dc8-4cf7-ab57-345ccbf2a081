import React from 'react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import candidateImage from '@assets/image_1754509231017.png';

const HomePage = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col items-center bg-[#f0eeee]">
      {/* Navbar */}
      <div className="w-full bg-white/95 backdrop-blur-sm shadow-sm border-b border-gray-100 px-8 py-4 flex items-center justify-between z-10">
        <div className="flex items-center space-x-2">
          <span className="text-2xl font-bold text-gray-900 tracking-tight select-none">Steorra</span>
        </div>
        <div className="flex items-center space-x-8 ml-8">
          <button className="text-gray-600 text-sm font-medium hover:text-gray-900 transition">Product</button>
          <button className="text-gray-600 text-sm font-medium hover:text-gray-900 transition">Pricing</button>
          <button className="text-gray-600 text-sm font-medium hover:text-gray-900 transition">Resources</button>
        </div>
        <div className="flex items-center space-x-4">
          <button 
            onClick={() => navigate('/auth')}
            className="text-gray-600 text-sm font-medium hover:text-gray-900 transition cursor-pointer"
          >
            Login
          </button>
          <Button 
            onClick={() => navigate('/book-demo')}
            className="bg-gray-900 text-white text-sm font-medium px-6 py-2.5 rounded-full hover:bg-gray-800 transition-colors"
          >
            Book a Demo
          </Button>
        </div>
      </div>

      {/* Banner with Animation */}
      <div className="w-[95vw] max-w-xl rounded-[40px] px-8 py-16 flex flex-col items-center justify-center relative mt-16 shadow-2xl animate-fadeInUp bg-[#121111]">
        {/* Animated Voice AI Agent Model */}
        <div className="animate-float mb-8">
          <img 
            alt="Professional HR representative with headset - Voice AI Agent for talent acquisition and candidate screening" 
            className="rounded-full border-4 border-white bg-white shadow-xl" 
            height={120} 
            src="/hr-agent.png" 
            width={120}
          />
        </div>
        <h1 className="text-center text-4xl md:text-5xl font-extrabold leading-tight mb-2 drop-shadow-lg animate-fadeInUpDelayed text-[#f7f0f0] bg-[#91505000]">
          Voice AI Agent for Talent Acquisition
        </h1>
        <p className="text-white/90 text-center text-lg font-medium mt-2 animate-fadeInUpDelayed2">Steorra</p>
        
        {/* Animated sound waves below the avatar */}
        <div className="flex justify-center mt-8 space-x-2 animate-fadeInUpDelayed2">
          <div className="h-6 w-1.5 rounded-full bg-white/80 animate-pulse" style={{animationDelay: '0.1s'}}></div>
          <div className="h-8 w-1.5 rounded-full bg-white/60 animate-pulse" style={{animationDelay: '0.2s'}}></div>
          <div className="h-10 w-1.5 rounded-full bg-white/40 animate-pulse" style={{animationDelay: '0.3s'}}></div>
          <div className="h-8 w-1.5 rounded-full bg-white/60 animate-pulse" style={{animationDelay: '0.4s'}}></div>
          <div className="h-6 w-1.5 rounded-full bg-white/80 animate-pulse" style={{animationDelay: '0.5s'}}></div>
        </div>
      </div>

      {/* Typewriter effect section */}
      <div className="w-full max-w-3xl mt-20 mb-16 px-4 animate-fadeInUpDelayed2">
        <h2 className="text-black text-3xl md:text-4xl font-extrabold text-center mb-6 animate-typewriter">
          Simplify Your Recruitment Automation
        </h2>
        <p className="text-black text-xl text-center font-medium">
          with <span className="font-bold">Steorra Voice AI Agent</span>
        </p>
        
        {/* Get Started Button */}
        <div className="flex justify-center mt-8 animate-fadeInUpDelayed2">
          <Button 
            onClick={() => {
              console.log('Get Started button clicked');
              navigate('/auth');
            }} 
            variant="outline"
            className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-semibold px-8 py-3 rounded-full transition-all duration-300 hover:shadow-lg hover:scale-105 shadow-lg"
          >
            Get Started
          </Button>
        </div>
      </div>

      {/* Enhanced AI Voice Call Animation */}
      <div className="w-full max-w-4xl px-4 animate-fadeInUpDelayed2 font-light text-justify text-[#23252f] mt-[35px] mb-[35px] pl-[166px] pr-[166px] ml-[37px] mr-[37px] pt-[25px] pb-[25px]">
        <h3 className="text-black text-xl font-bold text-center mb-6">Watch Steorra AI in Action</h3>
        
        {/* Phone Call Interface - Soft Beveled Tile Design */}
        <div className="relative rounded-2xl p-8 overflow-hidden ml-[-46px] mr-[-46px] pt-[20px] pb-[20px] pl-[41px] pr-[41px] bg-[#0f0f0f] text-white"
          style={{
            background: '#0f0f0f',
            boxShadow: `
              12px 12px 80px rgba(190, 190, 190, 0.4),
              -12px -12px 80px rgba(255, 255, 255, 0.8),
              inset 1px 1px 3px rgba(255, 255, 255, 0.7),
              inset -1px -1px 3px rgba(0, 0, 0, 0.05)
            `,
            border: '1px solid rgba(255, 255, 255, 0.3)',
            borderRadius: '32px',
            position: 'relative'
          }}>
          
          {/* Animated gradient overlay for video effect */}
          <div className="absolute inset-0 opacity-10 pointer-events-none"
            style={{
              background: 'linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.3) 50%, transparent 70%)',
              animation: 'shimmer 3s infinite'
            }}></div>

          {/* Video Call Header with Recording Indicator */}
          <div className="flex justify-between items-center mb-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl px-6 py-3 border border-white/30"
            style={{
              boxShadow: 'inset 1px 1px 2px rgba(0,0,0,0.03), inset -1px -1px 2px rgba(255,255,255,0.6)'
            }}>
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                <div className="absolute inset-0 w-3 h-3 bg-red-400 rounded-full animate-ping"></div>
              </div>
              <span className="text-sm font-semibold text-gray-800">Live Interview Call</span>
              <div className="flex space-x-1">
                <div className="w-1 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <div className="w-1 h-4 bg-green-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
                <div className="w-1 h-2 bg-green-400 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4 text-gray-600 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 15c1.66 0 3-1.34 3-3V6c0-1.66-1.34-3-3-3S9 4.34 9 6v6c0 1.66 1.34 3 3 3z"/>
                <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
              </svg>
              <span className="text-sm font-medium text-gray-700 animate-pulse">01:47</span>
            </div>
          </div>

          {/* Video Call Participants */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            {/* AI Agent Video Feed */}
            <div className="relative bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl p-4 shadow-inner border border-white/50"
              style={{
                boxShadow: 'inset 1px 1px 3px rgba(0,0,0,0.05), inset -1px -1px 3px rgba(255,255,255,0.8)',
                borderRadius: '20px'
              }}>
              <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full font-medium z-10">
                AI Agent
              </div>
              <div className="flex flex-col items-center justify-center h-32">
                <div className="relative">
                  <img 
                    src="/hr-agent.png" 
                    alt="AI Agent"
                    className="w-20 h-20 rounded-full border-3 border-white shadow-lg animate-float"
                  />
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                    </svg>
                  </div>
                </div>
                <div className="mt-2 flex space-x-1">
                  <div className="h-1 w-8 bg-blue-400 rounded-full animate-pulse"></div>
                  <div className="h-1 w-6 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
                  <div className="h-1 w-4 bg-blue-400 rounded-full animate-pulse" style={{animationDelay: '0.6s'}}></div>
                </div>
              </div>
            </div>

            {/* Candidate Video Feed */}
            <div className="relative bg-gradient-to-br from-green-100 to-emerald-100 rounded-2xl p-4 shadow-inner border border-white/50"
              style={{
                boxShadow: 'inset 1px 1px 3px rgba(0,0,0,0.05), inset -1px -1px 3px rgba(255,255,255,0.8)',
                borderRadius: '20px'
              }}>
              <div className="absolute top-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded-full font-medium z-10">
                Sarah Chen
              </div>
              <div className="flex flex-col items-center justify-center h-32">
                <div className="relative">
                  <img 
                    src={candidateImage} 
                    alt="Sarah Chen"
                    className="w-20 h-20 rounded-full border-3 border-white shadow-lg animate-float object-cover"
                    style={{animationDelay: '1s'}}
                  />
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-blue-500 rounded-full border-2 border-white animate-pulse">
                    <svg className="w-4 h-4 text-white m-auto mt-1" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 15c1.66 0 3-1.34 3-3V6c0-1.66-1.34-3-3-3S9 4.34 9 6v6c0 1.66 1.34 3 3 3z"/>
                    </svg>
                  </div>
                </div>
                <div className="mt-2 flex space-x-1">
                  <div className="h-1 w-4 bg-green-400 rounded-full animate-pulse"></div>
                  <div className="h-1 w-6 bg-green-500 rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
                  <div className="h-1 w-8 bg-green-400 rounded-full animate-pulse" style={{animationDelay: '0.6s'}}></div>
                </div>
              </div>
            </div>
          </div>

          {/* Voice Visualization Bar */}
          <div className="flex items-center justify-center space-x-2 bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 rounded-2xl p-4 pt-[0px] pb-[0px] mt-[24px] mb-[24px] pl-[15px] pr-[15px]"
            style={{
              boxShadow: 'inset 1px 1px 2px rgba(0,0,0,0.03), inset -1px -1px 2px rgba(255,255,255,0.6)',
              borderRadius: '20px'
            }}>
            <div className="h-8 w-1 bg-blue-400 rounded-full animate-pulse" style={{animationDelay: '0.1s'}}></div>
            <div className="h-12 w-1 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
            <div className="h-16 w-1 bg-purple-600 rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
            <div className="h-20 w-1 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            <div className="h-16 w-1 bg-pink-500 rounded-full animate-pulse" style={{animationDelay: '0.5s'}}></div>
            <div className="h-12 w-1 bg-pink-400 rounded-full animate-pulse" style={{animationDelay: '0.6s'}}></div>
            <div className="h-10 w-1 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.7s'}}></div>
            <div className="h-14 w-1 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.8s'}}></div>
            <div className="h-8 w-1 bg-blue-400 rounded-full animate-pulse" style={{animationDelay: '0.9s'}}></div>
          </div>

          {/* Conversation Flow with Enhanced Animations */}
          <div className="space-y-4">
            {/* Message 1 */}
            <div className="flex items-start space-x-3 animate-slideInLeft">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-white text-xs font-bold">AI</span>
              </div>
              <div className="bg-blue-100 border-l-4 border-blue-500 p-3 rounded-lg shadow-md max-w-sm animate-typeText pt-[3px] pb-[3px]">
                <p className="text-gray-800 text-sm font-medium">"Hi Sarah! I'm calling to schedule your interview for the Marketing Manager role."</p>
                <div className="text-xs text-gray-500 mt-1 flex items-center">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  Speaking...
                </div>
              </div>
            </div>

            {/* Message 2 */}
            <div className="flex items-start space-x-3 justify-end animate-slideInRight" style={{animationDelay: '2s'}}>
              <div className="bg-green-100 border-r-4 border-green-500 p-3 rounded-lg shadow-md max-w-sm animate-typeText" style={{animationDelay: '2.5s'}}>
                <p className="text-gray-800 text-sm font-medium">"Perfect! I've been looking forward to this. When would work best?"</p>
                <div className="text-xs text-gray-500 mt-1 flex items-center justify-end">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                  Listening...
                </div>
              </div>
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
              </div>
            </div>

            {/* Message 3 */}
            <div className="flex items-start space-x-3 animate-slideInLeft" style={{animationDelay: '4s'}}>
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-white text-xs font-bold">AI</span>
              </div>
              <div className="bg-blue-100 border-l-4 border-blue-500 p-3 rounded-lg shadow-md max-w-sm animate-typeText" style={{animationDelay: '4.5s'}}>
                <p className="text-gray-800 text-sm font-medium">"Great! Are you available this Thursday at 2 PM or Friday at 10 AM?"</p>
                <div className="text-xs text-gray-500 mt-1 flex items-center">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  Speaking...
                </div>
              </div>
            </div>

            {/* Message 4 */}
            <div className="flex items-start space-x-3 justify-end animate-slideInRight" style={{animationDelay: '6s'}}>
              <div className="bg-green-100 border-r-4 border-green-500 p-3 rounded-lg shadow-md max-w-sm animate-typeText" style={{animationDelay: '6.5s'}}>
                <p className="text-gray-800 text-sm font-medium">"Thursday at 2 PM works perfectly for me!"</p>
                <div className="text-xs text-gray-500 mt-1 flex items-center justify-end">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                  Listening...
                </div>
              </div>
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
              </div>
            </div>

            {/* Message 5 */}
            <div className="flex items-start space-x-3 animate-slideInLeft" style={{animationDelay: '8s'}}>
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-white text-xs font-bold">AI</span>
              </div>
              <div className="bg-blue-100 border-l-4 border-blue-500 p-3 rounded-lg shadow-md max-w-sm animate-typeText" style={{animationDelay: '8.5s'}}>
                <p className="text-gray-800 text-sm font-medium">"Excellent! I've scheduled your interview for Thursday 2 PM. You'll receive a calendar invite shortly."</p>
                <div className="text-xs text-gray-500 mt-1 flex items-center">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  Speaking...
                </div>
              </div>
            </div>

            {/* Calendar Confirmation */}
            <div className="mt-6 p-4 bg-white rounded-lg shadow-lg border animate-fadeInUp" style={{animationDelay: '10s'}}>
              <h4 className="font-semibold text-gray-800 mb-3 flex items-center text-sm">
                <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                Meeting Scheduled Successfully
              </h4>
              <div className="grid grid-cols-2 gap-3 text-xs">
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">Thu 2PM</div>
                  <div className="text-gray-600">Interview Time</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">45min</div>
                  <div className="text-gray-600">Duration</div>
                </div>
              </div>
              <div className="mt-2 text-center">
                <span className="text-xs text-gray-500">📧 Calendar invite sent automatically</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features section with animations */}
      <div className="w-full max-w-5xl mt-16 mb-16 px-4">
        <h3 className="text-black text-3xl font-bold text-center mb-12">Key Features</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div 
            className="bg-[#f79c9c] p-6 rounded-lg transform hover:scale-105 transition-all duration-300"
            style={{
              background: '#f79c9c',
              boxShadow: `
                8px 8px 16px rgba(200, 140, 140, 0.3),
                -8px -8px 16px rgba(255, 220, 220, 0.8),
                inset 2px 2px 4px rgba(255, 255, 255, 0.5),
                inset -2px -2px 4px rgba(0, 0, 0, 0.08)
              `,
              border: '1px solid rgba(255, 255, 255, 0.4)',
              borderRadius: '16px',
              transform: 'perspective(1000px) rotateX(2deg)',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'perspective(1000px) rotateX(0deg) scale(1.05)';
              e.currentTarget.style.boxShadow = `
                12px 12px 24px rgba(200, 140, 140, 0.4),
                -12px -12px 24px rgba(255, 220, 220, 0.9),
                inset 2px 2px 4px rgba(255, 255, 255, 0.6),
                inset -2px -2px 4px rgba(0, 0, 0, 0.1)
              `;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'perspective(1000px) rotateX(2deg)';
              e.currentTarget.style.boxShadow = `
                8px 8px 16px rgba(200, 140, 140, 0.3),
                -8px -8px 16px rgba(255, 220, 220, 0.8),
                inset 2px 2px 4px rgba(255, 255, 255, 0.5),
                inset -2px -2px 4px rgba(0, 0, 0, 0.08)
              `;
            }}
          >
            <svg className="w-12 h-12 text-black mb-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            <h4 className="text-xl font-semibold mb-2">AI-Powered Conversations</h4>
            <p className="text-gray-600">Engage candidates with natural, intelligent dialogues.</p>
          </div>
          <div 
            className="bg-[#f79c9c] p-6 rounded-lg transform hover:scale-105 transition-all duration-300"
            style={{
              background: '#f79c9c',
              boxShadow: `
                8px 8px 16px rgba(200, 140, 140, 0.3),
                -8px -8px 16px rgba(255, 220, 220, 0.8),
                inset 2px 2px 4px rgba(255, 255, 255, 0.5),
                inset -2px -2px 4px rgba(0, 0, 0, 0.08)
              `,
              border: '1px solid rgba(255, 255, 255, 0.4)',
              borderRadius: '16px',
              transform: 'perspective(1000px) rotateX(2deg)',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'perspective(1000px) rotateX(0deg) scale(1.05)';
              e.currentTarget.style.boxShadow = `
                12px 12px 24px rgba(200, 140, 140, 0.4),
                -12px -12px 24px rgba(255, 220, 220, 0.9),
                inset 2px 2px 4px rgba(255, 255, 255, 0.6),
                inset -2px -2px 4px rgba(0, 0, 0, 0.1)
              `;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'perspective(1000px) rotateX(2deg)';
              e.currentTarget.style.boxShadow = `
                8px 8px 16px rgba(200, 140, 140, 0.3),
                -8px -8px 16px rgba(255, 220, 220, 0.8),
                inset 2px 2px 4px rgba(255, 255, 255, 0.5),
                inset -2px -2px 4px rgba(0, 0, 0, 0.08)
              `;
            }}
          >
            <svg className="w-12 h-12 text-black mb-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
            </svg>
            <h4 className="text-xl font-semibold mb-2">Advanced Analytics</h4>
            <p className="text-gray-600">Gain insights from detailed recruitment metrics and reports.</p>
          </div>
          <div 
            className="bg-[#f79c9c] p-6 rounded-lg transform hover:scale-105 transition-all duration-300"
            style={{
              background: '#f79c9c',
              boxShadow: `
                8px 8px 16px rgba(200, 140, 140, 0.3),
                -8px -8px 16px rgba(255, 220, 220, 0.8),
                inset 2px 2px 4px rgba(255, 255, 255, 0.5),
                inset -2px -2px 4px rgba(0, 0, 0, 0.08)
              `,
              border: '1px solid rgba(255, 255, 255, 0.4)',
              borderRadius: '16px',
              transform: 'perspective(1000px) rotateX(2deg)',
              transition: 'all 0.3s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'perspective(1000px) rotateX(0deg) scale(1.05)';
              e.currentTarget.style.boxShadow = `
                12px 12px 24px rgba(200, 140, 140, 0.4),
                -12px -12px 24px rgba(255, 220, 220, 0.9),
                inset 2px 2px 4px rgba(255, 255, 255, 0.6),
                inset -2px -2px 4px rgba(0, 0, 0, 0.1)
              `;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'perspective(1000px) rotateX(2deg)';
              e.currentTarget.style.boxShadow = `
                8px 8px 16px rgba(200, 140, 140, 0.3),
                -8px -8px 16px rgba(255, 220, 220, 0.8),
                inset 2px 2px 4px rgba(255, 255, 255, 0.5),
                inset -2px -2px 4px rgba(0, 0, 0, 0.08)
              `;
            }}
          >
            <svg className="w-12 h-12 text-black mb-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
            </svg>
            <h4 className="text-xl font-semibold mb-2">24/7 Availability</h4>
            <p className="text-gray-600">Screen and engage candidates around the clock.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;