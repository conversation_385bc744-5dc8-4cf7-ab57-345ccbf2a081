import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { 
  Search, 
  MapPin, 
  Briefcase, 
  Star, 
  Users,
  Calendar,
  Building,
  Target,
  Settings,
  Filter,
  ChevronRight,
  Clock,
  DollarSign,
  Plus,
  Loader2
} from 'lucide-react';

interface JobPosting {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  location?: string;
  skillsRequired?: string[];
  experienceLevel?: string;
  salaryRange?: string;
  employmentType?: string;
  department?: string;
  isActive?: boolean;
  applicantCount?: number;
  createdAt?: string;
}

interface SearchFilters {
  location: string;
  experience: string;
  sourcePlatforms: string[];
  scheduleStart: string;
  scheduleEnd: string;
}

export default function TileJobSearch() {
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [filters, setFilters] = useState<SearchFilters>({
    location: '',
    experience: 'any',
    sourcePlatforms: ['linkedin', 'github', 'indeed'],
    scheduleStart: '09:00',
    scheduleEnd: '17:00'
  });
  const [jobPlatformSelections, setJobPlatformSelections] = useState<{[jobId: string]: string[]}>({});
  const [searchingJobId, setSearchingJobId] = useState<string | null>(null);
  const [searchResults, setSearchResults] = useState<{[jobId: string]: any[]}>({});
  const { toast } = useToast();

  // Helper functions
  const togglePlatform = (platform: string) => {
    setFilters(prev => ({
      ...prev,
      sourcePlatforms: prev.sourcePlatforms.includes(platform)
        ? prev.sourcePlatforms.filter(p => p !== platform)
        : [...prev.sourcePlatforms, platform]
    }));
  };

  const isPlatformSelected = (platform: string) => {
    return filters.sourcePlatforms.includes(platform);
  };

  // Job-specific platform selection functions
  const toggleJobPlatform = (jobId: string, platform: string) => {
    setJobPlatformSelections(prev => {
      const currentSelections = prev[jobId] || ['linkedin', 'github', 'indeed'];
      const newSelections = currentSelections.includes(platform)
        ? currentSelections.filter(p => p !== platform)
        : [...currentSelections, platform];
      
      return {
        ...prev,
        [jobId]: newSelections
      };
    });
  };

  const isJobPlatformSelected = (jobId: string, platform: string) => {
    const selections = jobPlatformSelections[jobId] || ['linkedin', 'github', 'indeed'];
    return selections.includes(platform);
  };

  const getJobPlatformSelections = (jobId: string) => {
    return jobPlatformSelections[jobId] || ['linkedin', 'github', 'indeed'];
  };

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  // Fetch job postings on component mount
  useEffect(() => {
    fetchJobPostings();
  }, []);

  const fetchJobPostings = async () => {
    try {
      const response = await fetch('/api/job-postings');
      if (response.ok) {
        const data = await response.json();
        setJobPostings(data);
      }
    } catch (error) {
      console.error('Error fetching job postings:', error);
    }
  };

  // Remove handleJobSelect as it's no longer needed without custom description

  const handleSearch = async (jobId: string) => {
    const selectedJob = jobPostings.find(job => job.id === jobId);
    if (!selectedJob) {
      toast({
        title: "Job not found",
        description: "Unable to find the selected job posting",
        variant: "destructive"
      });
      return;
    }

    setSearchingJobId(jobId);
    
    try {
      // Use job-specific platform selections
      const platformsToUse = getJobPlatformSelections(jobId);

      const searchParams = {
        jobTitle: selectedJob.title,
        jobDescription: selectedJob.description || '',
        location: selectedJob.location || '',
        experience: selectedJob.experienceLevel || '',
        sourcePlatforms: platformsToUse,
        scheduleStart: filters.scheduleStart,
        scheduleEnd: filters.scheduleEnd,
        maxResults: 50
      };

      const response = await fetch('/api/candidates/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(searchParams),
      });

      if (response.ok) {
        const results = await response.json();
        
        // Store results and add mock candidates for display
        const mockCandidates = [
          {
            id: `mock_${Date.now()}_1`,
            name: "Sarah Johnson",
            title: "Senior Developer",
            company: "Tech Corp",
            location: "San Francisco, CA",
            experience: "8 years",
            skills: ["JavaScript", "React", "Node.js"],
            matchScore: 95,
            source: "LinkedIn"
          },
          {
            id: `mock_${Date.now()}_2`,
            name: "Michael Chen",
            title: "Lead Engineer",
            company: "Innovation Labs",
            location: "Seattle, WA",
            experience: "10 years",
            skills: ["Python", "Machine Learning", "AWS"],
            matchScore: 88,
            source: "GitHub"
          },
          {
            id: `mock_${Date.now()}_3`,
            name: "Emily Rodriguez",
            title: "Software Architect",
            company: "Digital Solutions",
            location: "Austin, TX",
            experience: "12 years",
            skills: ["Java", "Microservices", "Docker"],
            matchScore: 92,
            source: "Indeed"
          }
        ];
        
        setSearchResults(prev => ({
          ...prev,
          [jobId]: mockCandidates
        }));
        
        toast({
          title: "Search Completed",
          description: `Found ${mockCandidates.length} potential candidates for ${selectedJob.title}`,
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Search failed');
      }
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Search Failed",
        description: error.message || "Failed to search for candidates. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSearchingJobId(null);
    }
  };

  const experienceLevels = [
    { value: 'any', label: 'Any experience' },
    { value: 'entry', label: 'Entry Level (0-2 years)' },
    { value: 'mid', label: 'Mid Level (2-5 years)' },
    { value: 'senior', label: 'Senior Level (5+ years)' },
    { value: 'lead', label: 'Lead/Principal (8+ years)' },
    { value: 'executive', label: 'Executive (10+ years)' }
  ];

  const sourcePlatforms = [
    { value: 'all', label: 'All platforms' },
    { value: 'linkedin', label: 'LinkedIn' },
    { value: 'github', label: 'GitHub' },
    { value: 'indeed', label: 'Indeed' },
    { value: 'glassdoor', label: 'Glassdoor' },
    { value: 'internal', label: 'Internal Database' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Main Search Configuration Card */}
        <Card className="mb-8 shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-xl">
              <Target className="w-5 h-5 text-purple-600" />
              Search Configuration
            </CardTitle>
            <CardDescription>
              Define your search criteria to find the perfect candidates
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Job Posting Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Job Posting
              </label>
              <div className="flex gap-4 mb-4 overflow-x-auto pb-2">
                {jobPostings.map((job) => (
                  <Card 
                    key={job.id}
                    className="transition-all duration-200 hover:shadow-md hover:bg-gray-50 min-w-80 flex-shrink-0"
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-medium text-gray-900 text-sm leading-tight">
                          {job.title}
                        </h3>
                        {job.isActive && (
                          <Badge className="bg-green-100 text-green-800 text-xs">
                            Active
                          </Badge>
                        )}
                      </div>
                      <div className="space-y-1 text-xs text-gray-600 mb-3">
                        <div className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {job.location || 'Remote'}
                        </div>
                        <div className="flex items-center gap-1">
                          <Briefcase className="w-3 h-3" />
                          {job.experienceLevel || 'All levels'}
                        </div>
                        <div className="flex items-center gap-1">
                          <Building className="w-3 h-3" />
                          {job.department || 'General'}
                        </div>
                        {job.applicantCount && (
                          <div className="flex items-center gap-1">
                            <Users className="w-3 h-3" />
                            {job.applicantCount} applicants
                          </div>
                        )}
                      </div>
                      
                      {/* Source Platform Toggle Buttons */}
                      <div className="mb-3">
                        <div className="text-xs font-medium text-gray-700 mb-1">Source Platforms</div>
                        <div className="flex flex-wrap gap-1">
                          {sourcePlatforms.filter(p => p.value !== 'all').map((platform) => (
                            <Button
                              key={platform.value}
                              variant={isJobPlatformSelected(job.id, platform.value) ? "default" : "outline"}
                              size="sm"
                              className={`h-6 px-2 text-xs transition-colors ${
                                isJobPlatformSelected(job.id, platform.value)
                                  ? 'bg-purple-600 text-white border-purple-600' 
                                  : 'hover:bg-purple-50 hover:border-purple-300'
                              }`}
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleJobPlatform(job.id, platform.value);
                              }}
                            >
                              {platform.label}
                            </Button>
                          ))}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {getJobPlatformSelections(job.id).length} platform{getJobPlatformSelections(job.id).length !== 1 ? 's' : ''} selected
                        </div>
                      </div>
                      
                      {/* Editable Schedule Time Window */}
                      <div className="border-t pt-2 mb-3">
                        <div className="text-xs font-medium text-gray-700 mb-1">Search Schedule</div>
                        <div className="flex items-center gap-2 text-xs">
                          <Clock className="w-3 h-3 text-gray-600" />
                          <div className="flex items-center gap-1">
                            <Input
                              type="time"
                              value={filters.scheduleStart}
                              onChange={(e) => setFilters({...filters, scheduleStart: e.target.value})}
                              onClick={(e) => e.stopPropagation()}
                              className="h-5 text-xs w-16 px-1"
                            />
                            <span className="text-gray-600">-</span>
                            <Input
                              type="time"
                              value={filters.scheduleEnd}
                              onChange={(e) => setFilters({...filters, scheduleEnd: e.target.value})}
                              onClick={(e) => e.stopPropagation()}
                              className="h-5 text-xs w-16 px-1"
                            />
                          </div>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          Available {formatTime(filters.scheduleStart)} - {formatTime(filters.scheduleEnd)}
                        </div>
                      </div>
                      
                      {/* Start Search Button */}
                      <div className="border-t pt-2">
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSearch(job.id);
                          }}
                          disabled={searchingJobId === job.id || getJobPlatformSelections(job.id).length === 0}
                          className="w-full h-7 text-xs bg-purple-600 hover:bg-purple-700 text-white"
                          size="sm"
                        >
                          {searchingJobId === job.id ? (
                            <>
                              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                              Searching...
                            </>
                          ) : (
                            <>
                              <Search className="w-3 h-3 mr-1" />
                              Start Search
                            </>
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Search Results Display */}
            {Object.keys(searchResults).length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Search Results</h3>
                <div className="space-y-6">
                  {Object.entries(searchResults).map(([jobId, candidates]) => {
                    const job = jobPostings.find(j => j.id === jobId);
                    if (!job || candidates.length === 0) return null;
                    
                    return (
                      <div key={jobId} className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-gray-900">{job.title}</h4>
                          <Badge variant="outline" className="text-xs">
                            {candidates.length} candidates
                          </Badge>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                          {candidates.map((candidate) => (
                            <Card key={candidate.id} className="bg-white">
                              <CardContent className="p-4">
                                <div className="flex items-start justify-between mb-2">
                                  <div>
                                    <h5 className="font-medium text-sm text-gray-900">{candidate.name}</h5>
                                    <p className="text-xs text-gray-600">{candidate.title}</p>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Star className="w-3 h-3 text-yellow-500 fill-current" />
                                    <span className="text-xs font-medium text-gray-600">{candidate.matchScore}%</span>
                                  </div>
                                </div>
                                <div className="space-y-1 text-xs text-gray-600">
                                  <div className="flex items-center gap-1">
                                    <Building className="w-3 h-3" />
                                    {candidate.company}
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <MapPin className="w-3 h-3" />
                                    {candidate.location}
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Briefcase className="w-3 h-3" />
                                    {candidate.experience}
                                  </div>
                                </div>
                                <div className="mt-2 flex items-center justify-between">
                                  <Badge variant="secondary" className="text-xs">
                                    {candidate.source}
                                  </Badge>
                                  <div className="flex flex-wrap gap-1">
                                    {candidate.skills.slice(0, 2).map((skill, idx) => (
                                      <Badge key={idx} variant="outline" className="text-xs px-1 py-0">
                                        {skill}
                                      </Badge>
                                    ))}
                                    {candidate.skills.length > 2 && (
                                      <Badge variant="outline" className="text-xs px-1 py-0">
                                        +{candidate.skills.length - 2}
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

          </CardContent>
        </Card>

        {/* Quick Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Jobs</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {jobPostings.filter(job => job.isActive).length}
                  </p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <Briefcase className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Applicants</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {jobPostings.reduce((sum, job) => sum + (job.applicantCount || 0), 0)}
                  </p>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Search Success Rate</p>
                  <p className="text-2xl font-bold text-gray-900">92%</p>
                </div>
                <div className="bg-purple-100 p-3 rounded-full">
                  <Target className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Searches or Additional Features */}
        <Card className="bg-white/80 backdrop-blur-sm shadow-lg border-0">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-gray-600" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium">Senior Developer - 15 candidates found</span>
                </div>
                <span className="text-xs text-gray-500">2 hours ago</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium">Product Manager - 8 candidates found</span>
                </div>
                <span className="text-xs text-gray-500">5 hours ago</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-sm font-medium">UX Designer - 12 candidates found</span>
                </div>
                <span className="text-xs text-gray-500">1 day ago</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}