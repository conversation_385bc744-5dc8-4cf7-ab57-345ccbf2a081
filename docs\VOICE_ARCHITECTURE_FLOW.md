# Voice Call Architecture & Flow Documentation

## System Overview
This document explains the complete end-to-end flow of our AI-powered voice calling system that integrates Twilio for telephony and ElevenLabs for conversational AI.

## Architecture Components

```
┌─────────────────────────────────────────────────────────────────┐
│                         Replit Application                       │
├───────────────────────────────────────────────────────────────────┤
│                                                                   │
│  ┌──────────────┐    ┌──────────────┐    ┌──────────────┐      │
│  │   Frontend   │───▶│   Express    │───▶│  PostgreSQL  │      │
│  │   (React)    │    │   Backend    │    │   Database   │      │
│  └──────────────┘    └──────────────┘    └──────────────┘      │
│                              │                                   │
│                              ├──────────────────┐                │
│                              ▼                  ▼                │
│                    ┌──────────────┐    ┌──────────────┐        │
│                    │   WebSocket   │    │   TwiML      │        │
│                    │    Server     │    │   Endpoint   │        │
│                    │/elevenlabs-   │    │/api/voice-   │        │
│                    │    stream     │    │ providers/   │        │
│                    └──────────────┘    │    twiml     │        │
│                              │          └──────────────┘        │
└──────────────────────────────┼───────────────────┼──────────────┘
                               │                   │
                               │                   │
                    WebSocket  │                   │ HTTPS
                    Connection │                   │ Webhook
                               │                   │
                               ▼                   ▼
                    ┌──────────────────────────────────┐
                    │         Twilio Cloud             │
                    │  - Phone Number Management       │
                    │  - Call Routing                  │
                    │  - Media Streaming               │
                    │  - TwiML Processing              │
                    └──────────────────────────────────┘
                               │
                               │ WebSocket
                               │ (via Twilio)
                               ▼
                    ┌──────────────────────────────────┐
                    │     ElevenLabs Cloud             │
                    │  - Conversational AI Agent       │
                    │  - Voice Synthesis               │
                    │  - Natural Language Processing   │
                    └──────────────────────────────────┘
```

## Detailed Sequence Flow

### Phase 1: Call Initiation

```
User clicks "Call" button in UI
    │
    ▼
Frontend sends POST request
/api/voice-providers/initiate/twilio/{candidateId}
    │
    ▼
Backend retrieves:
- Candidate information
- Job details
- Organization context
    │
    ▼
Backend calls Twilio API:
twilioClient.calls.create({
  to: candidatePhoneNumber,
  from: twilioPhoneNumber,
  url: callbackUrl (TwiML endpoint)
})
    │
    ▼
Twilio initiates phone call
```

### Phase 2: Call Connection & TwiML Request

```
Candidate answers phone
    │
    ▼
Twilio requests TwiML instructions
GET/POST to: /api/voice-providers/twiml/{callId}
    │
    ▼
Backend generates TwiML response:
<Response>
  <Connect>
    <Stream url="wss://[domain]/elevenlabs-stream">
      <Parameter name="agent_id" value="[agent_id]" />
      <Parameter name="candidate_name" value="[name]" />
      <Parameter name="job_title" value="[title]" />
    </Stream>
  </Connect>
  <Say>Connection failed message (fallback)</Say>
</Response>
    │
    ▼
Twilio processes TwiML
```

### Phase 3: WebSocket Connection Establishment

```
Twilio initiates WebSocket connection
    │
    ▼
WebSocket handshake:
1. Twilio → Replit: Connection request to /elevenlabs-stream
2. Replit: verifyClient() validates connection
3. Replit → Twilio: HTTP 101 Switching Protocols
4. WebSocket connection established
    │
    ▼
Twilio sends initial messages:
- event: "connected"
- event: "start" (with stream metadata)
```

### Phase 4: ElevenLabs AI Connection

```
Replit receives "start" event from Twilio
    │
    ▼
Replit connects to ElevenLabs:
1. Requests signed WebSocket URL from ElevenLabs API
2. Opens WebSocket to ElevenLabs conversational AI
3. Sends conversation configuration with context
    │
    ▼
Audio streaming begins:
- Twilio → Replit: Candidate's speech (μ-law encoded)
- Replit → ElevenLabs: Forward audio chunks
- ElevenLabs → Replit: AI agent's response audio
- Replit → Twilio: Forward AI audio to candidate
```

### Phase 5: Real-time Conversation

```
┌──────────┐     Audio     ┌──────────┐    WebSocket    ┌──────────┐
│Candidate │──────────────▶│  Twilio  │────────────────▶│  Replit  │
│  Phone   │               │  Cloud   │                 │   App    │
└──────────┘               └──────────┘                 └──────────┘
     ▲                          ▲                            │
     │                          │                            │
     │                          │                            ▼
     │                          │                     ┌──────────┐
     │                          │                     │ElevenLabs│
     │                          └─────────────────────│    AI    │
     │                         AI Response Audio      └──────────┘
     └─────────────────────────────────────────────────────┘
```

## Message Flow Details

### 1. Twilio → Replit Messages

```json
// Connection established
{
  "event": "connected",
  "protocol": "Call",
  "version": "1.0.0"
}

// Stream started
{
  "event": "start",
  "sequenceNumber": "1",
  "start": {
    "streamSid": "MZ...",
    "accountSid": "AC...",
    "callSid": "CA...",
    "customParameters": {
      "agent_id": "agent_8901...",
      "candidate_name": "John Doe",
      "job_title": "Senior Developer"
    }
  }
}

// Audio data (continuous)
{
  "event": "media",
  "sequenceNumber": "2",
  "media": {
    "track": "inbound",
    "chunk": "1",
    "timestamp": "5",
    "payload": "[base64 μ-law audio]"
  }
}
```

### 2. Replit → ElevenLabs Messages

```json
// Initial configuration
{
  "type": "conversation_initiation_client_data",
  "conversation_initiation_client_data": {
    "conversation_config_override": {
      "agent": {
        "prompt": {
          "prompt": "You are Sarah, an HR recruiter..."
        }
      }
    }
  }
}

// Audio forwarding
{
  "type": "audio",
  "audio": {
    "chunk": "[base64 audio from Twilio]",
    "encoding": "pcm_mulaw",
    "sample_rate": 8000
  }
}
```

### 3. ElevenLabs → Replit Messages

```json
// Audio response
{
  "type": "audio",
  "audio_event": {
    "audio_base_64": "[base64 audio]",
    "sample_rate": 8000,
    "codec": "pcm_mulaw"
  }
}

// Conversation events
{
  "type": "interruption",
  "interruption_event": {
    "event_id": "...",
    "timestamp": 1234567890
  }
}
```

### 4. Replit → Twilio Messages

```json
// Audio to candidate
{
  "event": "media",
  "streamSid": "MZ...",
  "media": {
    "payload": "[base64 audio from ElevenLabs]"
  }
}

// Control messages
{
  "event": "clear",
  "streamSid": "MZ..."
}
```

## Key Technical Details

### WebSocket URLs
- **Development**: `ws://localhost:5000/elevenlabs-stream`
- **Production**: `wss://[replit-domain]/elevenlabs-stream`

### Audio Encoding
- **Format**: μ-law (G.711)
- **Sample Rate**: 8000 Hz
- **Encoding**: Base64
- **Direction**: Bidirectional streaming

### Authentication
- **Twilio**: Account SID + Auth Token
- **ElevenLabs**: API Key + Agent ID
- **WebSocket**: No authentication (relies on Twilio signature)

### Error Handling

1. **WebSocket Connection Failure**
   - Twilio Error 31920: WebSocket handshake error
   - Solution: Ensure proper HTTP 101 response
   - Fallback: TwiML `<Say>` message

2. **ElevenLabs Connection Failure**
   - Graceful degradation to TwiML responses
   - Error logged but call continues

3. **Audio Stream Interruption**
   - Automatic reconnection attempt
   - Buffer management for smooth playback

## Security Considerations

1. **URL Security**
   - Use HTTPS/WSS in production
   - Validate Twilio signatures
   - Secure storage of API keys

2. **Data Privacy**
   - No audio recording without consent
   - Secure transmission of call metadata
   - GDPR compliance for EU candidates

3. **Rate Limiting**
   - Twilio rate limits apply
   - ElevenLabs API quotas
   - WebSocket connection limits

## Monitoring & Debugging

### Key Log Points

1. **Call Initiation**
   ```
   Attempting Twilio call from +1... to +1...
   Twilio call created successfully: CA...
   ```

2. **TwiML Generation**
   ```
   Interactive TwiML request for call [callId]
   Generated TwiML for ElevenLabs streaming
   ```

3. **WebSocket Connection**
   ```
   WebSocket connection attempt from Twilio
   WebSocket connection established
   Connected to ElevenLabs Conversational AI
   ```

4. **Audio Streaming**
   ```
   Forwarding audio: Twilio → ElevenLabs
   Forwarding audio: ElevenLabs → Twilio
   ```

### Common Issues & Solutions

| Issue | Symptom | Solution |
|-------|---------|----------|
| Error 31920 | WebSocket handshake fails | Check URL format, remove port numbers |
| No audio | Silent call | Verify audio encoding/format |
| Delayed responses | High latency | Check network, reduce processing |
| Dropped calls | Unexpected hangup | Monitor WebSocket stability |

## Testing Endpoints

1. **Manual Testing**
   - `/test-websocket-client` - Browser WebSocket test
   - `/api-docs` - Swagger documentation
   - `/api/voice-providers/test` - API health check

2. **Call Testing**
   ```bash
   curl -X POST http://localhost:5000/api/voice-providers/initiate/twilio/{candidateId} \
     -H "Content-Type: application/json" \
     -d '{"phoneNumber": "+***********"}'
   ```

## Environment Variables Required

```env
# Twilio Configuration
TWILIO_ACCOUNT_SID=AC...
TWILIO_AUTH_TOKEN=...
TWILIO_PHONE_NUMBER=+***********

# ElevenLabs Configuration
ELEVENLABS_API_KEY=...
ELEVENLABS_AGENT_ID=agent_8901...

# Database
DATABASE_URL=postgresql://...
```

## Performance Metrics

- **Call Setup Time**: ~2-3 seconds
- **WebSocket Latency**: <100ms
- **Audio Latency**: <500ms end-to-end
- **Concurrent Calls**: Limited by Twilio account
- **Success Rate**: Target >95%

## Future Enhancements

1. **Call Recording**: Store conversations for quality assurance
2. **Multi-language Support**: Detect and respond in candidate's language
3. **Call Analytics**: Track conversation metrics and outcomes
4. **Failover System**: Multiple AI providers for redundancy
5. **Call Scheduling**: Automated calling at preferred times

---

This architecture ensures a seamless, real-time conversational experience between candidates and our AI recruiter, leveraging the best of Twilio's telephony infrastructure and ElevenLabs' advanced conversational AI capabilities.