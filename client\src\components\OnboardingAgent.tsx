
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle, Clock, UserPlus, FileText, Calendar, Settings, Mail, Users, Building } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const OnboardingAgent = () => {
  const [selectedCandidate, setSelectedCandidate] = useState<any>(null);
  const [onboardingProgress, setOnboardingProgress] = useState(0);
  const [workdayStatus, setWorkdayStatus] = useState('pending');
  const { toast } = useToast();

  const acceptedCandidates = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      position: "Senior Frontend Developer",
      startDate: "2024-01-15",
      department: "Engineering",
      manager: "<PERSON>",
      salary: "$120,000",
      status: "offer_accepted"
    },
    {
      id: 2,
      name: "Emily Zhang",
      email: "<EMAIL>",
      position: "Product Designer",
      startDate: "2024-01-22",
      department: "Design",
      manager: "Mike Johnson",
      salary: "$95,000",
      status: "offer_accepted"
    }
  ];

  const onboardingTasks = [
    { id: 1, title: "Create Workday Employee Record", status: "pending", category: "workday" },
    { id: 2, title: "Generate Employee ID & Login", status: "pending", category: "workday" },
    { id: 3, title: "Setup Payroll Information", status: "pending", category: "workday" },
    { id: 4, title: "Assign Learning Modules", status: "pending", category: "workday" },
    { id: 5, title: "Send Welcome Email Package", status: "pending", category: "communication" },
    { id: 6, title: "Schedule First Day Orientation", status: "pending", category: "scheduling" },
    { id: 7, title: "Setup Equipment Request", status: "pending", category: "equipment" },
    { id: 8, title: "Create Team Introduction", status: "pending", category: "communication" }
  ];

  const [tasks, setTasks] = useState(onboardingTasks);

  const startOnboarding = async (candidate: any) => {
    setSelectedCandidate(candidate);
    setWorkdayStatus('processing');
    setOnboardingProgress(0);
    
    console.log("Starting onboarding for:", candidate.name);
    
    toast({
      title: "Onboarding initiated",
      description: `Starting automated onboarding for ${candidate.name}`,
    });

    // Simulate Workday integration steps
    const workdaySteps = [
      "Creating employee record in Workday...",
      "Generating employee ID and credentials...",
      "Setting up payroll and benefits...",
      "Assigning learning modules...",
      "Sending welcome communications..."
    ];

    for (let i = 0; i < workdaySteps.length; i++) {
      setTimeout(() => {
        console.log(workdaySteps[i]);
        setOnboardingProgress((i + 1) * 20);
        
        // Update task status
        setTasks(prevTasks => 
          prevTasks.map(task => 
            task.id <= i + 1 ? { ...task, status: 'completed' } : task
          )
        );
        
        if (i === workdaySteps.length - 1) {
          setWorkdayStatus('completed');
          toast({
            title: "Workday setup complete",
            description: "Employee record created and systems configured",
          });
        }
      }, (i + 1) * 1500);
    }
  };

  const sendWelcomeEmail = () => {
    console.log("Sending welcome email to:", selectedCandidate?.name);
    
    setTimeout(() => {
      setTasks(prevTasks => 
        prevTasks.map(task => 
          task.category === 'communication' ? { ...task, status: 'completed' } : task
        )
      );
      setOnboardingProgress(75);
      
      toast({
        title: "Welcome email sent",
        description: "Comprehensive welcome package delivered to candidate",
      });
    }, 1000);
  };

  const scheduleOrientation = () => {
    console.log("Scheduling orientation for:", selectedCandidate?.name);
    
    setTimeout(() => {
      setTasks(prevTasks => 
        prevTasks.map(task => 
          task.category === 'scheduling' ? { ...task, status: 'completed' } : task
        )
      );
      setOnboardingProgress(90);
      
      toast({
        title: "Orientation scheduled",
        description: "First day orientation booked with team introductions",
      });
    }, 1000);
  };

  const completeOnboarding = () => {
    setTasks(prevTasks => 
      prevTasks.map(task => ({ ...task, status: 'completed' }))
    );
    setOnboardingProgress(100);
    
    toast({
      title: "Onboarding completed",
      description: `${selectedCandidate?.name} is fully onboarded and ready for their first day!`,
    });
  };

  const getTaskIcon = (category: string) => {
    switch (category) {
      case 'workday': return <Building className="w-4 h-4" />;
      case 'communication': return <Mail className="w-4 h-4" />;
      case 'scheduling': return <Calendar className="w-4 h-4" />;
      case 'equipment': return <Settings className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Accepted Candidates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <UserPlus className="w-5 h-5 mr-2" />
            Accepted Candidates - Ready for Onboarding
          </CardTitle>
          <CardDescription>
            Candidates who accepted offers and need automated onboarding setup
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {acceptedCandidates.map((candidate) => (
              <Card 
                key={candidate.id} 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedCandidate?.id === candidate.id ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => setSelectedCandidate(candidate)}
              >
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h3 className="font-semibold">{candidate.name}</h3>
                      <p className="text-sm text-gray-600">{candidate.position}</p>
                      <p className="text-xs text-gray-500">{candidate.department}</p>
                    </div>
                    <Badge className="bg-green-100 text-green-800">
                      Offer Accepted
                    </Badge>
                  </div>
                  
                  <div className="space-y-1 text-sm text-gray-600">
                    <p><strong>Start Date:</strong> {candidate.startDate}</p>
                    <p><strong>Manager:</strong> {candidate.manager}</p>
                    <p><strong>Salary:</strong> {candidate.salary}</p>
                  </div>
                  
                  {selectedCandidate?.id !== candidate.id && (
                    <Button 
                      onClick={(e) => {
                        e.stopPropagation();
                        startOnboarding(candidate);
                      }}
                      size="sm" 
                      className="w-full mt-3"
                    >
                      <UserPlus className="w-4 h-4 mr-2" />
                      Start Onboarding
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Onboarding Workflow */}
      {selectedCandidate && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <UserPlus className="w-5 h-5 mr-2" />
                Onboarding Workflow - {selectedCandidate.name}
              </span>
              <Badge variant={workdayStatus === 'completed' ? 'default' : 'secondary'}>
                {workdayStatus === 'completed' ? 'Ready' : 'Processing'}
              </Badge>
            </CardTitle>
            <CardDescription>
              Automated onboarding with Workday integration and task management
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="progress" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="progress">Progress</TabsTrigger>
                <TabsTrigger value="workday">Workday Setup</TabsTrigger>
                <TabsTrigger value="communications">Communications</TabsTrigger>
              </TabsList>

              <TabsContent value="progress" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">Overall Progress</span>
                      <span className="text-sm text-gray-500">{onboardingProgress}%</span>
                    </div>
                    <Progress value={onboardingProgress} className="h-2" />
                  </div>

                  <div className="space-y-3">
                    {tasks.map((task) => (
                      <div key={task.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                          task.status === 'completed' 
                            ? 'bg-green-100 text-green-600' 
                            : 'bg-gray-100 text-gray-400'
                        }`}>
                          {task.status === 'completed' ? (
                            <CheckCircle className="w-4 h-4" />
                          ) : (
                            <Clock className="w-4 h-4" />
                          )}
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            {getTaskIcon(task.category)}
                            <span className={`text-sm ${
                              task.status === 'completed' ? 'line-through text-gray-500' : ''
                            }`}>
                              {task.title}
                            </span>
                          </div>
                        </div>
                        
                        <Badge 
                          variant={task.status === 'completed' ? 'default' : 'outline'}
                          className="text-xs"
                        >
                          {task.status === 'completed' ? 'Done' : 'Pending'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="workday" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <Building className="w-5 h-5 mr-2" />
                      Workday Integration Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium">Employee ID</label>
                        <Input 
                          value={workdayStatus === 'completed' ? 'EMP-2024-001' : 'Generating...'} 
                          disabled 
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium">Workday Login</label>
                        <Input 
                          value={workdayStatus === 'completed' ? selectedCandidate.email : 'Creating...'} 
                          disabled 
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Workday Setup Tasks</h4>
                      <div className="space-y-2">
                        {[
                          "Employee record created",
                          "Payroll information configured", 
                          "Benefits enrollment initiated",
                          "Learning modules assigned",
                          "Manager access granted"
                        ].map((step, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <CheckCircle className={`w-4 h-4 ${
                              workdayStatus === 'completed' ? 'text-green-600' : 'text-gray-400'
                            }`} />
                            <span className="text-sm">{step}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="communications" className="space-y-4">
                <div className="space-y-4">
                  <Card>
                    <CardContent className="p-4">
                      <h4 className="font-medium mb-2 flex items-center">
                        <Mail className="w-4 h-4 mr-2" />
                        Welcome Email Package
                      </h4>
                      <p className="text-sm text-gray-600 mb-3">
                        Comprehensive welcome email with company handbook, first-day instructions, and team introductions
                      </p>
                      <Button onClick={sendWelcomeEmail} size="sm" className="w-full">
                        Send Welcome Email
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <h4 className="font-medium mb-2 flex items-center">
                        <Calendar className="w-4 h-4 mr-2" />
                        First Day Orientation
                      </h4>
                      <p className="text-sm text-gray-600 mb-3">
                        Schedule orientation session and team introductions for {selectedCandidate.startDate}
                      </p>
                      <Button onClick={scheduleOrientation} size="sm" className="w-full">
                        Schedule Orientation
                      </Button>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <h4 className="font-medium mb-2 flex items-center">
                        <Users className="w-4 h-4 mr-2" />
                        Team Notifications
                      </h4>
                      <p className="text-sm text-gray-600 mb-3">
                        Notify team members and stakeholders about the new hire
                      </p>
                      <div className="space-y-2">
                        <Button variant="outline" size="sm" className="w-full">
                          Notify Direct Manager
                        </Button>
                        <Button variant="outline" size="sm" className="w-full">
                          Announce to Team
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>

            {onboardingProgress < 100 && onboardingProgress > 0 && (
              <div className="mt-6 pt-4 border-t">
                <Button onClick={completeOnboarding} className="w-full">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Complete Onboarding Process
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default OnboardingAgent;
