# Gmail Scope Re-authorization Required

## Issue
The Gmail API is returning a 403 "insufficient authentication scopes" error when trying to send emails. This indicates that the current OAuth token doesn't have the necessary permissions to send emails.

## Solution
The Gmail service needs to be re-authorized with the correct scopes:

### Required Scopes
- `https://www.googleapis.com/auth/gmail.readonly` - Read emails
- `https://www.googleapis.com/auth/gmail.send` - Send emails
- `https://www.googleapis.com/auth/gmail.compose` - Compose emails
- `https://www.googleapis.com/auth/gmail.modify` - Modify emails

### Re-authorization Steps

1. **Delete the existing token file**:
   ```bash
   rm token.json
   ```

2. **Get new authorization URL**:
   Visit: `/api/gmail/auth` endpoint to get the new auth URL with correct scopes

3. **Complete OAuth flow**:
   - Visit the returned authorization URL
   - Grant permissions for all required scopes
   - Copy the authorization code
   - Submit it to `/api/gmail/callback?code=YOUR_CODE`

4. **Verify new permissions**:
   - Check `/api/gmail/status` to confirm authentication
   - Test email sending functionality

### Alternative: Manual Token Refresh
If you have the refresh token, you can manually refresh with new scopes:

```javascript
// In Gmail service
await this.auth.refreshAccessToken();
```

## Current Status
- ✅ Updated Gmail service with correct scopes
- ✅ Modified auth URL generation
- ⚠️ **Re-authorization required** - existing token has insufficient scopes
- ⚠️ Email sending will fail until re-authorization is complete

## Next Steps
1. Delete existing token.json
2. Re-authorize Gmail with new scopes
3. Test email sending functionality